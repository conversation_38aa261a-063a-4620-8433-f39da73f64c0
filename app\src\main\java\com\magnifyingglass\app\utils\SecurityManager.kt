package com.magnifyingglass.app.utils

import android.content.Context
import android.content.pm.PackageManager
import android.content.pm.Signature
import android.util.Log
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

/**
 * 应用安全管理器
 * 处理签名验证、完整性检查等安全功能
 */
class SecurityManager(private val context: Context) {
    
    companion object {
        private const val TAG = "SecurityManager"
        
        // 预期的签名SHA-256哈希值（发布时需要更新）
        private const val EXPECTED_SIGNATURE_HASH = "YOUR_RELEASE_SIGNATURE_HASH_HERE"
        
        // 调试签名哈希值
        private const val DEBUG_SIGNATURE_HASH = "A40DA80A59D170CAA950CF15C18C454D47A39B26989D8B640ECD745BA71BF5DC"
    }
    
    /**
     * 验证应用签名
     */
    fun verifyAppSignature(): Boolean {
        return try {
            val signatures = getAppSignatures()
            if (signatures.isEmpty()) {
                Log.w(TAG, "未找到应用签名")
                return false
            }
            
            val currentHash = getSignatureHash(signatures[0])
            Log.d(TAG, "当前签名哈希: $currentHash")
            
            // 检查是否为调试签名或发布签名
            val isValid = currentHash == DEBUG_SIGNATURE_HASH || currentHash == EXPECTED_SIGNATURE_HASH
            
            if (isValid) {
                Log.d(TAG, "应用签名验证成功")
            } else {
                Log.w(TAG, "应用签名验证失败")
            }
            
            isValid
        } catch (e: Exception) {
            Log.e(TAG, "签名验证异常", e)
            false
        }
    }
    
    /**
     * 获取应用签名
     */
    private fun getAppSignatures(): Array<Signature> {
        return try {
            val packageInfo = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                context.packageManager.getPackageInfo(
                    context.packageName,
                    PackageManager.GET_SIGNING_CERTIFICATES
                )
            } else {
                @Suppress("DEPRECATION")
                context.packageManager.getPackageInfo(
                    context.packageName,
                    PackageManager.GET_SIGNATURES
                )
            }
            
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                packageInfo.signingInfo?.apkContentsSigners ?: emptyArray()
            } else {
                @Suppress("DEPRECATION")
                packageInfo.signatures ?: emptyArray()
            }
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "获取包信息失败", e)
            emptyArray()
        }
    }
    
    /**
     * 计算签名的SHA-256哈希值
     */
    private fun getSignatureHash(signature: Signature): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(signature.toByteArray())
            hashBytes.joinToString("") { "%02X".format(it) }
        } catch (e: NoSuchAlgorithmException) {
            Log.e(TAG, "SHA-256算法不可用", e)
            ""
        }
    }
    
    /**
     * 检查应用完整性
     */
    fun checkAppIntegrity(): Boolean {
        return try {
            // 1. 验证签名
            val signatureValid = verifyAppSignature()
            
            // 2. 检查包名
            val packageNameValid = context.packageName == "com.magnifyingglass.app"
            
            // 3. 检查调试状态
            val isDebuggable = (context.applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
            
            Log.d(TAG, "完整性检查 - 签名: $signatureValid, 包名: $packageNameValid, 调试: $isDebuggable")
            
            signatureValid && packageNameValid
        } catch (e: Exception) {
            Log.e(TAG, "完整性检查异常", e)
            false
        }
    }
    
    /**
     * 获取应用版本信息
     */
    fun getAppVersionInfo(): AppVersionInfo {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            AppVersionInfo(
                versionName = packageInfo.versionName ?: "未知",
                versionCode = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    packageInfo.longVersionCode
                } else {
                    @Suppress("DEPRECATION")
                    packageInfo.versionCode.toLong()
                },
                packageName = packageInfo.packageName,
                firstInstallTime = packageInfo.firstInstallTime,
                lastUpdateTime = packageInfo.lastUpdateTime
            )
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "获取版本信息失败", e)
            AppVersionInfo("未知", 0, "未知", 0, 0)
        }
    }
    
    /**
     * 检查是否为调试版本
     */
    fun isDebugBuild(): Boolean {
        return (context.applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
    }
    
    /**
     * 获取当前签名哈希（用于开发时获取正确的哈希值）
     */
    fun getCurrentSignatureHash(): String {
        val signatures = getAppSignatures()
        return if (signatures.isNotEmpty()) {
            getSignatureHash(signatures[0])
        } else {
            ""
        }
    }
    
    /**
     * 应用版本信息数据类
     */
    data class AppVersionInfo(
        val versionName: String,
        val versionCode: Long,
        val packageName: String,
        val firstInstallTime: Long,
        val lastUpdateTime: Long
    )
}
