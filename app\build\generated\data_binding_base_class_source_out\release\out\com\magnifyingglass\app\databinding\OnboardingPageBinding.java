// Generated by view binder compiler. Do not edit!
package com.magnifyingglass.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.magnifyingglass.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class OnboardingPageBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView descriptionTextView;

  @NonNull
  public final ImageView iconImageView;

  @NonNull
  public final TextView titleTextView;

  private OnboardingPageBinding(@NonNull LinearLayout rootView,
      @NonNull TextView descriptionTextView, @NonNull ImageView iconImageView,
      @NonNull TextView titleTextView) {
    this.rootView = rootView;
    this.descriptionTextView = descriptionTextView;
    this.iconImageView = iconImageView;
    this.titleTextView = titleTextView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static OnboardingPageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static OnboardingPageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.onboarding_page, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static OnboardingPageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.descriptionTextView;
      TextView descriptionTextView = ViewBindings.findChildViewById(rootView, id);
      if (descriptionTextView == null) {
        break missingId;
      }

      id = R.id.iconImageView;
      ImageView iconImageView = ViewBindings.findChildViewById(rootView, id);
      if (iconImageView == null) {
        break missingId;
      }

      id = R.id.titleTextView;
      TextView titleTextView = ViewBindings.findChildViewById(rootView, id);
      if (titleTextView == null) {
        break missingId;
      }

      return new OnboardingPageBinding((LinearLayout) rootView, descriptionTextView, iconImageView,
          titleTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
