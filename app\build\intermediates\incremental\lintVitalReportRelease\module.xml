<lint-module
    format="1"
    dir="C:\Magnifying Glass\app"
    name=":app"
    type="APP"
    maven="MagnifyingGlass:app:"
    agpVersion="8.1.2"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\33.0.1\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
