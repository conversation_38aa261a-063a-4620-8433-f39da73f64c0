package com.magnifyingglass.app

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.magnifyingglass.app.databinding.ActivitySplashBinding
import com.magnifyingglass.app.utils.PrivacyPolicyManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class SplashActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySplashBinding
    private lateinit var privacyPolicyManager: PrivacyPolicyManager

    companion object {
        private const val SPLASH_DELAY = 2000L // 2秒启动画面
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 初始化隐私政策管理器
        privacyPolicyManager = PrivacyPolicyManager(this)

        // 设置全屏
        window.setFlags(
            android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN,
            android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN
        )

        // 启动动画
        startSplashAnimation()

        // 延迟检查隐私政策
        lifecycleScope.launch {
            delay(SPLASH_DELAY)
            checkPrivacyPolicyAndNavigate()
        }
    }
    
    private fun startSplashAnimation() {
        // 图标淡入动画
        binding.splashIcon.alpha = 0f
        binding.splashIcon.animate()
            .alpha(1f)
            .setDuration(800)
            .start()
        
        // 标题延迟淡入
        binding.splashTitle.alpha = 0f
        binding.splashTitle.animate()
            .alpha(1f)
            .setDuration(600)
            .setStartDelay(400)
            .start()
        
        // 副标题延迟淡入
        binding.splashSubtitle.alpha = 0f
        binding.splashSubtitle.animate()
            .alpha(1f)
            .setDuration(600)
            .setStartDelay(800)
            .start()
    }
    
    /**
     * 检查隐私政策并导航
     */
    private fun checkPrivacyPolicyAndNavigate() {
        if (privacyPolicyManager.shouldShowPrivacyPolicy()) {
            // 需要显示隐私政策
            privacyPolicyManager.showPrivacyPolicyDialog(
                activity = this,
                onAccepted = {
                    navigateToMain()
                },
                onRejected = {
                    // 用户拒绝隐私政策，退出应用
                    finish()
                }
            )
        } else {
            // 已同意隐私政策，直接进入主界面
            navigateToMain()
        }
    }

    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()

        // 添加过渡动画
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
    }
    
    override fun onBackPressed() {
        // 启动画面期间禁用返回键
    }
}
