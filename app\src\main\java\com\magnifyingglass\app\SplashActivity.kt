package com.magnifyingglass.app

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.magnifyingglass.app.databinding.ActivitySplashBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class SplashActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivitySplashBinding
    
    companion object {
        private const val SPLASH_DELAY = 2000L // 2秒启动画面
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 设置全屏
        window.setFlags(
            android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN,
            android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        // 启动动画
        startSplashAnimation()
        
        // 延迟跳转到主界面
        lifecycleScope.launch {
            delay(SPLASH_DELAY)
            navigateToMain()
        }
    }
    
    private fun startSplashAnimation() {
        // 图标淡入动画
        binding.splashIcon.alpha = 0f
        binding.splashIcon.animate()
            .alpha(1f)
            .setDuration(800)
            .start()
        
        // 标题延迟淡入
        binding.splashTitle.alpha = 0f
        binding.splashTitle.animate()
            .alpha(1f)
            .setDuration(600)
            .setStartDelay(400)
            .start()
        
        // 副标题延迟淡入
        binding.splashSubtitle.alpha = 0f
        binding.splashSubtitle.animate()
            .alpha(1f)
            .setDuration(600)
            .setStartDelay(800)
            .start()
    }
    
    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
        
        // 添加过渡动画
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
    }
    
    override fun onBackPressed() {
        // 启动画面期间禁用返回键
    }
}
