package com.magnifyingglass.app.utils

import android.app.Activity
import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.appcompat.app.AlertDialog

/**
 * 隐私政策管理器
 * 符合华为应用市场审核要求
 */
class PrivacyPolicyManager(private val context: Context) {
    
    companion object {
        private const val TAG = "PrivacyPolicyManager"
        private const val PREFS_NAME = "privacy_prefs"
        private const val KEY_PRIVACY_ACCEPTED = "privacy_accepted"
        private const val KEY_PRIVACY_VERSION = "privacy_version"
        private const val CURRENT_PRIVACY_VERSION = "1.0"
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    /**
     * 检查是否需要显示隐私政策
     */
    fun shouldShowPrivacyPolicy(): Boolean {
        val accepted = prefs.getBoolean(KEY_PRIVACY_ACCEPTED, false)
        val version = prefs.getString(KEY_PRIVACY_VERSION, "")
        
        return !accepted || version != CURRENT_PRIVACY_VERSION
    }
    
    /**
     * 显示隐私政策对话框
     */
    fun showPrivacyPolicyDialog(activity: Activity, onAccepted: () -> Unit, onRejected: () -> Unit) {
        Log.d(TAG, "显示隐私政策对话框")
        
        val privacyText = getPrivacyPolicyText()
        
        val dialog = AlertDialog.Builder(activity)
            .setTitle("隐私政策与用户协议")
            .setMessage(privacyText)
            .setCancelable(false)
            .setPositiveButton("同意并继续") { _, _ ->
                acceptPrivacyPolicy()
                onAccepted()
            }
            .setNegativeButton("不同意") { _, _ ->
                onRejected()
            }
            .create()

        dialog.show()

        // 确保按钮可见
        dialog.getButton(AlertDialog.BUTTON_POSITIVE)?.text = "同意并继续"
        dialog.getButton(AlertDialog.BUTTON_NEGATIVE)?.text = "不同意"
    }
    
    /**
     * 用户同意隐私政策
     */
    private fun acceptPrivacyPolicy() {
        prefs.edit()
            .putBoolean(KEY_PRIVACY_ACCEPTED, true)
            .putString(KEY_PRIVACY_VERSION, CURRENT_PRIVACY_VERSION)
            .apply()
        
        Log.d(TAG, "用户已同意隐私政策")
    }
    
    /**
     * 获取隐私政策文本
     */
    private fun getPrivacyPolicyText(): String {
        return """
欢迎使用智能放大镜应用！

为了向您提供更好的服务，我们需要获取以下权限：

📷 相机权限
• 用途：实现放大镜功能，显示实时画面
• 说明：仅在应用内使用，不会上传或分享

💾 存储权限  
• 用途：保存您拍摄的放大图像
• 说明：仅保存到本地相册，您可随时删除

📱 设备信息
• 用途：优化应用性能和兼容性
• 说明：仅用于技术优化，不涉及个人身份

🔒 隐私承诺：
• 我们不会收集您的个人身份信息
• 所有数据仅在本地处理，不会上传到服务器
• 您可以随时在设置中撤销权限
• 我们严格遵守相关法律法规

点击"同意并继续"即表示您已阅读并同意本隐私政策。

如有疑问，请联系我们：<EMAIL>
        """.trimIndent()
    }
    
    /**
     * 显示权限申请说明
     */
    fun showPermissionRationale(activity: Activity, permissionType: String, onContinue: () -> Unit) {
        val (title, message) = when (permissionType) {
            "camera" -> Pair(
                "相机权限申请",
                "智能放大镜需要使用相机权限来：\n\n• 显示实时放大画面\n• 提供清晰的视觉辅助\n• 实现冻结和拍照功能\n\n请点击允许以继续使用。"
            )
            "storage" -> Pair(
                "存储权限申请",
                "智能放大镜需要存储权限来：\n\n• 保存您拍摄的图像\n• 存储到相册供后续查看\n• 导出增强后的图片\n\n请点击允许以使用保存功能。"
            )
            else -> Pair(
                "权限申请",
                "为了提供完整的功能体验，我们需要申请相应权限。所有权限仅用于应用功能，不会用于其他目的。"
            )
        }

        AlertDialog.Builder(activity)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("继续申请") { _, _ ->
                onContinue()
            }
            .setNegativeButton("暂不授权", null)
            .show()
    }
}
