package com.magnifyingglass.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream

/**
 * 真正的Real-ESRGAN处理器
 * 使用NCNN框架和预训练的轻量版模型
 * 
 * 支持的轻量版模型：
 * 1. RealESRGAN_x4plus_anime_6B - 动漫图像专用（6MB）
 * 2. realesr-general-x4v3 - 通用场景超小模型（约4MB）
 */
class TrueRealESRGANProcessor(private val context: Context) {
    
    companion object {
        private const val TAG = "TrueRealESRGAN"
        
        // 模型文件名
        private const val ANIME_MODEL_PARAM = "RealESRGAN_x4plus_anime_6B.param"
        private const val ANIME_MODEL_BIN = "RealESRGAN_x4plus_anime_6B.bin"
        private const val GENERAL_MODEL_PARAM = "realesr-general-x4v3.param"
        private const val GENERAL_MODEL_BIN = "realesr-general-x4v3.bin"
        
        // 模型下载链接
        private const val ANIME_MODEL_URL = "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x4plus_anime_6B.pth"
        private const val GENERAL_MODEL_URL = "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.3.0/realesr-general-x4v3.pth"
        
        init {
            try {
                System.loadLibrary("ncnn")
                System.loadLibrary("realesrgan")
                Log.d(TAG, "NCNN库加载成功")
            } catch (e: UnsatisfiedLinkError) {
                Log.e(TAG, "NCNN库加载失败", e)
            }
        }
    }
    
    // JNI接口声明
    private external fun realESRGANInit(paramPath: String, binPath: String): Long
    private external fun realESRGANProcess(handle: Long, inputPath: String, outputPath: String, scale: Int): Int
    private external fun realESRGANRelease(handle: Long)
    
    private var modelHandle: Long = 0
    private var isInitialized = false
    
    enum class ModelType {
        ANIME,      // 动漫图像专用
        GENERAL     // 通用场景
    }
    
    /**
     * 初始化Real-ESRGAN模型
     */
    suspend fun initialize(modelType: ModelType = ModelType.GENERAL): Boolean = withContext(Dispatchers.IO) {
        try {
            if (isInitialized) {
                Log.d(TAG, "模型已初始化")
                return@withContext true
            }
            
            val (paramFile, binFile) = when (modelType) {
                ModelType.ANIME -> Pair(ANIME_MODEL_PARAM, ANIME_MODEL_BIN)
                ModelType.GENERAL -> Pair(GENERAL_MODEL_PARAM, GENERAL_MODEL_BIN)
            }
            
            // 检查模型文件是否存在
            val modelDir = File(context.filesDir, "realesrgan_models")
            if (!modelDir.exists()) {
                modelDir.mkdirs()
            }
            
            val paramPath = File(modelDir, paramFile)
            val binPath = File(modelDir, binFile)
            
            // 如果模型文件不存在，从assets复制或提示下载
            if (!paramPath.exists() || !binPath.exists()) {
                Log.w(TAG, "模型文件不存在，需要下载")
                return@withContext false
            }
            
            // 初始化NCNN模型
            modelHandle = realESRGANInit(paramPath.absolutePath, binPath.absolutePath)
            
            if (modelHandle != 0L) {
                isInitialized = true
                Log.d(TAG, "Real-ESRGAN模型初始化成功")
                return@withContext true
            } else {
                Log.e(TAG, "Real-ESRGAN模型初始化失败")
                return@withContext false
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "初始化失败", e)
            return@withContext false
        }
    }
    
    /**
     * 处理图像
     */
    suspend fun enhanceImage(
        inputBitmap: Bitmap,
        scale: Int = 4,
        progressCallback: ((Float) -> Unit)? = null
    ): Bitmap? = withContext(Dispatchers.IO) {
        
        if (!isInitialized) {
            Log.e(TAG, "模型未初始化")
            return@withContext null
        }
        
        try {
            progressCallback?.invoke(0.1f)
            
            // 创建临时文件
            val tempDir = File(context.cacheDir, "realesrgan_temp")
            if (!tempDir.exists()) {
                tempDir.mkdirs()
            }
            
            val inputFile = File(tempDir, "input_${System.currentTimeMillis()}.jpg")
            val outputFile = File(tempDir, "output_${System.currentTimeMillis()}.jpg")
            
            progressCallback?.invoke(0.2f)
            
            // 保存输入图像
            FileOutputStream(inputFile).use { out ->
                inputBitmap.compress(Bitmap.CompressFormat.JPEG, 100, out)
            }
            
            progressCallback?.invoke(0.3f)
            
            // 调用NCNN处理
            val result = realESRGANProcess(
                modelHandle,
                inputFile.absolutePath,
                outputFile.absolutePath,
                scale
            )
            
            progressCallback?.invoke(0.8f)
            
            if (result == 0 && outputFile.exists()) {
                // 读取处理后的图像
                val enhancedBitmap = BitmapFactory.decodeFile(outputFile.absolutePath)
                
                // 清理临时文件
                inputFile.delete()
                outputFile.delete()
                
                progressCallback?.invoke(1.0f)
                
                Log.d(TAG, "Real-ESRGAN处理成功")
                return@withContext enhancedBitmap
            } else {
                Log.e(TAG, "Real-ESRGAN处理失败，错误码: $result")
                return@withContext null
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "图像处理失败", e)
            return@withContext null
        }
    }
    
    /**
     * 检查模型是否可用
     */
    fun isModelAvailable(modelType: ModelType): Boolean {
        val modelDir = File(context.filesDir, "realesrgan_models")
        val (paramFile, binFile) = when (modelType) {
            ModelType.ANIME -> Pair(ANIME_MODEL_PARAM, ANIME_MODEL_BIN)
            ModelType.GENERAL -> Pair(GENERAL_MODEL_PARAM, GENERAL_MODEL_BIN)
        }
        
        val paramPath = File(modelDir, paramFile)
        val binPath = File(modelDir, binFile)
        
        return paramPath.exists() && binPath.exists()
    }
    
    /**
     * 获取模型下载信息
     */
    fun getModelDownloadInfo(modelType: ModelType): Pair<String, String> {
        return when (modelType) {
            ModelType.ANIME -> Pair(
                "动漫图像专用模型 (6MB)",
                "专为动漫、插画图像优化，模型较小，适合手机使用"
            )
            ModelType.GENERAL -> Pair(
                "通用场景超小模型 (4MB)",
                "适用于各种图像类型，模型最小，处理速度快"
            )
        }
    }
    
    /**
     * 复制assets中的模型文件
     */
    suspend fun copyModelFromAssets(modelType: ModelType): Boolean = withContext(Dispatchers.IO) {
        try {
            val modelDir = File(context.filesDir, "realesrgan_models")
            if (!modelDir.exists()) {
                modelDir.mkdirs()
            }
            
            val (paramFile, binFile) = when (modelType) {
                ModelType.ANIME -> Pair(ANIME_MODEL_PARAM, ANIME_MODEL_BIN)
                ModelType.GENERAL -> Pair(GENERAL_MODEL_PARAM, GENERAL_MODEL_BIN)
            }
            
            // 复制param文件
            copyAssetFile("realesrgan_models/$paramFile", File(modelDir, paramFile))
            // 复制bin文件
            copyAssetFile("realesrgan_models/$binFile", File(modelDir, binFile))
            
            Log.d(TAG, "模型文件复制成功")
            return@withContext true
            
        } catch (e: Exception) {
            Log.e(TAG, "模型文件复制失败", e)
            return@withContext false
        }
    }
    
    private fun copyAssetFile(assetPath: String, destFile: File) {
        context.assets.open(assetPath).use { input ->
            FileOutputStream(destFile).use { output ->
                input.copyTo(output)
            }
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        if (isInitialized && modelHandle != 0L) {
            realESRGANRelease(modelHandle)
            modelHandle = 0
            isInitialized = false
            Log.d(TAG, "Real-ESRGAN资源已释放")
        }
    }
}
