<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Magnifying Glass\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Magnifying Glass\app\src\main\res"><file name="brightness_indicator_background" path="C:\Magnifying Glass\app\src\main\res\drawable\brightness_indicator_background.xml" qualifiers="" type="drawable"/><file name="control_button_background" path="C:\Magnifying Glass\app\src\main\res\drawable\control_button_background.xml" qualifiers="" type="drawable"/><file name="enhance_dialog_background" path="C:\Magnifying Glass\app\src\main\res\drawable\enhance_dialog_background.xml" qualifiers="" type="drawable"/><file name="focus_indicator" path="C:\Magnifying Glass\app\src\main\res\drawable\focus_indicator.xml" qualifiers="" type="drawable"/><file name="ic_app_icon" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_app_icon.xml" qualifiers="" type="drawable"/><file name="ic_app_icon_with_border" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_app_icon_with_border.xml" qualifiers="" type="drawable"/><file name="ic_back" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_back.xml" qualifiers="" type="drawable"/><file name="ic_brightness" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_brightness.xml" qualifiers="" type="drawable"/><file name="ic_camera" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_camera.xml" qualifiers="" type="drawable"/><file name="ic_contrast" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_contrast.xml" qualifiers="" type="drawable"/><file name="ic_enhance" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_enhance.xml" qualifiers="" type="drawable"/><file name="ic_flashlight" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_flashlight.xml" qualifiers="" type="drawable"/><file name="ic_freeze" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_freeze.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_magnifying_glass" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_magnifying_glass.png" qualifiers="" type="drawable"/><file name="ic_mode" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_mode.xml" qualifiers="" type="drawable"/><file name="ic_saturation" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_saturation.xml" qualifiers="" type="drawable"/><file name="ic_save" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_save.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_super_resolution" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_super_resolution.xml" qualifiers="" type="drawable"/><file name="ic_zoom_in" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_zoom_in.xml" qualifiers="" type="drawable"/><file name="ic_zoom_out" path="C:\Magnifying Glass\app\src\main\res\drawable\ic_zoom_out.xml" qualifiers="" type="drawable"/><file name="magnifier_mask" path="C:\Magnifying Glass\app\src\main\res\drawable\magnifier_mask.xml" qualifiers="" type="drawable"/><file name="sliders_background" path="C:\Magnifying Glass\app\src\main\res\drawable\sliders_background.xml" qualifiers="" type="drawable"/><file name="splash_background" path="C:\Magnifying Glass\app\src\main\res\drawable\splash_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Magnifying Glass\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_onboarding" path="C:\Magnifying Glass\app\src\main\res\layout\activity_onboarding.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Magnifying Glass\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="dialog_enhance" path="C:\Magnifying Glass\app\src\main\res\layout\dialog_enhance.xml" qualifiers="" type="layout"/><file name="dialog_permission" path="C:\Magnifying Glass\app\src\main\res\layout\dialog_permission.xml" qualifiers="" type="layout"/><file name="onboarding_page" path="C:\Magnifying Glass\app\src\main\res\layout\onboarding_page.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Magnifying Glass\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Magnifying Glass\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="C:\Magnifying Glass\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="transparent">#00000000</color><color name="primary">#FF2196F3</color><color name="primary_dark">#FF1976D2</color><color name="accent">#FFFF5722</color><color name="control_background">#80000000</color><color name="control_background_pressed">#B0000000</color><color name="control_text">#FFFFFFFF</color><color name="control_icon">#FFFFFFFF</color><color name="active">#FF4CAF50</color><color name="inactive">#FF9E9E9E</color><color name="warning">#FFFF9800</color><color name="error">#FFF44336</color><color name="high_contrast_bg">#FF000000</color><color name="high_contrast_fg">#FFFFFFFF</color><color name="overlay_dark">#80000000</color><color name="overlay_light">#80FFFFFF</color></file><file path="C:\Magnifying Glass\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="control_button_size">64dp</dimen><dimen name="small_control_button_size">48dp</dimen><dimen name="focus_indicator_size">60dp</dimen></file><file path="C:\Magnifying Glass\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">放大镜</string><string name="zoom_in">放大</string><string name="zoom_out">缩小</string><string name="freeze_frame">冻结</string><string name="save_image">保存</string><string name="flashlight">手电筒</string><string name="mode_switch">模式</string><string name="settings">设置</string><string name="normal_mode">普通模式</string><string name="reading_mode">阅读模式</string><string name="low_light_mode">低光模式</string><string name="high_contrast_mode">高对比度</string><string name="permission_camera_title">需要相机权限</string><string name="permission_camera_message">放大镜需要使用相机来显示放大画面，请授予相机权限。</string><string name="permission_storage_title">需要存储权限</string><string name="permission_storage_message">保存截图需要存储权限，请授予存储权限。</string><string name="permission_denied">权限被拒绝</string><string name="permission_grant">授予权限</string><string name="permission_cancel">取消</string><string name="onboarding_title_1">欢迎使用放大镜</string><string name="onboarding_desc_1">双指捏合或双击屏幕可以调节放大倍数</string><string name="onboarding_title_2">冻结画面</string><string name="onboarding_desc_2">长按屏幕可以冻结当前画面，方便仔细观看</string><string name="onboarding_title_3">调节亮度</string><string name="onboarding_desc_3">在屏幕边缘上下滑动可以调节画面亮度</string><string name="onboarding_next">下一步</string><string name="onboarding_start">开始使用</string><string name="onboarding_skip">跳过</string><string name="zoom_level">%dx</string><string name="brightness_level">亮度: %d%%</string><string name="image_saved">图片已保存到相册</string><string name="image_save_failed">保存失败</string><string name="back">返回</string><string name="enhance">增强</string><string name="enhance_image">画面增强</string><string name="frame_frozen">画面已冻结</string><string name="frame_unfrozen">恢复实时画面</string><string name="flashlight_on">手电筒已开启</string><string name="flashlight_off">手电筒已关闭</string><string name="low_light_detected">检测到弱光环境，是否开启最大亮度？</string><string name="enable_max_brightness">开启</string><string name="dismiss">忽略</string><string name="scenario_package">📦 看清快递单上的小字</string><string name="scenario_medicine">💊 阅读药品说明书</string><string name="scenario_inspection">🕵️‍♂️ 检查商品细节瑕疵</string><string name="camera_error">相机初始化失败</string><string name="camera_not_available">相机不可用</string><string name="focus_failed">对焦失败</string><string name="privacy_policy_title">隐私政策与用户协议</string><string name="privacy_policy_accept">同意并继续</string><string name="privacy_policy_decline">不同意</string><string name="app_description">专为老年人设计的智能放大镜应用，提供清晰的视觉辅助功能</string><string name="app_version">版本 1.0</string><string name="app_subtitle">专为老年人设计的智能放大镜</string></file><file path="C:\Magnifying Glass\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MagnifyingGlass" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">@color/black</item>
        <item name="android:navigationBarColor">@color/black</item>
    </style><style name="Theme.MagnifyingGlass.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style><style name="ControlButton">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:background">@drawable/control_button_background</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:padding">12dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:tint">@color/control_icon</item>
    </style><style name="SmallControlButton" parent="ControlButton">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:padding">10dp</item>
        <item name="android:layout_margin">6dp</item>
    </style><style name="InfoText">
        <item name="android:textColor">@color/control_text</item>
        <item name="android:textSize">16sp</item>
        <item name="android:background">@color/control_background</item>
        <item name="android:padding">8dp</item>
        <item name="android:layout_margin">4dp</item>
    </style><style name="OnboardingTitle">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/primary</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style><style name="OnboardingDescription">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:gravity">center</item>
        <item name="android:lineSpacingExtra">4dp</item>
    </style></file><file path="C:\Magnifying Glass\app\src\main\res\values\themes_splash.xml" qualifiers=""><style name="SplashTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsTranslucent">false</item>
    </style></file><file name="backup_rules" path="C:\Magnifying Glass\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Magnifying Glass\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Magnifying Glass\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Magnifying Glass\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Magnifying Glass\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Magnifying Glass\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>