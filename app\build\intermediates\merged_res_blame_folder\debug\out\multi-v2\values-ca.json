{"logs": [{"outputFile": "com.magnifyingglass.app-mergeDebugResources-2:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c3074531d01ac9ab8cfdf2481696a09e\\transformed\\core-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3493,3589,3691,3790,3887,3993,4098,9572", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "3584,3686,3785,3882,3988,4093,4219,9668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7c6a6834abbfd4e3a8f7f46272ab8256\\transformed\\appcompat-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,447,552,659,742,848,974,1058,1137,1228,1321,1414,1509,1607,1700,1793,1887,1978,2069,2150,2261,2369,2467,2577,2682,2790,2950,9490", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "442,547,654,737,843,969,1053,1132,1223,1316,1409,1504,1602,1695,1788,1882,1973,2064,2145,2256,2364,2462,2572,2677,2785,2945,3044,9567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57c380a018d3ece89de602b2e29bfae7\\transformed\\material-1.10.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1096,1193,1273,1338,1433,1497,1569,1631,1707,1770,1827,1948,2006,2067,2124,2204,2341,2428,2512,2651,2729,2808,2960,3049,3125,3182,3238,3304,3382,3463,3551,3639,3717,3794,3868,3947,4057,4147,4239,4331,4432,4506,4588,4689,4739,4822,4888,4980,5067,5129,5193,5256,5329,5452,5565,5669,5777,5838,5898", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,151,88,75,56,55,65,77,80,87,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85", "endOffsets": "269,350,430,518,621,713,814,942,1026,1091,1188,1268,1333,1428,1492,1564,1626,1702,1765,1822,1943,2001,2062,2119,2199,2336,2423,2507,2646,2724,2803,2955,3044,3120,3177,3233,3299,3377,3458,3546,3634,3712,3789,3863,3942,4052,4142,4234,4326,4427,4501,4583,4684,4734,4817,4883,4975,5062,5124,5188,5251,5324,5447,5560,5664,5772,5833,5893,5979"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3049,3130,3210,3298,3401,4224,4325,4453,4537,4602,4699,4779,4844,4939,5003,5075,5137,5213,5276,5333,5454,5512,5573,5630,5710,5847,5934,6018,6157,6235,6314,6466,6555,6631,6688,6744,6810,6888,6969,7057,7145,7223,7300,7374,7453,7563,7653,7745,7837,7938,8012,8094,8195,8245,8328,8394,8486,8573,8635,8699,8762,8835,8958,9071,9175,9283,9344,9404", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,151,88,75,56,55,65,77,80,87,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85", "endOffsets": "319,3125,3205,3293,3396,3488,4320,4448,4532,4597,4694,4774,4839,4934,4998,5070,5132,5208,5271,5328,5449,5507,5568,5625,5705,5842,5929,6013,6152,6230,6309,6461,6550,6626,6683,6739,6805,6883,6964,7052,7140,7218,7295,7369,7448,7558,7648,7740,7832,7933,8007,8089,8190,8240,8323,8389,8481,8568,8630,8694,8757,8830,8953,9066,9170,9278,9339,9399,9485"}}]}]}