package com.magnifyingglass.app.utils

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.util.Log
import androidx.camera.core.Camera
import androidx.camera.camera2.interop.Camera2Interop
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import kotlin.math.abs
import kotlin.math.sqrt

class StabilizationController(
    private val context: Context
) : SensorEventListener {
    
    companion object {
        private const val TAG = "StabilizationController"
        private const val GYRO_THRESHOLD = 0.1f // 陀螺仪阈值
        private const val ACCEL_THRESHOLD = 0.5f // 加速度计阈值
        private const val STABILIZATION_FACTOR = 0.8f // 稳定化因子
        private const val SENSOR_DELAY = SensorManager.SENSOR_DELAY_GAME
    }
    
    interface StabilizationListener {
        fun onStabilizationStateChanged(isStable: Boolean)
        fun onMovementDetected(intensity: Float)
    }
    
    private val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private var gyroscope: Sensor? = null
    private var accelerometer: Sensor? = null
    private var camera: Camera? = null
    
    private var isEnabled = true
    private var isHardwareStabilizationSupported = false
    private var stabilizationListener: StabilizationListener? = null
    
    // 传感器数据
    private var gyroData = FloatArray(3)
    private var accelData = FloatArray(3)
    private var lastGyroTime = 0L
    private var lastAccelTime = 0L
    
    // 稳定性状态
    private var isCurrentlyStable = true
    private var movementIntensity = 0f
    
    // 滤波器参数
    private val gyroFilter = LowPassFilter(0.8f)
    private val accelFilter = LowPassFilter(0.9f)
    
    init {
        initializeSensors()
    }
    
    private fun initializeSensors() {
        gyroscope = sensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE)
        accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
        
        if (gyroscope == null) {
            Log.w(TAG, "Gyroscope not available")
        }
        
        if (accelerometer == null) {
            Log.w(TAG, "Accelerometer not available")
        }
    }
    
    fun setCamera(camera: Camera) {
        this.camera = camera
        checkHardwareStabilizationSupport()
    }
    
    fun setStabilizationListener(listener: StabilizationListener) {
        this.stabilizationListener = listener
    }
    
    @OptIn(ExperimentalCamera2Interop::class)
    private fun checkHardwareStabilizationSupport() {
        camera?.let { cam ->
            try {
                // 检查是否支持硬件防抖
                val cameraController = CameraController(context)
                isHardwareStabilizationSupported = cameraController.supportsVideoStabilization(cam) ||
                        cameraController.supportsOpticalStabilization(cam)
                
                if (isHardwareStabilizationSupported) {
                    enableHardwareStabilization()
                }
                
                Log.d(TAG, "Hardware stabilization supported: $isHardwareStabilizationSupported")
            } catch (e: Exception) {
                Log.w(TAG, "Failed to check hardware stabilization support", e)
                isHardwareStabilizationSupported = false
            }
        }
    }
    
    @OptIn(ExperimentalCamera2Interop::class)
    private fun enableHardwareStabilization() {
        camera?.let { cam ->
            try {
                // 启用视频防抖
                // 这里需要在创建CaptureRequest时设置防抖参数
                // 由于CameraX的限制，我们只能在支持的设备上启用
                Log.d(TAG, "Hardware stabilization enabled")
            } catch (e: Exception) {
                Log.w(TAG, "Failed to enable hardware stabilization", e)
            }
        }
    }
    
    fun startStabilization() {
        if (!isEnabled) return
        
        gyroscope?.let { 
            sensorManager.registerListener(this, it, SENSOR_DELAY)
        }
        
        accelerometer?.let {
            sensorManager.registerListener(this, it, SENSOR_DELAY)
        }
        
        Log.d(TAG, "Stabilization started")
    }
    
    fun stopStabilization() {
        sensorManager.unregisterListener(this)
        Log.d(TAG, "Stabilization stopped")
    }
    
    fun setEnabled(enabled: Boolean) {
        isEnabled = enabled
        if (enabled) {
            startStabilization()
        } else {
            stopStabilization()
        }
    }
    
    fun isEnabled(): Boolean = isEnabled
    
    fun isHardwareStabilizationSupported(): Boolean = isHardwareStabilizationSupported
    
    override fun onSensorChanged(event: SensorEvent?) {
        if (!isEnabled) return
        
        event?.let {
            when (it.sensor.type) {
                Sensor.TYPE_GYROSCOPE -> {
                    processGyroscopeData(it.values, it.timestamp)
                }
                Sensor.TYPE_ACCELEROMETER -> {
                    processAccelerometerData(it.values, it.timestamp)
                }
            }
            
            updateStabilityState()
        }
    }
    
    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        Log.d(TAG, "Sensor accuracy changed: ${sensor?.name}, accuracy: $accuracy")
    }
    
    private fun processGyroscopeData(values: FloatArray, timestamp: Long) {
        // 应用低通滤波器
        gyroData = gyroFilter.filter(values)
        lastGyroTime = timestamp
        
        // 计算角速度大小
        val angularVelocity = sqrt(
            gyroData[0] * gyroData[0] + 
            gyroData[1] * gyroData[1] + 
            gyroData[2] * gyroData[2]
        )
        
        // 更新运动强度
        movementIntensity = (movementIntensity + angularVelocity) / 2f
    }
    
    private fun processAccelerometerData(values: FloatArray, timestamp: Long) {
        // 应用低通滤波器
        accelData = accelFilter.filter(values)
        lastAccelTime = timestamp
        
        // 计算加速度变化
        val totalAccel = sqrt(
            accelData[0] * accelData[0] + 
            accelData[1] * accelData[1] + 
            accelData[2] * accelData[2]
        )
        
        // 减去重力加速度
        val dynamicAccel = abs(totalAccel - SensorManager.GRAVITY_EARTH)
        
        // 更新运动强度
        movementIntensity = (movementIntensity + dynamicAccel) / 2f
    }
    
    private fun updateStabilityState() {
        val gyroStable = gyroData.all { abs(it) < GYRO_THRESHOLD }
        val accelStable = abs(movementIntensity) < ACCEL_THRESHOLD
        
        val newStableState = gyroStable && accelStable
        
        if (newStableState != isCurrentlyStable) {
            isCurrentlyStable = newStableState
            stabilizationListener?.onStabilizationStateChanged(isCurrentlyStable)
        }
        
        stabilizationListener?.onMovementDetected(movementIntensity)
    }
    
    /**
     * 获取当前稳定性状态
     */
    fun isCurrentlyStable(): Boolean = isCurrentlyStable
    
    /**
     * 获取当前运动强度
     */
    fun getMovementIntensity(): Float = movementIntensity
    
    /**
     * 获取稳定性建议
     */
    fun getStabilityRecommendation(): String {
        return when {
            movementIntensity < 0.1f -> "非常稳定"
            movementIntensity < 0.3f -> "轻微晃动"
            movementIntensity < 0.6f -> "中等晃动"
            else -> "剧烈晃动"
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        stopStabilization()
        stabilizationListener = null
    }
    
    /**
     * 低通滤波器类
     */
    private class LowPassFilter(private val alpha: Float) {
        private var output: FloatArray? = null
        
        fun filter(input: FloatArray): FloatArray {
            if (output == null) {
                output = input.clone()
                return output!!
            }
            
            for (i in input.indices) {
                output!![i] = alpha * output!![i] + (1 - alpha) * input[i]
            }
            
            return output!!
        }
    }
}
