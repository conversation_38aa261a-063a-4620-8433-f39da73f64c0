/ Header Record For PersistentHashMapValueStorage) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity? >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener5 4android.view.GestureDetector.SimpleOnGestureListener kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum% $android.hardware.SensorEventListener kotlin.Enum android.view.View!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBindingN (androidx.appcompat.app.AppCompatActivity$android.hardware.SensorEventListener) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivityN (androidx.appcompat.app.AppCompatActivity$android.hardware.SensorEventListener) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder kotlin.Enum android.view.View!  androidx.viewbinding.ViewBinding) (androidx.appcompat.app.AppCompatActivity android.view.ViewN (androidx.appcompat.app.AppCompatActivity$android.hardware.SensorEventListener) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity kotlin.Enum kotlin.Enum android.view.View