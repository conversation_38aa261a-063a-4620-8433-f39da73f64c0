/ Header Record For PersistentHashMapValueStorageN (androidx.appcompat.app.AppCompatActivity$android.hardware.SensorEventListener) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder? >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener5 4android.view.GestureDetector.SimpleOnGestureListener kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum% $android.hardware.SensorEventListener!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBindingN (androidx.appcompat.app.AppCompatActivity$android.hardware.SensorEventListenerN (androidx.appcompat.app.AppCompatActivity$android.hardware.SensorEventListenerN (androidx.appcompat.app.AppCompatActivity$android.hardware.SensorEventListener) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder? >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener5 4android.view.GestureDetector.SimpleOnGestureListener kotlin.Enum kotlin.Enum% $android.hardware.SensorEventListenerN (androidx.appcompat.app.AppCompatActivity$android.hardware.SensorEventListener) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity? >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener5 4android.view.GestureDetector.SimpleOnGestureListener% $android.hardware.SensorEventListener!  androidx.viewbinding.ViewBinding