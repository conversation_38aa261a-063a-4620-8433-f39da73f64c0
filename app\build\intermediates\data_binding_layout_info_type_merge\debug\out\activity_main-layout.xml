<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.magnifyingglass.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="168" endOffset="51"/></Target><Target id="@+id/previewView" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="10" startOffset="4" endLine="17" endOffset="51"/></Target><Target id="@+id/frozenImageView" view="ImageView"><Expressions/><location startLine="20" startOffset="4" endLine="29" endOffset="51"/></Target><Target id="@+id/topInfoLayout" view="LinearLayout"><Expressions/><location startLine="32" startOffset="4" endLine="68" endOffset="18"/></Target><Target id="@+id/zoomLevelText" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="49" endOffset="38"/></Target><Target id="@+id/brightnessText" view="TextView"><Expressions/><location startLine="57" startOffset="8" endLine="64" endOffset="39"/></Target><Target id="@+id/bottomControlLayout" view="LinearLayout"><Expressions/><location startLine="71" startOffset="4" endLine="117" endOffset="18"/></Target><Target id="@+id/zoomOutButton" view="ImageButton"><Expressions/><location startLine="83" startOffset="8" endLine="87" endOffset="49"/></Target><Target id="@+id/flashlightButton" view="ImageButton"><Expressions/><location startLine="90" startOffset="8" endLine="94" endOffset="51"/></Target><Target id="@+id/freezeButton" view="ImageButton"><Expressions/><location startLine="97" startOffset="8" endLine="101" endOffset="47"/></Target><Target id="@+id/modeButton" view="ImageButton"><Expressions/><location startLine="104" startOffset="8" endLine="108" endOffset="45"/></Target><Target id="@+id/zoomInButton" view="ImageButton"><Expressions/><location startLine="111" startOffset="8" endLine="115" endOffset="48"/></Target><Target id="@+id/frozenButtonsContainer" view="LinearLayout"><Expressions/><location startLine="120" startOffset="4" endLine="153" endOffset="18"/></Target><Target id="@+id/backButton" view="ImageButton"><Expressions/><location startLine="132" startOffset="8" endLine="137" endOffset="45"/></Target><Target id="@+id/saveButton" view="ImageButton"><Expressions/><location startLine="140" startOffset="8" endLine="144" endOffset="45"/></Target><Target id="@+id/enhanceButton" view="ImageButton"><Expressions/><location startLine="147" startOffset="8" endLine="152" endOffset="48"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="156" startOffset="4" endLine="166" endOffset="55"/></Target></Targets></Layout>