<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.magnifyingglass.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="283" endOffset="51"/></Target><Target id="@+id/previewView" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="10" startOffset="4" endLine="17" endOffset="51"/></Target><Target id="@+id/magnifierMask" view="FrameLayout"><Expressions/><location startLine="20" startOffset="4" endLine="28" endOffset="51"/></Target><Target id="@+id/frozenImageView" view="ImageView"><Expressions/><location startLine="31" startOffset="4" endLine="40" endOffset="51"/></Target><Target id="@+id/topInfoLayout" view="LinearLayout"><Expressions/><location startLine="43" startOffset="4" endLine="79" endOffset="18"/></Target><Target id="@+id/zoomLevelText" view="TextView"><Expressions/><location startLine="54" startOffset="8" endLine="60" endOffset="38"/></Target><Target id="@+id/brightnessText" view="TextView"><Expressions/><location startLine="68" startOffset="8" endLine="75" endOffset="39"/></Target><Target id="@+id/bottomControlLayout" view="LinearLayout"><Expressions/><location startLine="82" startOffset="4" endLine="128" endOffset="18"/></Target><Target id="@+id/zoomOutButton" view="ImageButton"><Expressions/><location startLine="94" startOffset="8" endLine="98" endOffset="49"/></Target><Target id="@+id/flashlightButton" view="ImageButton"><Expressions/><location startLine="101" startOffset="8" endLine="105" endOffset="51"/></Target><Target id="@+id/freezeButton" view="ImageButton"><Expressions/><location startLine="108" startOffset="8" endLine="112" endOffset="47"/></Target><Target id="@+id/modeButton" view="ImageButton"><Expressions/><location startLine="115" startOffset="8" endLine="119" endOffset="45"/></Target><Target id="@+id/zoomInButton" view="ImageButton"><Expressions/><location startLine="122" startOffset="8" endLine="126" endOffset="48"/></Target><Target id="@+id/enhancementSlidersContainer" view="LinearLayout"><Expressions/><location startLine="131" startOffset="4" endLine="224" endOffset="18"/></Target><Target id="@+id/brightnessSlider" view="SeekBar"><Expressions/><location startLine="159" startOffset="12" endLine="167" endOffset="45"/></Target><Target id="@+id/contrastSlider" view="SeekBar"><Expressions/><location startLine="186" startOffset="12" endLine="194" endOffset="45"/></Target><Target id="@+id/saturationSlider" view="SeekBar"><Expressions/><location startLine="212" startOffset="12" endLine="220" endOffset="45"/></Target><Target id="@+id/frozenButtonsContainer" view="LinearLayout"><Expressions/><location startLine="227" startOffset="4" endLine="268" endOffset="18"/></Target><Target id="@+id/backButton" view="ImageButton"><Expressions/><location startLine="239" startOffset="8" endLine="244" endOffset="45"/></Target><Target id="@+id/saveButton" view="ImageButton"><Expressions/><location startLine="247" startOffset="8" endLine="251" endOffset="45"/></Target><Target id="@+id/enhanceButton" view="ImageButton"><Expressions/><location startLine="254" startOffset="8" endLine="259" endOffset="48"/></Target><Target id="@+id/realESRGANButton" view="ImageButton"><Expressions/><location startLine="262" startOffset="8" endLine="267" endOffset="57"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="271" startOffset="4" endLine="281" endOffset="55"/></Target></Targets></Layout>