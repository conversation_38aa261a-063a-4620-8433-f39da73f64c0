{"logs": [{"outputFile": "com.magnifyingglass.app-mergeDebugResources-2:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57c380a018d3ece89de602b2e29bfae7\\transformed\\material-1.10.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1186,1279,1356,1419,1535,1598,1667,1726,1797,1856,1910,2031,2092,2155,2209,2282,2404,2492,2575,2727,2813,2900,3033,3124,3207,3264,3315,3381,3453,3530,3614,3697,3772,3849,3931,4007,4115,4204,4286,4377,4473,4547,4628,4723,4777,4859,4925,5012,5098,5160,5224,5287,5356,5466,5579,5682,5789,5850,5905", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1181,1274,1351,1414,1530,1593,1662,1721,1792,1851,1905,2026,2087,2150,2204,2277,2399,2487,2570,2722,2808,2895,3028,3119,3202,3259,3310,3376,3448,3525,3609,3692,3767,3844,3926,4002,4110,4199,4281,4372,4468,4542,4623,4718,4772,4854,4920,5007,5093,5155,5219,5282,5351,5461,5574,5677,5784,5845,5900,5980"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3162,3239,3316,3398,3495,4318,4415,4547,4630,4697,4790,4867,4930,5046,5109,5178,5237,5308,5367,5421,5542,5603,5666,5720,5793,5915,6003,6086,6238,6324,6411,6544,6635,6718,6775,6826,6892,6964,7041,7125,7208,7283,7360,7442,7518,7626,7715,7797,7888,7984,8058,8139,8234,8288,8370,8436,8523,8609,8671,8735,8798,8867,8977,9090,9193,9300,9361,9416", "endLines": "7,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "427,3234,3311,3393,3490,3582,4410,4542,4625,4692,4785,4862,4925,5041,5104,5173,5232,5303,5362,5416,5537,5598,5661,5715,5788,5910,5998,6081,6233,6319,6406,6539,6630,6713,6770,6821,6887,6959,7036,7120,7203,7278,7355,7437,7513,7621,7710,7792,7883,7979,8053,8134,8229,8283,8365,8431,8518,8604,8666,8730,8793,8862,8972,9085,9188,9295,9356,9411,9491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c3074531d01ac9ab8cfdf2481696a09e\\transformed\\core-1.12.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,111", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3587,3685,3787,3887,3988,4094,4197,9578", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3680,3782,3882,3983,4089,4192,4313,9674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7c6a6834abbfd4e3a8f7f46272ab8256\\transformed\\appcompat-1.6.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "432,552,655,771,857,962,1081,1161,1238,1330,1424,1519,1613,1708,1802,1898,1993,2085,2177,2258,2364,2469,2567,2675,2781,2889,3062,9496", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "547,650,766,852,957,1076,1156,1233,1325,1419,1514,1608,1703,1797,1893,1988,2080,2172,2253,2359,2464,2562,2670,2776,2884,3057,3157,9573"}}]}]}