$com.magnifyingglass.app.MainActivity*com.magnifyingglass.app.OnboardingActivity9com.magnifyingglass.app.OnboardingActivity.OnboardingPage<com.magnifyingglass.app.OnboardingActivity.OnboardingAdapterQcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder&com.magnifyingglass.app.SplashActivity0com.magnifyingglass.app.SplashActivity.Companion2com.magnifyingglass.app.utils.BrightnessController<com.magnifyingglass.app.utils.BrightnessController.CompanionEcom.magnifyingglass.app.utils.BrightnessController.BrightnessListener.com.magnifyingglass.app.utils.CameraController8com.magnifyingglass.app.utils.CameraController.Companion9com.magnifyingglass.app.utils.CameraController.DeviceInfo=com.magnifyingglass.app.utils.CameraController.CameraSettings,com.magnifyingglass.app.utils.GestureHandler6com.magnifyingglass.app.utils.GestureHandler.Companion<com.magnifyingglass.app.utils.GestureHandler.GestureListener:com.magnifyingglass.app.utils.GestureHandler.ScaleListenerBcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener2com.magnifyingglass.app.utils.ImageFilterProcessor<com.magnifyingglass.app.utils.ImageFilterProcessor.Companion=com.magnifyingglass.app.utils.ImageFilterProcessor.FilterType(com.magnifyingglass.app.utils.ImageSaver2com.magnifyingglass.app.utils.ImageSaver.Companion5com.magnifyingglass.app.utils.ImageSaver.SaveListener7com.magnifyingglass.app.utils.ImageSaver.SaveStatistics)com.magnifyingglass.app.utils.ModeManager3com.magnifyingglass.app.utils.ModeManager.Companion4com.magnifyingglass.app.utils.ModeManager.CameraMode<com.magnifyingglass.app.utils.ModeManager.ModeChangeListener;com.magnifyingglass.app.utils.ModeManager.ModeConfiguration7com.magnifyingglass.app.utils.ModeManager.AutoFocusMode0com.magnifyingglass.app.utils.OnboardingAnimator:com.magnifyingglass.app.utils.OnboardingAnimator.CompanionDcom.magnifyingglass.app.utils.OnboardingAnimator.TransitionDirection<com.magnifyingglass.app.utils.OnboardingAnimator.GestureType0com.magnifyingglass.app.utils.PerformanceMonitor:com.magnifyingglass.app.utils.PerformanceMonitor.CompanionDcom.magnifyingglass.app.utils.PerformanceMonitor.PerformanceListenerBcom.magnifyingglass.app.utils.PerformanceMonitor.PerformanceReport5com.magnifyingglass.app.utils.PermissionDialogManagerNcom.magnifyingglass.app.utils.PermissionDialogManager.PermissionDialogListener.com.magnifyingglass.app.utils.PermissionHelper8com.magnifyingglass.app.utils.PermissionHelper.Companion1com.magnifyingglass.app.utils.RealESRGANProcessor;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion5com.magnifyingglass.app.utils.StabilizationController?com.magnifyingglass.app.utils.StabilizationController.CompanionKcom.magnifyingglass.app.utils.StabilizationController.StabilizationListenerCcom.magnifyingglass.app.utils.StabilizationController.LowPassFilter5com.magnifyingglass.app.utils.TrueRealESRGANProcessor?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.ModelType,com.magnifyingglass.app.utils.ZoomController6com.magnifyingglass.app.utils.ZoomController.Companion9com.magnifyingglass.app.utils.ZoomController.ZoomListener2com.magnifyingglass.app.views.MagnifierOverlayView=com.magnifyingglass.app.databinding.ActivityOnboardingBinding7com.magnifyingglass.app.databinding.ActivityMainBinding9com.magnifyingglass.app.databinding.ActivitySplashBinding.com.magnifyingglass.app.MainActivity.Companion-com.magnifyingglass.app.utils.AutoTestManager7com.magnifyingglass.app.utils.AutoTestManager.Companion:com.magnifyingglass.app.utils.AutoTestManager.TestFunction2com.magnifyingglass.app.utils.PrivacyPolicyManager<com.magnifyingglass.app.utils.PrivacyPolicyManager.Companion                                                                                                                                                                                                                                                                                                                                                                    