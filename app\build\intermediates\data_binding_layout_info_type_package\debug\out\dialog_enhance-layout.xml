<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_enhance" modulePackage="com.magnifyingglass.app" filePath="app\src\main\res\layout\dialog_enhance.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_enhance_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="178" endOffset="14"/></Target><Target id="@+id/sharpenSeekBar" view="SeekBar"><Expressions/><location startLine="33" startOffset="8" endLine="40" endOffset="52"/></Target><Target id="@+id/sharpenValue" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="48" endOffset="38"/></Target><Target id="@+id/contrastSeekBar" view="SeekBar"><Expressions/><location startLine="66" startOffset="8" endLine="73" endOffset="52"/></Target><Target id="@+id/contrastValue" view="TextView"><Expressions/><location startLine="75" startOffset="8" endLine="81" endOffset="38"/></Target><Target id="@+id/brightnessSeekBar" view="SeekBar"><Expressions/><location startLine="99" startOffset="8" endLine="106" endOffset="52"/></Target><Target id="@+id/brightnessValue" view="TextView"><Expressions/><location startLine="108" startOffset="8" endLine="114" endOffset="38"/></Target><Target id="@+id/noiseReductionSeekBar" view="SeekBar"><Expressions/><location startLine="132" startOffset="8" endLine="139" endOffset="52"/></Target><Target id="@+id/noiseReductionValue" view="TextView"><Expressions/><location startLine="141" startOffset="8" endLine="147" endOffset="38"/></Target><Target id="@+id/resetButton" view="Button"><Expressions/><location startLine="157" startOffset="8" endLine="162" endOffset="45"/></Target><Target id="@+id/applyButton" view="Button"><Expressions/><location startLine="164" startOffset="8" endLine="168" endOffset="31"/></Target><Target id="@+id/cancelButton" view="Button"><Expressions/><location startLine="170" startOffset="8" endLine="175" endOffset="47"/></Target></Targets></Layout>