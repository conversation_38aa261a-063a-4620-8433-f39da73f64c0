<variant
    name="release"
    package="com.magnifyingglass.app"
    minSdkVersion="21"
    targetSdkVersion="34"
    shrinking="true"
    mergedManifest="build\intermediates\merged_manifest\release\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.1.2;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\ab312e3e39d2f2515abe76151f7e37bc\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      viewBinding="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <mainArtifact
      classOutputs="build\intermediates\javac\release\classes;build\tmp\kotlin-classes\release;build\kotlinToolingMetadata;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\R.jar"
      applicationId="com.magnifyingglass.app"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out;build\generated\data_binding_base_class_source_out\release\out"
      generatedResourceFolders="build\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\ab312e3e39d2f2515abe76151f7e37bc\transformed\D8BackportedDesugaredMethods.txt">
  </mainArtifact>
</variant>
