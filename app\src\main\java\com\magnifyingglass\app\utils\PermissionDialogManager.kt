package com.magnifyingglass.app.utils

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.view.LayoutInflater
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import com.magnifyingglass.app.R

class PermissionDialogManager(private val activity: Activity) {
    
    interface PermissionDialogListener {
        fun onPermissionGranted()
        fun onPermissionDenied()
        fun onSettingsRequested()
    }
    
    /**
     * 显示相机权限请求对话框
     */
    fun showCameraPermissionDialog(listener: PermissionDialogListener) {
        showPermissionDialog(
            iconRes = R.drawable.ic_camera,
            titleRes = R.string.permission_camera_title,
            messageRes = R.string.permission_camera_message,
            listener = listener
        )
    }
    
    /**
     * 显示存储权限请求对话框
     */
    fun showStoragePermissionDialog(listener: PermissionDialogListener) {
        showPermissionDialog(
            iconRes = R.drawable.ic_save,
            titleRes = R.string.permission_storage_title,
            messageRes = R.string.permission_storage_message,
            listener = listener
        )
    }
    
    /**
     * 显示权限被永久拒绝的对话框
     */
    fun showPermissionPermanentlyDeniedDialog(
        permissionType: String,
        listener: PermissionDialogListener
    ) {
        val dialogView = LayoutInflater.from(activity).inflate(R.layout.dialog_permission, null)
        
        val icon = dialogView.findViewById<ImageView>(R.id.permissionIcon)
        val title = dialogView.findViewById<TextView>(R.id.permissionTitle)
        val message = dialogView.findViewById<TextView>(R.id.permissionMessage)
        val cancelButton = dialogView.findViewById<Button>(R.id.cancelButton)
        val grantButton = dialogView.findViewById<Button>(R.id.grantButton)
        
        // 设置内容
        when (permissionType) {
            "camera" -> {
                icon.setImageResource(R.drawable.ic_camera)
                title.text = activity.getString(R.string.permission_camera_title)
                message.text = "相机权限已被永久拒绝，请在设置中手动开启权限。"
            }
            "storage" -> {
                icon.setImageResource(R.drawable.ic_save)
                title.text = activity.getString(R.string.permission_storage_title)
                message.text = "存储权限已被永久拒绝，请在设置中手动开启权限。"
            }
        }
        
        grantButton.text = "前往设置"
        
        val dialog = AlertDialog.Builder(activity)
            .setView(dialogView)
            .setCancelable(false)
            .create()
        
        cancelButton.setOnClickListener {
            dialog.dismiss()
            listener.onPermissionDenied()
        }
        
        grantButton.setOnClickListener {
            dialog.dismiss()
            openAppSettings()
            listener.onSettingsRequested()
        }
        
        dialog.show()
    }
    
    /**
     * 显示通用权限请求对话框
     */
    private fun showPermissionDialog(
        iconRes: Int,
        titleRes: Int,
        messageRes: Int,
        listener: PermissionDialogListener
    ) {
        val dialogView = LayoutInflater.from(activity).inflate(R.layout.dialog_permission, null)
        
        val icon = dialogView.findViewById<ImageView>(R.id.permissionIcon)
        val title = dialogView.findViewById<TextView>(R.id.permissionTitle)
        val message = dialogView.findViewById<TextView>(R.id.permissionMessage)
        val cancelButton = dialogView.findViewById<Button>(R.id.cancelButton)
        val grantButton = dialogView.findViewById<Button>(R.id.grantButton)
        
        // 设置内容
        icon.setImageResource(iconRes)
        title.setText(titleRes)
        message.setText(messageRes)
        
        val dialog = AlertDialog.Builder(activity)
            .setView(dialogView)
            .setCancelable(false)
            .create()
        
        cancelButton.setOnClickListener {
            dialog.dismiss()
            listener.onPermissionDenied()
        }
        
        grantButton.setOnClickListener {
            dialog.dismiss()
            listener.onPermissionGranted()
        }
        
        dialog.show()
    }
    
    /**
     * 显示权限说明对话框
     */
    fun showPermissionRationaleDialog(
        permissionType: String,
        listener: PermissionDialogListener
    ) {
        val (iconRes, titleRes, messageRes) = when (permissionType) {
            "camera" -> Triple(
                R.drawable.ic_camera,
                R.string.permission_camera_title,
                R.string.permission_camera_message
            )
            "storage" -> Triple(
                R.drawable.ic_save,
                R.string.permission_storage_title,
                R.string.permission_storage_message
            )
            else -> Triple(
                R.drawable.ic_camera,
                R.string.permission_camera_title,
                R.string.permission_camera_message
            )
        }
        
        val dialogView = LayoutInflater.from(activity).inflate(R.layout.dialog_permission, null)
        
        val icon = dialogView.findViewById<ImageView>(R.id.permissionIcon)
        val title = dialogView.findViewById<TextView>(R.id.permissionTitle)
        val message = dialogView.findViewById<TextView>(R.id.permissionMessage)
        val cancelButton = dialogView.findViewById<Button>(R.id.cancelButton)
        val grantButton = dialogView.findViewById<Button>(R.id.grantButton)
        
        // 设置内容
        icon.setImageResource(iconRes)
        title.setText(titleRes)
        message.setText(messageRes)
        
        // 添加说明文字
        val rationaleText = when (permissionType) {
            "camera" -> "\n\n为了提供放大镜功能，应用需要访问您的相机。我们不会保存或上传任何图像数据。"
            "storage" -> "\n\n为了保存放大后的截图，应用需要访问您的存储空间。您可以随时删除保存的图片。"
            else -> ""
        }
        
        message.text = "${activity.getString(messageRes)}$rationaleText"
        
        val dialog = AlertDialog.Builder(activity)
            .setView(dialogView)
            .setCancelable(true)
            .create()
        
        cancelButton.setOnClickListener {
            dialog.dismiss()
            listener.onPermissionDenied()
        }
        
        grantButton.setOnClickListener {
            dialog.dismiss()
            listener.onPermissionGranted()
        }
        
        dialog.show()
    }
    
    /**
     * 打开应用设置页面
     */
    private fun openAppSettings() {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", activity.packageName, null)
            }
            activity.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开应用设置，打开通用设置
            val intent = Intent(Settings.ACTION_SETTINGS)
            activity.startActivity(intent)
        }
    }
    
    /**
     * 显示功能限制提示
     */
    fun showFeatureLimitationDialog(feature: String) {
        val message = when (feature) {
            "camera" -> "没有相机权限，无法使用放大镜功能。"
            "storage" -> "没有存储权限，无法保存截图。您仍可以使用放大镜功能。"
            else -> "某些功能可能受到限制。"
        }
        
        AlertDialog.Builder(activity)
            .setTitle("功能受限")
            .setMessage(message)
            .setPositiveButton("我知道了") { dialog, _ ->
                dialog.dismiss()
            }
            .setNegativeButton("前往设置") { dialog, _ ->
                dialog.dismiss()
                openAppSettings()
            }
            .show()
    }
}
