package com.magnifyingglass.app

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.magnifyingglass.app.databinding.ActivityOnboardingBinding

class OnboardingActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityOnboardingBinding
    private lateinit var onboardingAdapter: OnboardingAdapter
    private var currentPage = 0
    
    private val onboardingPages = listOf(
        OnboardingPage(
            R.drawable.ic_zoom_in,
            R.string.onboarding_title_1,
            R.string.onboarding_desc_1
        ),
        OnboardingPage(
            R.drawable.ic_camera,
            R.string.onboarding_title_2,
            R.string.onboarding_desc_2
        ),
        OnboardingPage(
            R.drawable.ic_flashlight,
            R.string.onboarding_title_3,
            R.string.onboarding_desc_3
        )
    )
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOnboardingBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupViewPager()
        setupButtons()
        setupIndicators()
    }
    
    private fun setupViewPager() {
        onboardingAdapter = OnboardingAdapter(onboardingPages)
        binding.viewPager.adapter = onboardingAdapter
        
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                currentPage = position
                updateIndicators()
                updateButtons()
            }
        })
    }
    
    private fun setupButtons() {
        binding.skipButton.setOnClickListener {
            finishOnboarding()
        }
        
        binding.nextButton.setOnClickListener {
            if (currentPage < onboardingPages.size - 1) {
                binding.viewPager.currentItem = currentPage + 1
            } else {
                finishOnboarding()
            }
        }
    }
    
    private fun setupIndicators() {
        val indicators = Array(onboardingPages.size) { ImageView(this) }
        val layoutParams = LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        ).apply {
            setMargins(8, 0, 8, 0)
        }
        
        for (i in indicators.indices) {
            indicators[i].apply {
                setImageDrawable(ContextCompat.getDrawable(this@OnboardingActivity, R.drawable.ic_mode))
                this.layoutParams = layoutParams
                scaleX = 0.5f
                scaleY = 0.5f
            }
            binding.indicatorLayout.addView(indicators[i])
        }
        
        updateIndicators()
    }
    
    private fun updateIndicators() {
        for (i in 0 until binding.indicatorLayout.childCount) {
            val indicator = binding.indicatorLayout.getChildAt(i) as ImageView
            if (i == currentPage) {
                indicator.alpha = 1.0f
                indicator.setColorFilter(ContextCompat.getColor(this, R.color.primary))
            } else {
                indicator.alpha = 0.5f
                indicator.setColorFilter(ContextCompat.getColor(this, R.color.inactive))
            }
        }
    }
    
    private fun updateButtons() {
        if (currentPage == onboardingPages.size - 1) {
            binding.nextButton.text = getString(R.string.onboarding_start)
        } else {
            binding.nextButton.text = getString(R.string.onboarding_next)
        }
    }
    
    private fun finishOnboarding() {
        // 标记引导已完成
        val prefs = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        prefs.edit().putBoolean("onboarding_completed", true).apply()
        
        // 启动主活动
        startActivity(Intent(this, MainActivity::class.java))
        finish()
    }
    
    data class OnboardingPage(
        val iconRes: Int,
        val titleRes: Int,
        val descriptionRes: Int
    )
    
    private class OnboardingAdapter(
        private val pages: List<OnboardingPage>
    ) : RecyclerView.Adapter<OnboardingAdapter.OnboardingViewHolder>() {
        
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OnboardingViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.onboarding_page, parent, false)
            return OnboardingViewHolder(view)
        }
        
        override fun onBindViewHolder(holder: OnboardingViewHolder, position: Int) {
            holder.bind(pages[position])
        }
        
        override fun getItemCount(): Int = pages.size
        
        class OnboardingViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            private val iconImageView: ImageView = itemView.findViewById(R.id.iconImageView)
            private val titleTextView: TextView = itemView.findViewById(R.id.titleTextView)
            private val descriptionTextView: TextView = itemView.findViewById(R.id.descriptionTextView)
            
            fun bind(page: OnboardingPage) {
                iconImageView.setImageResource(page.iconRes)
                titleTextView.setText(page.titleRes)
                descriptionTextView.setText(page.descriptionRes)
            }
        }
    }
}
