// Generated by view binder compiler. Do not edit!
package com.magnifyingglass.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.magnifyingglass.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogPermissionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button cancelButton;

  @NonNull
  public final Button grantButton;

  @NonNull
  public final ImageView permissionIcon;

  @NonNull
  public final TextView permissionMessage;

  @NonNull
  public final TextView permissionTitle;

  private DialogPermissionBinding(@NonNull LinearLayout rootView, @NonNull Button cancelButton,
      @NonNull Button grantButton, @NonNull ImageView permissionIcon,
      @NonNull TextView permissionMessage, @NonNull TextView permissionTitle) {
    this.rootView = rootView;
    this.cancelButton = cancelButton;
    this.grantButton = grantButton;
    this.permissionIcon = permissionIcon;
    this.permissionMessage = permissionMessage;
    this.permissionTitle = permissionTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogPermissionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogPermissionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_permission, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogPermissionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancelButton;
      Button cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.grantButton;
      Button grantButton = ViewBindings.findChildViewById(rootView, id);
      if (grantButton == null) {
        break missingId;
      }

      id = R.id.permissionIcon;
      ImageView permissionIcon = ViewBindings.findChildViewById(rootView, id);
      if (permissionIcon == null) {
        break missingId;
      }

      id = R.id.permissionMessage;
      TextView permissionMessage = ViewBindings.findChildViewById(rootView, id);
      if (permissionMessage == null) {
        break missingId;
      }

      id = R.id.permissionTitle;
      TextView permissionTitle = ViewBindings.findChildViewById(rootView, id);
      if (permissionTitle == null) {
        break missingId;
      }

      return new DialogPermissionBinding((LinearLayout) rootView, cancelButton, grantButton,
          permissionIcon, permissionMessage, permissionTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
