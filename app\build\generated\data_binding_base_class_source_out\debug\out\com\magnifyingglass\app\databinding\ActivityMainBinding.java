// Generated by view binder compiler. Do not edit!
package com.magnifyingglass.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.magnifyingglass.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final LinearLayout bottomControlLayout;

  @NonNull
  public final TextView brightnessText;

  @NonNull
  public final ImageButton enhanceButton;

  @NonNull
  public final ImageButton flashlightButton;

  @NonNull
  public final ImageButton freezeButton;

  @NonNull
  public final LinearLayout frozenButtonsContainer;

  @NonNull
  public final ImageView frozenImageView;

  @NonNull
  public final ImageButton modeButton;

  @NonNull
  public final PreviewView previewView;

  @NonNull
  public final ImageButton saveButton;

  @NonNull
  public final TextView statusText;

  @NonNull
  public final LinearLayout topInfoLayout;

  @NonNull
  public final ImageButton zoomInButton;

  @NonNull
  public final TextView zoomLevelText;

  @NonNull
  public final ImageButton zoomOutButton;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView, @NonNull ImageButton backButton,
      @NonNull LinearLayout bottomControlLayout, @NonNull TextView brightnessText,
      @NonNull ImageButton enhanceButton, @NonNull ImageButton flashlightButton,
      @NonNull ImageButton freezeButton, @NonNull LinearLayout frozenButtonsContainer,
      @NonNull ImageView frozenImageView, @NonNull ImageButton modeButton,
      @NonNull PreviewView previewView, @NonNull ImageButton saveButton,
      @NonNull TextView statusText, @NonNull LinearLayout topInfoLayout,
      @NonNull ImageButton zoomInButton, @NonNull TextView zoomLevelText,
      @NonNull ImageButton zoomOutButton) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.bottomControlLayout = bottomControlLayout;
    this.brightnessText = brightnessText;
    this.enhanceButton = enhanceButton;
    this.flashlightButton = flashlightButton;
    this.freezeButton = freezeButton;
    this.frozenButtonsContainer = frozenButtonsContainer;
    this.frozenImageView = frozenImageView;
    this.modeButton = modeButton;
    this.previewView = previewView;
    this.saveButton = saveButton;
    this.statusText = statusText;
    this.topInfoLayout = topInfoLayout;
    this.zoomInButton = zoomInButton;
    this.zoomLevelText = zoomLevelText;
    this.zoomOutButton = zoomOutButton;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backButton;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.bottomControlLayout;
      LinearLayout bottomControlLayout = ViewBindings.findChildViewById(rootView, id);
      if (bottomControlLayout == null) {
        break missingId;
      }

      id = R.id.brightnessText;
      TextView brightnessText = ViewBindings.findChildViewById(rootView, id);
      if (brightnessText == null) {
        break missingId;
      }

      id = R.id.enhanceButton;
      ImageButton enhanceButton = ViewBindings.findChildViewById(rootView, id);
      if (enhanceButton == null) {
        break missingId;
      }

      id = R.id.flashlightButton;
      ImageButton flashlightButton = ViewBindings.findChildViewById(rootView, id);
      if (flashlightButton == null) {
        break missingId;
      }

      id = R.id.freezeButton;
      ImageButton freezeButton = ViewBindings.findChildViewById(rootView, id);
      if (freezeButton == null) {
        break missingId;
      }

      id = R.id.frozenButtonsContainer;
      LinearLayout frozenButtonsContainer = ViewBindings.findChildViewById(rootView, id);
      if (frozenButtonsContainer == null) {
        break missingId;
      }

      id = R.id.frozenImageView;
      ImageView frozenImageView = ViewBindings.findChildViewById(rootView, id);
      if (frozenImageView == null) {
        break missingId;
      }

      id = R.id.modeButton;
      ImageButton modeButton = ViewBindings.findChildViewById(rootView, id);
      if (modeButton == null) {
        break missingId;
      }

      id = R.id.previewView;
      PreviewView previewView = ViewBindings.findChildViewById(rootView, id);
      if (previewView == null) {
        break missingId;
      }

      id = R.id.saveButton;
      ImageButton saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      id = R.id.topInfoLayout;
      LinearLayout topInfoLayout = ViewBindings.findChildViewById(rootView, id);
      if (topInfoLayout == null) {
        break missingId;
      }

      id = R.id.zoomInButton;
      ImageButton zoomInButton = ViewBindings.findChildViewById(rootView, id);
      if (zoomInButton == null) {
        break missingId;
      }

      id = R.id.zoomLevelText;
      TextView zoomLevelText = ViewBindings.findChildViewById(rootView, id);
      if (zoomLevelText == null) {
        break missingId;
      }

      id = R.id.zoomOutButton;
      ImageButton zoomOutButton = ViewBindings.findChildViewById(rootView, id);
      if (zoomOutButton == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, backButton, bottomControlLayout,
          brightnessText, enhanceButton, flashlightButton, freezeButton, frozenButtonsContainer,
          frozenImageView, modeButton, previewView, saveButton, statusText, topInfoLayout,
          zoomInButton, zoomLevelText, zoomOutButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
