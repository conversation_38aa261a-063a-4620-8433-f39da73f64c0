@echo off
echo ========================================
echo 🚀 高级AI图像增强测试
echo ========================================

set ADB=C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe

echo.
echo 1. 检查设备连接...
%ADB% devices
echo.

echo 2. 安装最新APK...
%ADB% install -r "app\build\outputs\apk\debug\app-debug.apk"
if %ERRORLEVEL% EQU 0 (
    echo ✅ APK安装成功
) else (
    echo ❌ APK安装失败
    pause
    exit /b 1
)
echo.

echo 3. 启动应用...
%ADB% shell am start -n com.magnifyingglass.app/.SplashActivity
if %ERRORLEVEL% EQU 0 (
    echo ✅ 应用启动成功
) else (
    echo ❌ 应用启动失败
    pause
    exit /b 1
)
echo.

echo ========================================
echo 🧪 AI增强功能测试指南
echo ========================================
echo.
echo 测试步骤：
echo 1. 等待2秒启动画面完成
echo 2. 进入放大镜界面（圆圈内正常视频，外部磨砂）
echo 3. 长按屏幕冻结画面
echo 4. 点击右下角的增强按钮
echo 5. 选择"高级AI算法"
echo 6. 观察处理进度条
echo 7. 查看增强后的效果
echo.
echo 预期效果：
echo ✓ 图像更清晰锐利
echo ✓ 边缘增强明显
echo ✓ 颜色更鲜艳
echo ✓ 细节更丰富
echo.

echo 开始监控日志...
echo 按 Ctrl+C 停止监控
echo ========================================
%ADB% logcat -s "TrueRealESRGAN" "MainActivity" "MagnifierOverlay"
