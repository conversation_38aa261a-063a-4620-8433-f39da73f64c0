1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.magnifyingglass.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- 相机权限 -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:6:22-62
13
14    <!-- 闪光灯权限 -->
15    <uses-feature
15-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:5-91
16        android:name="android.hardware.camera.flash"
16-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:19-63
17        android:required="false" />
17-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:64-88
18
19    <!-- 存储权限 -->
20    <uses-permission
20-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:12:5-13:38
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:12:22-78
22        android:maxSdkVersion="28" />
22-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:13:9-35
23    <uses-permission
23-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:14:5-15:38
24        android:name="android.permission.READ_EXTERNAL_STORAGE"
24-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:14:22-77
25        android:maxSdkVersion="32" />
25-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:15:9-35
26
27    <!-- Android 13+ 媒体权限 -->
28    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
28-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:18:5-76
28-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:18:22-73
29    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
29-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:19:5-75
29-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:19:22-72
30
31    <!-- 相机硬件要求 -->
32    <uses-feature
32-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:22:5-84
33        android:name="android.hardware.camera"
33-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:22:19-57
34        android:required="true" />
34-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:22:58-81
35    <uses-feature
35-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:23:5-95
36        android:name="android.hardware.camera.autofocus"
36-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:23:19-67
37        android:required="false" />
37-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:23:68-92
38
39    <queries>
39-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:22:5-26:15
40        <intent>
40-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:23:9-25:18
41            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
41-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:13-86
41-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:21-83
42        </intent>
43    </queries>
44
45    <permission
45-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
46        android:name="com.magnifyingglass.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.magnifyingglass.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
50
51    <application
51-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:25:5-53:19
52        android:allowBackup="true"
52-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:26:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:27:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="true"
57        android:fullBackupContent="@xml/backup_rules"
57-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:28:9-54
58        android:icon="@drawable/ic_app_icon_with_border"
58-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:29:9-57
59        android:label="@string/app_name"
59-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:30:9-41
60        android:roundIcon="@drawable/ic_app_icon_with_border"
60-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:31:9-62
61        android:supportsRtl="true"
61-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:32:9-35
62        android:theme="@style/Theme.MagnifyingGlass" >
62-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:33:9-53
63        <activity
63-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:36:9-46:20
64            android:name="com.magnifyingglass.app.MainActivity"
64-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:37:13-41
65            android:exported="true"
65-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:38:13-36
66            android:launchMode="singleTop"
66-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:41:13-43
67            android:screenOrientation="portrait"
67-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:39:13-49
68            android:theme="@style/Theme.MagnifyingGlass.NoActionBar" >
68-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:40:13-69
69            <intent-filter>
69-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:42:13-45:29
70                <action android:name="android.intent.action.MAIN" />
70-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:43:17-69
70-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:43:25-66
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:44:17-77
72-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:44:27-74
73            </intent-filter>
74        </activity>
75        <activity
75-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:48:9-52:72
76            android:name="com.magnifyingglass.app.OnboardingActivity"
76-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:49:13-47
77            android:exported="false"
77-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:50:13-37
78            android:screenOrientation="portrait"
78-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:51:13-49
79            android:theme="@style/Theme.MagnifyingGlass.NoActionBar" />
79-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:52:13-69
80
81        <uses-library
81-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:29:9-31:40
82            android:name="androidx.camera.extensions.impl"
82-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:30:13-59
83            android:required="false" />
83-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:31:13-37
84
85        <service
85-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:24:9-33:19
86            android:name="androidx.camera.core.impl.MetadataHolderService"
86-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:25:13-75
87            android:enabled="false"
87-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:26:13-36
88            android:exported="false" >
88-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:27:13-37
89            <meta-data
89-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:30:13-32:89
90                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
90-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:31:17-103
91                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
91-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:32:17-86
92        </service>
93
94        <provider
94-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
95            android:name="androidx.startup.InitializationProvider"
95-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
96            android:authorities="com.magnifyingglass.app.debug.androidx-startup"
96-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
97            android:exported="false" >
97-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
98            <meta-data
98-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.emoji2.text.EmojiCompatInitializer"
99-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
100                android:value="androidx.startup" />
100-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
102-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
103                android:value="androidx.startup" />
103-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
106                android:value="androidx.startup" />
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
107        </provider>
108
109        <receiver
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
110            android:name="androidx.profileinstaller.ProfileInstallReceiver"
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
111            android:directBootAware="false"
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
112            android:enabled="true"
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
113            android:exported="true"
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
114            android:permission="android.permission.DUMP" >
114-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
116                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
119                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
122                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
125                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
126            </intent-filter>
127        </receiver>
128    </application>
129
130</manifest>
