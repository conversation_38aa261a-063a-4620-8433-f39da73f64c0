1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.magnifyingglass.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- 相机权限 -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:6:22-62
13
14    <!-- 闪光灯权限 -->
15    <uses-feature
15-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:5-91
16        android:name="android.hardware.camera.flash"
16-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:19-63
17        android:required="false" />
17-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:64-88
18
19    <!-- 存储权限 -->
20    <uses-permission
20-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:12:5-13:38
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:12:22-78
22        android:maxSdkVersion="28" />
22-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:13:9-35
23    <uses-permission
23-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:14:5-15:38
24        android:name="android.permission.READ_EXTERNAL_STORAGE"
24-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:14:22-77
25        android:maxSdkVersion="32" />
25-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:15:9-35
26
27    <!-- Android 13+ 媒体权限 -->
28    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
28-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:18:5-76
28-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:18:22-73
29    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
29-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:19:5-75
29-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:19:22-72
30
31    <!-- 相机硬件要求 -->
32    <uses-feature
32-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:22:5-84
33        android:name="android.hardware.camera"
33-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:22:19-57
34        android:required="true" />
34-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:22:58-81
35    <uses-feature
35-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:23:5-95
36        android:name="android.hardware.camera.autofocus"
36-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:23:19-67
37        android:required="false" />
37-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:23:68-92
38
39    <queries>
39-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:22:5-26:15
40        <intent>
40-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:23:9-25:18
41            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
41-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:13-86
41-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:21-83
42        </intent>
43    </queries>
44
45    <permission
45-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
46        android:name="com.magnifyingglass.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.magnifyingglass.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
50
51    <application
51-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:25:5-59:19
52        android:allowBackup="true"
52-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:26:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:27:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="true"
57        android:fullBackupContent="@xml/backup_rules"
57-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:28:9-54
58        android:icon="@drawable/ic_magnifying_glass"
58-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:29:9-53
59        android:label="@string/app_name"
59-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:30:9-41
60        android:roundIcon="@drawable/ic_magnifying_glass"
60-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:31:9-58
61        android:supportsRtl="true"
61-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:32:9-35
62        android:theme="@style/Theme.MagnifyingGlass" >
62-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:33:9-53
63        <activity
63-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:36:9-45:20
64            android:name="com.magnifyingglass.app.SplashActivity"
64-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:37:13-43
65            android:exported="true"
65-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:38:13-36
66            android:screenOrientation="portrait"
66-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:39:13-49
67            android:theme="@style/Theme.MagnifyingGlass.NoActionBar" >
67-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:40:13-69
68            <intent-filter>
68-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:41:13-44:29
69                <action android:name="android.intent.action.MAIN" />
69-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:42:17-69
69-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:42:25-66
70
71                <category android:name="android.intent.category.LAUNCHER" />
71-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:43:17-77
71-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:43:27-74
72            </intent-filter>
73        </activity>
74        <activity
74-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:47:9-52:46
75            android:name="com.magnifyingglass.app.MainActivity"
75-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:48:13-41
76            android:exported="false"
76-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:49:13-37
77            android:launchMode="singleTop"
77-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:52:13-43
78            android:screenOrientation="portrait"
78-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:50:13-49
79            android:theme="@style/Theme.MagnifyingGlass.NoActionBar" />
79-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:51:13-69
80        <activity
80-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:54:9-58:72
81            android:name="com.magnifyingglass.app.OnboardingActivity"
81-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:55:13-47
82            android:exported="false"
82-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:56:13-37
83            android:screenOrientation="portrait"
83-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:57:13-49
84            android:theme="@style/Theme.MagnifyingGlass.NoActionBar" />
84-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:58:13-69
85
86        <uses-library
86-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:29:9-31:40
87            android:name="androidx.camera.extensions.impl"
87-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:30:13-59
88            android:required="false" />
88-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:31:13-37
89
90        <service
90-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:24:9-33:19
91            android:name="androidx.camera.core.impl.MetadataHolderService"
91-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:25:13-75
92            android:enabled="false"
92-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:26:13-36
93            android:exported="false" >
93-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:27:13-37
94            <meta-data
94-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:30:13-32:89
95                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
95-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:31:17-103
96                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
96-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:32:17-86
97        </service>
98
99        <provider
99-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
100            android:name="androidx.startup.InitializationProvider"
100-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
101            android:authorities="com.magnifyingglass.app.debug.androidx-startup"
101-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
102            android:exported="false" >
102-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
103            <meta-data
103-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.emoji2.text.EmojiCompatInitializer"
104-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
105                android:value="androidx.startup" />
105-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
106            <meta-data
106-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
107                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
107-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
108                android:value="androidx.startup" />
108-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
109            <meta-data
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
110                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
111                android:value="androidx.startup" />
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
112        </provider>
113
114        <receiver
114-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
115            android:name="androidx.profileinstaller.ProfileInstallReceiver"
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
116            android:directBootAware="false"
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
117            android:enabled="true"
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
118            android:exported="true"
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
119            android:permission="android.permission.DUMP" >
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
120            <intent-filter>
120-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
121                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
122            </intent-filter>
123            <intent-filter>
123-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
124                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
125            </intent-filter>
126            <intent-filter>
126-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
127                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
128            </intent-filter>
129            <intent-filter>
129-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
130                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
131            </intent-filter>
132        </receiver>
133    </application>
134
135</manifest>
