1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.magnifyingglass.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- 相机权限 -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:6:22-62
13
14    <!-- 闪光灯权限 -->
15    <uses-feature
15-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:5-91
16        android:name="android.hardware.camera.flash"
16-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:19-63
17        android:required="false" />
17-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:64-88
18
19    <!-- 存储权限 (Android 10以下需要) -->
20    <uses-permission
20-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:12:5-13:38
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:12:22-78
22        android:maxSdkVersion="28" />
22-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:13:9-35
23
24    <!-- 相机硬件要求 -->
25    <uses-feature
25-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:16:5-84
26        android:name="android.hardware.camera"
26-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:16:19-57
27        android:required="true" />
27-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:16:58-81
28    <uses-feature
28-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:17:5-95
29        android:name="android.hardware.camera.autofocus"
29-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:17:19-67
30        android:required="false" />
30-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:17:68-92
31
32    <queries>
32-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:22:5-26:15
33        <intent>
33-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:23:9-25:18
34            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
34-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:13-86
34-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:21-83
35        </intent>
36    </queries>
37
38    <permission
38-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
39        android:name="com.magnifyingglass.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.magnifyingglass.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
43
44    <application
44-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:19:5-47:19
45        android:allowBackup="true"
45-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:20:9-35
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
47        android:dataExtractionRules="@xml/data_extraction_rules"
47-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:21:9-65
48        android:debuggable="true"
49        android:extractNativeLibs="true"
50        android:fullBackupContent="@xml/backup_rules"
50-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:22:9-54
51        android:icon="@drawable/ic_app_icon"
51-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:23:9-45
52        android:label="@string/app_name"
52-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:24:9-41
53        android:roundIcon="@drawable/ic_app_icon"
53-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:25:9-50
54        android:supportsRtl="true"
54-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:26:9-35
55        android:theme="@style/Theme.MagnifyingGlass" >
55-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:27:9-53
56        <activity
56-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:30:9-40:20
57            android:name="com.magnifyingglass.app.MainActivity"
57-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:31:13-41
58            android:exported="true"
58-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:32:13-36
59            android:launchMode="singleTop"
59-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:35:13-43
60            android:screenOrientation="portrait"
60-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:33:13-49
61            android:theme="@style/SplashTheme" >
61-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:34:13-47
62            <intent-filter>
62-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:36:13-39:29
63                <action android:name="android.intent.action.MAIN" />
63-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:37:17-69
63-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:37:25-66
64
65                <category android:name="android.intent.category.LAUNCHER" />
65-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:38:17-77
65-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:38:27-74
66            </intent-filter>
67        </activity>
68        <activity
68-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:42:9-46:72
69            android:name="com.magnifyingglass.app.OnboardingActivity"
69-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:43:13-47
70            android:exported="false"
70-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:44:13-37
71            android:screenOrientation="portrait"
71-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:45:13-49
72            android:theme="@style/Theme.MagnifyingGlass.NoActionBar" />
72-->C:\Magnifying Glass\app\src\main\AndroidManifest.xml:46:13-69
73
74        <uses-library
74-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:29:9-31:40
75            android:name="androidx.camera.extensions.impl"
75-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:30:13-59
76            android:required="false" />
76-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:31:13-37
77
78        <service
78-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:24:9-33:19
79            android:name="androidx.camera.core.impl.MetadataHolderService"
79-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:25:13-75
80            android:enabled="false"
80-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:26:13-36
81            android:exported="false" >
81-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:27:13-37
82            <meta-data
82-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:30:13-32:89
83                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
83-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:31:17-103
84                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
84-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:32:17-86
85        </service>
86
87        <provider
87-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
88            android:name="androidx.startup.InitializationProvider"
88-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
89            android:authorities="com.magnifyingglass.app.debug.androidx-startup"
89-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
90            android:exported="false" >
90-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
91            <meta-data
91-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
92                android:name="androidx.emoji2.text.EmojiCompatInitializer"
92-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
93                android:value="androidx.startup" />
93-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
94            <meta-data
94-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
95-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
96                android:value="androidx.startup" />
96-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
97            <meta-data
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
98                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
99                android:value="androidx.startup" />
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
100        </provider>
101
102        <receiver
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
103            android:name="androidx.profileinstaller.ProfileInstallReceiver"
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
104            android:directBootAware="false"
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
105            android:enabled="true"
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
106            android:exported="true"
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
107            android:permission="android.permission.DUMP" >
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
109                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
110            </intent-filter>
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
112                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
113            </intent-filter>
114            <intent-filter>
114-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
115                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
116            </intent-filter>
117            <intent-filter>
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
118                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
119            </intent-filter>
120        </receiver>
121    </application>
122
123</manifest>
