package com.magnifyingglass.app.utils

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.text.SimpleDateFormat
import java.util.*

class ImageSaver(private val context: Context) {
    
    companion object {
        private const val FOLDER_NAME = "MagnifyingGlass"
        private const val IMAGE_QUALITY = 90
        private const val DATE_FORMAT = "yyyyMMdd_HHmmss"
    }
    
    interface SaveListener {
        fun onSaveSuccess(uri: Uri?, filePath: String?)
        fun onSaveError(error: String)
        fun onSaveProgress(progress: Int)
    }
    
    /**
     * 保存图片到相册
     */
    suspend fun saveImageToGallery(
        bitmap: Bitmap,
        filename: String? = null,
        listener: SaveListener? = null
    ): Result<Pair<Uri?, String?>> = withContext(Dispatchers.IO) {
        try {
            listener?.onSaveProgress(10)
            
            val displayName = filename ?: generateFileName()
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10及以上使用MediaStore
                saveToMediaStore(bitmap, displayName, listener)
            } else {
                // Android 9及以下使用传统方式
                saveToExternalStorage(bitmap, displayName, listener)
            }
        } catch (e: Exception) {
            val errorMsg = "保存失败: ${e.message}"
            listener?.onSaveError(errorMsg)
            Result.failure(e)
        }
    }
    
    /**
     * 使用MediaStore保存图片 (Android 10+)
     */
    private suspend fun saveToMediaStore(
        bitmap: Bitmap,
        displayName: String,
        listener: SaveListener?
    ): Result<Pair<Uri?, String?>> = withContext(Dispatchers.IO) {
        try {
            listener?.onSaveProgress(30)
            
            val contentValues = ContentValues().apply {
                put(MediaStore.MediaColumns.DISPLAY_NAME, displayName)
                put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
                put(MediaStore.MediaColumns.RELATIVE_PATH, "Pictures/$FOLDER_NAME")
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    put(MediaStore.MediaColumns.IS_PENDING, 1)
                }
            }
            
            listener?.onSaveProgress(50)
            
            val uri = context.contentResolver.insert(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                contentValues
            )
            
            if (uri != null) {
                listener?.onSaveProgress(70)
                
                context.contentResolver.openOutputStream(uri)?.use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, outputStream)
                }
                
                // 标记文件不再pending
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    contentValues.clear()
                    contentValues.put(MediaStore.MediaColumns.IS_PENDING, 0)
                    context.contentResolver.update(uri, contentValues, null, null)
                }
                
                listener?.onSaveProgress(100)
                listener?.onSaveSuccess(uri, null)
                
                Result.success(Pair(uri, null))
            } else {
                throw Exception("无法创建媒体文件")
            }
        } catch (e: Exception) {
            val errorMsg = "MediaStore保存失败: ${e.message}"
            listener?.onSaveError(errorMsg)
            Result.failure(e)
        }
    }
    
    /**
     * 保存到外部存储 (Android 9及以下)
     */
    private suspend fun saveToExternalStorage(
        bitmap: Bitmap,
        displayName: String,
        listener: SaveListener?
    ): Result<Pair<Uri?, String?>> = withContext(Dispatchers.IO) {
        try {
            listener?.onSaveProgress(30)
            
            val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
            val appDir = File(picturesDir, FOLDER_NAME)
            
            if (!appDir.exists()) {
                appDir.mkdirs()
            }
            
            listener?.onSaveProgress(50)
            
            val file = File(appDir, displayName)
            
            FileOutputStream(file).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, outputStream)
            }
            
            listener?.onSaveProgress(80)
            
            // 通知媒体扫描器
            val contentValues = ContentValues().apply {
                put(MediaStore.Images.Media.DATA, file.absolutePath)
                put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
            }
            
            val uri = context.contentResolver.insert(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                contentValues
            )
            
            listener?.onSaveProgress(100)
            listener?.onSaveSuccess(uri, file.absolutePath)
            
            Result.success(Pair(uri, file.absolutePath))
        } catch (e: Exception) {
            val errorMsg = "外部存储保存失败: ${e.message}"
            listener?.onSaveError(errorMsg)
            Result.failure(e)
        }
    }
    
    /**
     * 保存到应用私有目录
     */
    suspend fun saveToPrivateStorage(
        bitmap: Bitmap,
        filename: String? = null
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val displayName = filename ?: generateFileName()
            val file = File(context.getExternalFilesDir(Environment.DIRECTORY_PICTURES), displayName)
            
            FileOutputStream(file).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, outputStream)
            }
            
            Result.success(file.absolutePath)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 生成文件名
     */
    private fun generateFileName(): String {
        val timestamp = SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).format(Date())
        return "magnifier_$timestamp.jpg"
    }
    
    /**
     * 获取图片保存目录
     */
    fun getSaveDirectory(): File? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ 使用MediaStore，没有直接的文件路径
            null
        } else {
            val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
            File(picturesDir, FOLDER_NAME)
        }
    }
    
    /**
     * 检查存储空间
     */
    fun checkStorageSpace(requiredBytes: Long): Boolean {
        return try {
            val externalDir = Environment.getExternalStorageDirectory()
            val freeBytes = externalDir.freeSpace
            freeBytes >= requiredBytes
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 估算图片大小
     */
    fun estimateImageSize(bitmap: Bitmap): Long {
        // 粗略估算JPEG压缩后的大小
        val pixels = bitmap.width * bitmap.height
        return (pixels * 0.5).toLong() // 假设每像素0.5字节
    }
    
    /**
     * 压缩图片
     */
    fun compressBitmap(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }
        
        val scaleWidth = maxWidth.toFloat() / width
        val scaleHeight = maxHeight.toFloat() / height
        val scale = minOf(scaleWidth, scaleHeight)
        
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    /**
     * 获取保存统计信息
     */
    fun getSaveStatistics(): SaveStatistics {
        val saveDir = getSaveDirectory()
        var fileCount = 0
        var totalSize = 0L
        
        saveDir?.let { dir ->
            if (dir.exists()) {
                dir.listFiles()?.forEach { file ->
                    if (file.isFile && file.name.endsWith(".jpg")) {
                        fileCount++
                        totalSize += file.length()
                    }
                }
            }
        }
        
        return SaveStatistics(fileCount, totalSize)
    }
    
    data class SaveStatistics(
        val fileCount: Int,
        val totalSize: Long
    ) {
        fun getTotalSizeMB(): Double = totalSize / (1024.0 * 1024.0)
    }
}
