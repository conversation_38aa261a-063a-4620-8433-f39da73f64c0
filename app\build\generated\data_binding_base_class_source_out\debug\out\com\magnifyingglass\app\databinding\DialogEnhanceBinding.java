// Generated by view binder compiler. Do not edit!
package com.magnifyingglass.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.magnifyingglass.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogEnhanceBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button applyButton;

  @NonNull
  public final SeekBar brightnessSeekBar;

  @NonNull
  public final TextView brightnessValue;

  @NonNull
  public final Button cancelButton;

  @NonNull
  public final SeekBar contrastSeekBar;

  @NonNull
  public final TextView contrastValue;

  @NonNull
  public final SeekBar noiseReductionSeekBar;

  @NonNull
  public final TextView noiseReductionValue;

  @NonNull
  public final Button resetButton;

  @NonNull
  public final SeekBar sharpenSeekBar;

  @NonNull
  public final TextView sharpenValue;

  private DialogEnhanceBinding(@NonNull LinearLayout rootView, @NonNull Button applyButton,
      @NonNull SeekBar brightnessSeekBar, @NonNull TextView brightnessValue,
      @NonNull Button cancelButton, @NonNull SeekBar contrastSeekBar,
      @NonNull TextView contrastValue, @NonNull SeekBar noiseReductionSeekBar,
      @NonNull TextView noiseReductionValue, @NonNull Button resetButton,
      @NonNull SeekBar sharpenSeekBar, @NonNull TextView sharpenValue) {
    this.rootView = rootView;
    this.applyButton = applyButton;
    this.brightnessSeekBar = brightnessSeekBar;
    this.brightnessValue = brightnessValue;
    this.cancelButton = cancelButton;
    this.contrastSeekBar = contrastSeekBar;
    this.contrastValue = contrastValue;
    this.noiseReductionSeekBar = noiseReductionSeekBar;
    this.noiseReductionValue = noiseReductionValue;
    this.resetButton = resetButton;
    this.sharpenSeekBar = sharpenSeekBar;
    this.sharpenValue = sharpenValue;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogEnhanceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogEnhanceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_enhance, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogEnhanceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.applyButton;
      Button applyButton = ViewBindings.findChildViewById(rootView, id);
      if (applyButton == null) {
        break missingId;
      }

      id = R.id.brightnessSeekBar;
      SeekBar brightnessSeekBar = ViewBindings.findChildViewById(rootView, id);
      if (brightnessSeekBar == null) {
        break missingId;
      }

      id = R.id.brightnessValue;
      TextView brightnessValue = ViewBindings.findChildViewById(rootView, id);
      if (brightnessValue == null) {
        break missingId;
      }

      id = R.id.cancelButton;
      Button cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.contrastSeekBar;
      SeekBar contrastSeekBar = ViewBindings.findChildViewById(rootView, id);
      if (contrastSeekBar == null) {
        break missingId;
      }

      id = R.id.contrastValue;
      TextView contrastValue = ViewBindings.findChildViewById(rootView, id);
      if (contrastValue == null) {
        break missingId;
      }

      id = R.id.noiseReductionSeekBar;
      SeekBar noiseReductionSeekBar = ViewBindings.findChildViewById(rootView, id);
      if (noiseReductionSeekBar == null) {
        break missingId;
      }

      id = R.id.noiseReductionValue;
      TextView noiseReductionValue = ViewBindings.findChildViewById(rootView, id);
      if (noiseReductionValue == null) {
        break missingId;
      }

      id = R.id.resetButton;
      Button resetButton = ViewBindings.findChildViewById(rootView, id);
      if (resetButton == null) {
        break missingId;
      }

      id = R.id.sharpenSeekBar;
      SeekBar sharpenSeekBar = ViewBindings.findChildViewById(rootView, id);
      if (sharpenSeekBar == null) {
        break missingId;
      }

      id = R.id.sharpenValue;
      TextView sharpenValue = ViewBindings.findChildViewById(rootView, id);
      if (sharpenValue == null) {
        break missingId;
      }

      return new DialogEnhanceBinding((LinearLayout) rootView, applyButton, brightnessSeekBar,
          brightnessValue, cancelButton, contrastSeekBar, contrastValue, noiseReductionSeekBar,
          noiseReductionValue, resetButton, sharpenSeekBar, sharpenValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
