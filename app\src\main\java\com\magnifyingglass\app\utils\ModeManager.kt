package com.magnifyingglass.app.utils

import android.content.Context
import android.content.SharedPreferences
import com.magnifyingglass.app.R

class ModeManager(context: Context) {
    
    companion object {
        private const val PREFS_NAME = "mode_preferences"
        private const val KEY_CURRENT_MODE = "current_mode"
        private const val KEY_LAST_ZOOM_RATIO = "last_zoom_ratio"
        private const val KEY_LAST_BRIGHTNESS = "last_brightness"
    }
    
    enum class CameraMode(val displayNameRes: Int, val description: String) {
        NORMAL(R.string.normal_mode, "标准放大模式，适合日常使用"),
        READING(R.string.reading_mode, "阅读模式，降低饱和度，提高对比度，适合长时间阅读"),
        HIGH_CONTRAST(R.string.high_contrast_mode, "高对比度模式，黑底白字，适合低视力用户"),
        LOW_LIGHT(R.string.low_light_mode, "低光模式，自动开启闪光灯和最大亮度")
    }
    
    interface ModeChangeListener {
        fun onModeChanged(newMode: CameraMode, oldMode: CameraMode)
        fun onModeConfigurationChanged(mode: CameraMode, config: ModeConfiguration)
    }
    
    data class ModeConfiguration(
        val filterType: ImageFilterProcessor.FilterType,
        val stabilizationEnabled: Boolean,
        val flashlightEnabled: Boolean,
        val brightnessLevel: Int,
        val autoFocusMode: AutoFocusMode = AutoFocusMode.CONTINUOUS
    )
    
    enum class AutoFocusMode {
        CONTINUOUS,    // 连续自动对焦
        SINGLE,        // 单次对焦
        MANUAL         // 手动对焦
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    private var currentMode: CameraMode = CameraMode.NORMAL
    private var modeChangeListener: ModeChangeListener? = null
    
    // 模式配置
    private val modeConfigurations = mapOf(
        CameraMode.NORMAL to ModeConfiguration(
            filterType = ImageFilterProcessor.FilterType.NONE,
            stabilizationEnabled = true,
            flashlightEnabled = false,
            brightnessLevel = 100
        ),
        CameraMode.READING to ModeConfiguration(
            filterType = ImageFilterProcessor.FilterType.READING_MODE,
            stabilizationEnabled = false, // 阅读时关闭防抖以节省电量
            flashlightEnabled = false,
            brightnessLevel = 120,
            autoFocusMode = AutoFocusMode.SINGLE // 阅读时使用单次对焦
        ),
        CameraMode.HIGH_CONTRAST to ModeConfiguration(
            filterType = ImageFilterProcessor.FilterType.HIGH_CONTRAST,
            stabilizationEnabled = true,
            flashlightEnabled = false,
            brightnessLevel = 150
        ),
        CameraMode.LOW_LIGHT to ModeConfiguration(
            filterType = ImageFilterProcessor.FilterType.NONE,
            stabilizationEnabled = true,
            flashlightEnabled = true,
            brightnessLevel = 200
        )
    )
    
    init {
        loadCurrentMode()
    }
    
    fun setModeChangeListener(listener: ModeChangeListener) {
        this.modeChangeListener = listener
    }
    
    /**
     * 切换到下一个模式
     */
    fun switchToNextMode() {
        val modes = CameraMode.values()
        val currentIndex = modes.indexOf(currentMode)
        val nextIndex = (currentIndex + 1) % modes.size
        setMode(modes[nextIndex])
    }
    
    /**
     * 设置特定模式
     */
    fun setMode(mode: CameraMode) {
        val oldMode = currentMode
        currentMode = mode
        saveCurrentMode()
        
        modeChangeListener?.onModeChanged(currentMode, oldMode)
        
        val config = getModeConfiguration(mode)
        modeChangeListener?.onModeConfigurationChanged(mode, config)
    }
    
    /**
     * 获取当前模式
     */
    fun getCurrentMode(): CameraMode = currentMode
    
    /**
     * 获取模式配置
     */
    fun getModeConfiguration(mode: CameraMode): ModeConfiguration {
        return modeConfigurations[mode] ?: modeConfigurations[CameraMode.NORMAL]!!
    }
    
    /**
     * 获取当前模式配置
     */
    fun getCurrentModeConfiguration(): ModeConfiguration {
        return getModeConfiguration(currentMode)
    }
    
    /**
     * 检查模式是否适合当前环境
     */
    fun isModeSuitableForEnvironment(mode: CameraMode, ambientLight: Float): Boolean {
        return when (mode) {
            CameraMode.LOW_LIGHT -> ambientLight < 0.3f
            CameraMode.HIGH_CONTRAST -> true // 高对比度模式适合所有环境
            CameraMode.READING -> true // 阅读模式适合所有环境
            CameraMode.NORMAL -> ambientLight >= 0.3f
        }
    }
    
    /**
     * 根据环境推荐模式
     */
    fun recommendModeForEnvironment(ambientLight: Float): CameraMode {
        return when {
            ambientLight < 0.2f -> CameraMode.LOW_LIGHT
            ambientLight < 0.4f -> CameraMode.HIGH_CONTRAST
            else -> CameraMode.NORMAL
        }
    }
    
    /**
     * 获取模式使用统计
     */
    fun getModeUsageStatistics(): Map<CameraMode, Int> {
        val stats = mutableMapOf<CameraMode, Int>()
        CameraMode.values().forEach { mode ->
            val count = sharedPreferences.getInt("usage_${mode.name}", 0)
            stats[mode] = count
        }
        return stats
    }
    
    /**
     * 记录模式使用
     */
    fun recordModeUsage(mode: CameraMode) {
        val currentCount = sharedPreferences.getInt("usage_${mode.name}", 0)
        sharedPreferences.edit()
            .putInt("usage_${mode.name}", currentCount + 1)
            .apply()
    }
    
    /**
     * 获取最常用的模式
     */
    fun getMostUsedMode(): CameraMode {
        val stats = getModeUsageStatistics()
        return stats.maxByOrNull { it.value }?.key ?: CameraMode.NORMAL
    }
    
    /**
     * 保存模式相关的设置
     */
    fun saveModeSettings(zoomRatio: Float, brightness: Int) {
        sharedPreferences.edit()
            .putFloat(KEY_LAST_ZOOM_RATIO, zoomRatio)
            .putInt(KEY_LAST_BRIGHTNESS, brightness)
            .apply()
    }
    
    /**
     * 获取保存的设置
     */
    fun getSavedSettings(): Pair<Float, Int> {
        val zoomRatio = sharedPreferences.getFloat(KEY_LAST_ZOOM_RATIO, 1.0f)
        val brightness = sharedPreferences.getInt(KEY_LAST_BRIGHTNESS, 100)
        return Pair(zoomRatio, brightness)
    }
    
    /**
     * 重置所有模式设置
     */
    fun resetAllSettings() {
        sharedPreferences.edit().clear().apply()
        currentMode = CameraMode.NORMAL
    }
    
    /**
     * 获取模式切换建议
     */
    fun getModeSwitchSuggestion(currentZoom: Float, currentBrightness: Int): CameraMode? {
        return when {
            // 如果用户经常调高亮度，建议低光模式
            currentBrightness > 150 && currentMode != CameraMode.LOW_LIGHT -> CameraMode.LOW_LIGHT
            
            // 如果用户使用高倍放大，建议阅读模式
            currentZoom > 8.0f && currentMode != CameraMode.READING -> CameraMode.READING
            
            // 如果亮度很低，建议高对比度模式
            currentBrightness < 80 && currentMode != CameraMode.HIGH_CONTRAST -> CameraMode.HIGH_CONTRAST
            
            else -> null
        }
    }
    
    private fun loadCurrentMode() {
        val modeName = sharedPreferences.getString(KEY_CURRENT_MODE, CameraMode.NORMAL.name)
        currentMode = try {
            CameraMode.valueOf(modeName ?: CameraMode.NORMAL.name)
        } catch (e: IllegalArgumentException) {
            CameraMode.NORMAL
        }
    }
    
    private fun saveCurrentMode() {
        sharedPreferences.edit()
            .putString(KEY_CURRENT_MODE, currentMode.name)
            .apply()
        
        // 记录使用次数
        recordModeUsage(currentMode)
    }
}
