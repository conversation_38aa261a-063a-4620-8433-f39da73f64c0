<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">

    <!-- 外层方框 -->
    <path
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:pathData="M3,3 L21,3 L21,21 L3,21 Z" />

    <!-- 内层方框 -->
    <path
        android:strokeColor="#FFFFFF"
        android:strokeWidth="1.5"
        android:fillColor="#00000000"
        android:pathData="M6,6 L18,6 L18,18 L6,18 Z" />

    <!-- 中心增强符号 - 垂直线 -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M11,8 L13,8 L13,16 L11,16 Z" />

    <!-- 中心增强符号 - 水平线 -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M8,11 L16,11 L16,13 L8,13 Z" />

    <!-- 角落装饰点 -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M4,4 m-1,0 a1,1 0,1 1,2 0 a1,1 0,1 1,-2 0" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M20,4 m-1,0 a1,1 0,1 1,2 0 a1,1 0,1 1,-2 0" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M4,20 m-1,0 a1,1 0,1 1,2 0 a1,1 0,1 1,-2 0" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M20,20 m-1,0 a1,1 0,1 1,2 0 a1,1 0,1 1,-2 0" />

</vector>
