@echo off
echo Building and installing Magnifying Glass App...

set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set ADB=%ANDROID_HOME%\platform-tools\adb.exe
set BUILD_TOOLS=%ANDROID_HOME%\build-tools\36.0.0

echo Checking connected devices...
%ADB% devices

echo.
echo Attempting to build with Android Studio gradle...

REM Try to find and use Android Studio's gradle
set STUDIO_GRADLE="C:\Program Files\Android\Android Studio\plugins\gradle\lib\gradle.jar"

if exist %STUDIO_GRADLE% (
    echo Found Android Studio gradle
) else (
    echo Android Studio gradle not found at expected location
)

echo.
echo Please use Android Studio to build and install the app:
echo 1. Open Android Studio
echo 2. Open this project folder: %CD%
echo 3. Wait for gradle sync to complete
echo 4. Click "Run" button or press Shift+F10
echo.
echo Alternatively, if you have gradle in PATH, run:
echo gradlew assembleDebug
echo gradlew installDebug
echo.

pause
