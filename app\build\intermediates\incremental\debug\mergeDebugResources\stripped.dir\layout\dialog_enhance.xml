<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/enhance_dialog_background">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="画面增强"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- 锐化 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:text="锐化"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <SeekBar
            android:id="@+id/sharpenSeekBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:max="100"
            android:progress="0"
            android:layout_marginHorizontal="16dp" />

        <TextView
            android:id="@+id/sharpenValue"
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:text="0"
            android:textColor="@color/white"
            android:gravity="center" />
    </LinearLayout>

    <!-- 对比度 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:text="对比度"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <SeekBar
            android:id="@+id/contrastSeekBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:max="100"
            android:progress="50"
            android:layout_marginHorizontal="16dp" />

        <TextView
            android:id="@+id/contrastValue"
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:text="50"
            android:textColor="@color/white"
            android:gravity="center" />
    </LinearLayout>

    <!-- 亮度 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:text="亮度"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <SeekBar
            android:id="@+id/brightnessSeekBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:max="100"
            android:progress="50"
            android:layout_marginHorizontal="16dp" />

        <TextView
            android:id="@+id/brightnessValue"
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:text="50"
            android:textColor="@color/white"
            android:gravity="center" />
    </LinearLayout>

    <!-- 降噪 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <TextView
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:text="降噪"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <SeekBar
            android:id="@+id/noiseReductionSeekBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:max="100"
            android:progress="0"
            android:layout_marginHorizontal="16dp" />

        <TextView
            android:id="@+id/noiseReductionValue"
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:text="0"
            android:textColor="@color/white"
            android:gravity="center" />
    </LinearLayout>

    <!-- 按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/resetButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="重置"
            android:layout_marginEnd="16dp" />

        <Button
            android:id="@+id/applyButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="应用" />

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:layout_marginStart="16dp" />
    </LinearLayout>

</LinearLayout>
