package com.magnifyingglass.app.utils

import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.View
import android.view.WindowManager
import androidx.camera.core.Camera
import androidx.camera.core.ExposureState
import androidx.camera.view.PreviewView

class BrightnessController(
    private val activity: Activity,
    private val previewView: PreviewView
) {
    
    companion object {
        private const val MIN_BRIGHTNESS = 0
        private const val MAX_BRIGHTNESS = 200
        private const val DEFAULT_BRIGHTNESS = 100
        private const val BRIGHTNESS_INDICATOR_TIMEOUT = 3000L
        private const val LOW_LIGHT_THRESHOLD = 0.3f
    }
    
    interface BrightnessListener {
        fun onBrightnessChanged(brightness: Int)
        fun onLowLightDetected()
        fun onBrightnessIndicatorVisibilityChanged(visible: Boolean)
    }
    
    private var currentBrightness = DEFAULT_BRIGHTNESS
    private var camera: Camera? = null
    private var brightnessListener: BrightnessListener? = null
    private val handler = Handler(Looper.getMainLooper())
    private var hideIndicatorRunnable: Runnable? = null
    
    fun setBrightnessListener(listener: BrightnessListener) {
        this.brightnessListener = listener
    }
    
    fun setCamera(camera: Camera) {
        this.camera = camera
    }
    
    /**
     * 调整亮度
     */
    fun adjustBrightness(delta: Int) {
        val newBrightness = (currentBrightness + delta).coerceIn(MIN_BRIGHTNESS, MAX_BRIGHTNESS)
        setBrightness(newBrightness)
    }
    
    /**
     * 设置亮度
     */
    fun setBrightness(brightness: Int) {
        currentBrightness = brightness.coerceIn(MIN_BRIGHTNESS, MAX_BRIGHTNESS)
        applyBrightness()
        
        brightnessListener?.onBrightnessChanged(currentBrightness)
        showBrightnessIndicator()
    }
    
    /**
     * 应用亮度设置
     */
    private fun applyBrightness() {
        when {
            currentBrightness <= 100 -> {
                // 0-100%: 调整预览透明度和相机曝光
                val alpha = (currentBrightness / 100f).coerceIn(0.1f, 1.0f)
                previewView.alpha = alpha
                
                // 调整相机曝光补偿
                adjustCameraExposure(currentBrightness - 100)
            }
            
            currentBrightness > 100 -> {
                // 100-200%: 增加屏幕亮度和相机曝光
                previewView.alpha = 1.0f
                
                // 调整应用窗口亮度
                val windowBrightness = ((currentBrightness - 100) / 100f).coerceIn(0f, 1f)
                adjustWindowBrightness(windowBrightness)
                
                // 调整相机曝光补偿
                adjustCameraExposure(currentBrightness - 100)
            }
        }
    }
    
    /**
     * 调整相机曝光补偿
     */
    private fun adjustCameraExposure(exposureDelta: Int) {
        camera?.let { cam ->
            val exposureState = cam.cameraInfo.exposureState
            if (exposureState.isExposureCompensationSupported) {
                val range = exposureState.exposureCompensationRange
                val step = exposureState.exposureCompensationStep.toFloat()
                
                // 将亮度变化转换为曝光补偿值
                val exposureValue = (exposureDelta / 50f * step).toInt()
                val clampedValue = exposureValue.coerceIn(range.lower, range.upper)
                
                cam.cameraControl.setExposureCompensationIndex(clampedValue)
            }
        }
    }
    
    /**
     * 调整窗口亮度
     */
    private fun adjustWindowBrightness(brightness: Float) {
        val layoutParams = activity.window.attributes
        layoutParams.screenBrightness = brightness
        activity.window.attributes = layoutParams
    }
    
    /**
     * 重置窗口亮度到系统设置
     */
    private fun resetWindowBrightness() {
        val layoutParams = activity.window.attributes
        layoutParams.screenBrightness = WindowManager.LayoutParams.BRIGHTNESS_OVERRIDE_NONE
        activity.window.attributes = layoutParams
    }
    
    /**
     * 显示亮度指示器
     */
    private fun showBrightnessIndicator() {
        brightnessListener?.onBrightnessIndicatorVisibilityChanged(true)
        
        // 取消之前的隐藏任务
        hideIndicatorRunnable?.let { handler.removeCallbacks(it) }
        
        // 3秒后隐藏指示器
        hideIndicatorRunnable = Runnable {
            brightnessListener?.onBrightnessIndicatorVisibilityChanged(false)
        }
        handler.postDelayed(hideIndicatorRunnable!!, BRIGHTNESS_INDICATOR_TIMEOUT)
    }
    
    /**
     * 检测低光环境
     */
    fun checkLowLightEnvironment() {
        // 这里可以通过分析预览帧来检测低光环境
        // 简化实现：检查当前亮度设置
        if (currentBrightness < 50) {
            brightnessListener?.onLowLightDetected()
        }
    }
    
    /**
     * 设置最大亮度
     */
    fun setMaxBrightness() {
        setBrightness(MAX_BRIGHTNESS)
    }
    
    /**
     * 重置亮度到默认值
     */
    fun resetBrightness() {
        setBrightness(DEFAULT_BRIGHTNESS)
        resetWindowBrightness()
    }
    
    /**
     * 获取当前亮度
     */
    fun getCurrentBrightness(): Int = currentBrightness
    
    /**
     * 获取亮度百分比
     */
    fun getBrightnessPercentage(): Int = currentBrightness
    
    /**
     * 获取系统亮度
     */
    private fun getSystemBrightness(): Int {
        return try {
            Settings.System.getInt(
                activity.contentResolver,
                Settings.System.SCREEN_BRIGHTNESS
            )
        } catch (e: Exception) {
            128 // 默认值
        }
    }
    
    /**
     * 检查是否可以修改系统亮度
     */
    private fun canModifySystemBrightness(): Boolean {
        return Settings.System.canWrite(activity)
    }
    
    /**
     * 获取亮度调节建议
     */
    fun getBrightnessRecommendation(ambientLight: Float): Int {
        return when {
            ambientLight < 0.2f -> 150 // 暗环境，建议高亮度
            ambientLight < 0.5f -> 120 // 中等环境
            ambientLight < 0.8f -> 100 // 正常环境
            else -> 80 // 明亮环境，降低亮度
        }
    }
    
    /**
     * 自动调节亮度
     */
    fun autoAdjustBrightness(ambientLight: Float) {
        val recommendedBrightness = getBrightnessRecommendation(ambientLight)
        setBrightness(recommendedBrightness)
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        hideIndicatorRunnable?.let { handler.removeCallbacks(it) }
        resetWindowBrightness()
        previewView.alpha = 1.0f
    }
}
