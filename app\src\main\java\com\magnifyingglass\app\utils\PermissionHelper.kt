package com.magnifyingglass.app.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class PermissionHelper(private val context: Context) {

    private val privacyPolicyManager = PrivacyPolicyManager(context)
    
    companion object {
        const val CAMERA_PERMISSION_REQUEST_CODE = 1001
        const val STORAGE_PERMISSION_REQUEST_CODE = 1002
        const val ALL_PERMISSIONS_REQUEST_CODE = 1003
    }
    
    /**
     * 检查是否有相机权限
     */
    fun hasCameraPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查是否有存储权限
     */
    fun hasStoragePermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10及以上版本不需要存储权限来保存到MediaStore
            true
        } else {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 检查是否有所有必要权限
     */
    fun hasAllRequiredPermissions(): Boolean {
        return hasCameraPermission() && hasStoragePermission()
    }
    
    /**
     * 获取需要请求的权限列表
     */
    fun getRequiredPermissions(): Array<String> {
        val permissions = mutableListOf<String>()
        
        if (!hasCameraPermission()) {
            permissions.add(Manifest.permission.CAMERA)
        }
        
        if (!hasStoragePermission() && Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
        
        return permissions.toTypedArray()
    }
    
    /**
     * 请求相机权限 - 符合华为市场要求
     */
    fun requestCameraPermission(activity: Activity) {
        // 先显示权限说明
        privacyPolicyManager.showPermissionRationale(activity, "camera") {
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.CAMERA),
                CAMERA_PERMISSION_REQUEST_CODE
            )
        }
    }
    
    /**
     * 请求存储权限 - 符合华为市场要求
     */
    fun requestStoragePermission(activity: Activity) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            // 先显示权限说明
            privacyPolicyManager.showPermissionRationale(activity, "storage") {
                ActivityCompat.requestPermissions(
                    activity,
                    arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                    STORAGE_PERMISSION_REQUEST_CODE
                )
            }
        }
    }
    
    /**
     * 请求所有必要权限
     */
    fun requestAllPermissions(activity: Activity) {
        val permissions = getRequiredPermissions()
        if (permissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(
                activity,
                permissions,
                ALL_PERMISSIONS_REQUEST_CODE
            )
        }
    }
    
    /**
     * 检查是否应该显示权限说明
     */
    fun shouldShowCameraPermissionRationale(activity: Activity): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(
            activity,
            Manifest.permission.CAMERA
        )
    }
    
    /**
     * 检查是否应该显示存储权限说明
     */
    fun shouldShowStoragePermissionRationale(activity: Activity): Boolean {
        return if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            ActivityCompat.shouldShowRequestPermissionRationale(
                activity,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        } else {
            false
        }
    }
    
    /**
     * 处理权限请求结果
     */
    fun handlePermissionResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
        onAllGranted: () -> Unit,
        onCameraDenied: () -> Unit,
        onStorageDenied: () -> Unit
    ) {
        when (requestCode) {
            CAMERA_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    if (hasAllRequiredPermissions()) {
                        onAllGranted()
                    }
                } else {
                    onCameraDenied()
                }
            }
            STORAGE_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    if (hasAllRequiredPermissions()) {
                        onAllGranted()
                    }
                } else {
                    onStorageDenied()
                }
            }
            ALL_PERMISSIONS_REQUEST_CODE -> {
                var allGranted = true
                var cameraGranted = true
                var storageGranted = true
                
                for (i in permissions.indices) {
                    when (permissions[i]) {
                        Manifest.permission.CAMERA -> {
                            cameraGranted = grantResults[i] == PackageManager.PERMISSION_GRANTED
                        }
                        Manifest.permission.WRITE_EXTERNAL_STORAGE -> {
                            storageGranted = grantResults[i] == PackageManager.PERMISSION_GRANTED
                        }
                    }
                    
                    if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                        allGranted = false
                    }
                }
                
                when {
                    allGranted -> onAllGranted()
                    !cameraGranted -> onCameraDenied()
                    !storageGranted -> onStorageDenied()
                }
            }
        }
    }
    
    /**
     * 检查设备是否支持相机
     */
    fun hasCameraHardware(): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_CAMERA_ANY)
    }
    
    /**
     * 检查设备是否支持闪光灯
     */
    fun hasFlashlightHardware(): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_CAMERA_FLASH)
    }
    
    /**
     * 检查设备是否支持自动对焦
     */
    fun hasAutoFocusHardware(): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_CAMERA_AUTOFOCUS)
    }
}
