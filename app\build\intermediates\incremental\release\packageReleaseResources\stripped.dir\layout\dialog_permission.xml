<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 图标 -->
    <ImageView
        android:id="@+id/permissionIcon"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp"
        android:src="@drawable/ic_camera"
        android:tint="@color/primary" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/permissionTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:gravity="center"
        android:text="@string/permission_camera_title"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- 描述 -->
    <TextView
        android:id="@+id/permissionMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:gravity="center"
        android:lineSpacingExtra="4dp"
        android:text="@string/permission_camera_message"
        android:textColor="@color/black"
        android:textSize="14sp" />

    <!-- 按钮布局 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <!-- 取消按钮 -->
        <Button
            android:id="@+id/cancelButton"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="@string/permission_cancel"
            android:textColor="@color/inactive" />

        <!-- 授权按钮 -->
        <Button
            android:id="@+id/grantButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:text="@string/permission_grant" />

    </LinearLayout>

</LinearLayout>
