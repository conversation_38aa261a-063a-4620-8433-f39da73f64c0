<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="@color/primary"
    android:padding="32dp">

    <!-- 应用图标 -->
    <ImageView
        android:id="@+id/splash_icon"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/ic_magnifying_glass"
        android:layout_marginBottom="24dp"
        android:contentDescription="@string/app_name" />

    <!-- 应用标题 -->
    <TextView
        android:id="@+id/splash_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:layout_marginBottom="8dp" />

    <!-- 副标题 -->
    <TextView
        android:id="@+id/splash_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="基于神经网络的超分辨率智能放大镜"
        android:textSize="16sp"
        android:textColor="@color/white"
        android:alpha="0.8"
        android:layout_marginBottom="48dp" />

    <!-- 加载指示器 -->
    <ProgressBar
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminateTint="@color/white"
        style="?android:attr/progressBarStyle" />

    <!-- 版本信息 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="版本 1.0"
        android:textSize="12sp"
        android:textColor="@color/white"
        android:alpha="0.6"
        android:layout_marginTop="32dp" />

</LinearLayout>
