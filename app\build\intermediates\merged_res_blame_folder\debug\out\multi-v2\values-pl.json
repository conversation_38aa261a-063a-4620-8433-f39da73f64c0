{"logs": [{"outputFile": "com.magnifyingglass.app-mergeDebugResources-2:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c3074531d01ac9ab8cfdf2481696a09e\\transformed\\core-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "40,41,42,43,44,45,46,111", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3576,3673,3775,3873,3972,4086,4191,9470", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3668,3770,3868,3967,4081,4186,4308,9566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57c380a018d3ece89de602b2e29bfae7\\transformed\\material-1.10.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,719,814,899,1016,1098,1162,1243,1307,1368,1479,1543,1611,1665,1734,1796,1850,1961,2022,2084,2138,2210,2339,2428,2510,2659,2741,2824,2961,3048,3125,3179,3230,3296,3367,3443,3532,3615,3692,3770,3848,3924,4032,4122,4195,4290,4387,4459,4533,4633,4685,4770,4836,4924,5014,5076,5140,5203,5274,5381,5493,5592,5699,5757,5812", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,74,74,78,103,94,84,116,81,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,81,148,81,82,136,86,76,53,50,65,70,75,88,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75", "endOffsets": "381,456,531,610,714,809,894,1011,1093,1157,1238,1302,1363,1474,1538,1606,1660,1729,1791,1845,1956,2017,2079,2133,2205,2334,2423,2505,2654,2736,2819,2956,3043,3120,3174,3225,3291,3362,3438,3527,3610,3687,3765,3843,3919,4027,4117,4190,4285,4382,4454,4528,4628,4680,4765,4831,4919,5009,5071,5135,5198,5269,5376,5488,5587,5694,5752,5807,5883"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3223,3298,3377,3481,4313,4398,4515,4597,4661,4742,4806,4867,4978,5042,5110,5164,5233,5295,5349,5460,5521,5583,5637,5709,5838,5927,6009,6158,6240,6323,6460,6547,6624,6678,6729,6795,6866,6942,7031,7114,7191,7269,7347,7423,7531,7621,7694,7789,7886,7958,8032,8132,8184,8269,8335,8423,8513,8575,8639,8702,8773,8880,8992,9091,9198,9256,9311", "endLines": "7,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "12,74,74,78,103,94,84,116,81,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,81,148,81,82,136,86,76,53,50,65,70,75,88,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75", "endOffsets": "431,3218,3293,3372,3476,3571,4393,4510,4592,4656,4737,4801,4862,4973,5037,5105,5159,5228,5290,5344,5455,5516,5578,5632,5704,5833,5922,6004,6153,6235,6318,6455,6542,6619,6673,6724,6790,6861,6937,7026,7109,7186,7264,7342,7418,7526,7616,7689,7784,7881,7953,8027,8127,8179,8264,8330,8418,8508,8570,8634,8697,8768,8875,8987,9086,9193,9251,9306,9382"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7c6a6834abbfd4e3a8f7f46272ab8256\\transformed\\appcompat-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "436,551,653,761,847,954,1073,1152,1228,1319,1412,1507,1601,1702,1795,1890,1985,2076,2167,2249,2358,2458,2557,2666,2778,2889,3052,9387", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "546,648,756,842,949,1068,1147,1223,1314,1407,1502,1596,1697,1790,1885,1980,2071,2162,2244,2353,2453,2552,2661,2773,2884,3047,3143,9465"}}]}]}