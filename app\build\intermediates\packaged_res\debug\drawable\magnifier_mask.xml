<?xml version="1.0" encoding="utf-8"?>
<!-- 这个drawable只用于装饰边框和手柄，不遮挡视频 -->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 放大镜边框 -->
    <item android:gravity="center">
        <shape android:shape="oval">
            <size android:width="320dp" android:height="320dp" />
            <stroke android:width="8dp" android:color="#FFD700" />
            <solid android:color="#00000000" />
        </shape>
    </item>

    <!-- 放大镜手柄 - 从圆形边缘延伸 -->
    <item android:gravity="center_horizontal|bottom" android:bottom="40dp">
        <shape android:shape="rectangle">
            <size android:width="20dp" android:height="100dp" />
            <solid android:color="#FFD700" />
            <corners android:radius="10dp" />
        </shape>
    </item>

    <!-- 手柄连接点 -->
    <item android:gravity="center_horizontal|bottom" android:bottom="130dp">
        <shape android:shape="rectangle">
            <size android:width="25dp" android:height="20dp" />
            <solid android:color="#FFD700" />
        </shape>
    </item>
</layer-list>
