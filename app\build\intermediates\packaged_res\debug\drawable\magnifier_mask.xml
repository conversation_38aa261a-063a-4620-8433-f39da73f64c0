<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 半透明背景 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#80000000" />
        </shape>
    </item>
    
    <!-- 圆形透明区域 -->
    <item android:gravity="center">
        <shape android:shape="oval">
            <size android:width="320dp" android:height="320dp" />
            <solid android:color="#00000000" />
        </shape>
    </item>
    
    <!-- 放大镜边框 -->
    <item android:gravity="center">
        <shape android:shape="oval">
            <size android:width="320dp" android:height="320dp" />
            <stroke android:width="8dp" android:color="#FFD700" />
            <solid android:color="#00000000" />
        </shape>
    </item>
    
    <!-- 放大镜手柄 -->
    <item android:gravity="center_horizontal|bottom" android:bottom="60dp">
        <shape android:shape="rectangle">
            <size android:width="20dp" android:height="80dp" />
            <solid android:color="#FFD700" />
            <corners android:radius="10dp" />
        </shape>
    </item>
</layer-list>
