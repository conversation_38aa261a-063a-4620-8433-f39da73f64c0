package com.magnifyingglass.app.views

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View

/**
 * 放大镜遮罩视图
 * 圆圈内透明显示正常视频，圆圈外半透明磨砂效果
 */
class MagnifierOverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val maskPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#80000000") // 半透明黑色
        style = Paint.Style.FILL
    }
    
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#FFD700") // 金色
        style = Paint.Style.STROKE
        strokeWidth = 16f
    }
    
    private val handlePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#FFD700") // 金色
        style = Paint.Style.FILL
    }
    
    private val clearPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
    }
    
    private var centerX = 0f
    private var centerY = 0f
    private var circleRadius = 160f // 圆形半径
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        centerX = w / 2f
        centerY = h / 2f
        circleRadius = minOf(w, h) * 0.3f // 动态调整圆形大小
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        // 使用离屏缓冲区来实现正确的遮罩效果
        val layerId = canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null)
        
        // 1. 绘制半透明背景
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), maskPaint)
        
        // 2. 在中心挖一个透明圆洞
        canvas.drawCircle(centerX, centerY, circleRadius, clearPaint)
        
        canvas.restoreToCount(layerId)
        
        // 3. 绘制放大镜边框
        canvas.drawCircle(centerX, centerY, circleRadius, borderPaint)
        
        // 4. 绘制放大镜手柄
        drawMagnifierHandle(canvas)
    }
    
    private fun drawMagnifierHandle(canvas: Canvas) {
        // 计算手柄起始位置（圆形底部右侧）
        val handleStartAngle = 45f // 45度角
        val angleRad = Math.toRadians(handleStartAngle.toDouble())
        
        val handleStartX = centerX + (circleRadius * Math.cos(angleRad)).toFloat()
        val handleStartY = centerY + (circleRadius * Math.sin(angleRad)).toFloat()
        
        // 手柄长度和宽度
        val handleLength = 120f
        val handleWidth = 20f
        
        // 计算手柄终点
        val handleEndX = handleStartX + (handleLength * Math.cos(angleRad)).toFloat()
        val handleEndY = handleStartY + (handleLength * Math.sin(angleRad)).toFloat()
        
        // 绘制手柄主体
        val handlePath = Path()
        
        // 计算手柄的四个角点
        val perpAngle = angleRad + Math.PI / 2
        val halfWidth = handleWidth / 2
        
        val x1 = handleStartX + (halfWidth * Math.cos(perpAngle)).toFloat()
        val y1 = handleStartY + (halfWidth * Math.sin(perpAngle)).toFloat()
        val x2 = handleStartX - (halfWidth * Math.cos(perpAngle)).toFloat()
        val y2 = handleStartY - (halfWidth * Math.sin(perpAngle)).toFloat()
        val x3 = handleEndX - (halfWidth * Math.cos(perpAngle)).toFloat()
        val y3 = handleEndY - (halfWidth * Math.sin(perpAngle)).toFloat()
        val x4 = handleEndX + (halfWidth * Math.cos(perpAngle)).toFloat()
        val y4 = handleEndY + (halfWidth * Math.sin(perpAngle)).toFloat()
        
        handlePath.moveTo(x1, y1)
        handlePath.lineTo(x2, y2)
        handlePath.lineTo(x3, y3)
        handlePath.lineTo(x4, y4)
        handlePath.close()
        
        canvas.drawPath(handlePath, handlePaint)
        
        // 绘制手柄末端的圆形装饰
        canvas.drawCircle(handleEndX, handleEndY, handleWidth / 2, handlePaint)
        
        // 绘制连接处的圆形
        canvas.drawCircle(handleStartX, handleStartY, handleWidth / 2 + 2, handlePaint)
    }
    
    /**
     * 设置圆形区域大小
     */
    fun setCircleRadius(radius: Float) {
        circleRadius = radius
        invalidate()
    }
    
    /**
     * 设置圆形中心位置
     */
    fun setCircleCenter(x: Float, y: Float) {
        centerX = x
        centerY = y
        invalidate()
    }
}
