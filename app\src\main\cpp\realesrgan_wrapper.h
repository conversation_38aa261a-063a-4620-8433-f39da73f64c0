#ifndef REALESRGAN_WRAPPER_H
#define REALESRGAN_WRAPPER_H

#include <string>

/**
 * Real-ESRGAN NCNN包装器
 * 简化的接口，用于Android JNI调用
 */
class RealESRGANWrapper {
public:
    RealESRGANWrapper();
    ~RealESRGANWrapper();
    
    /**
     * 加载模型
     * @param param_path .param文件路径
     * @param bin_path .bin文件路径
     * @return 是否成功
     */
    bool load(const std::string& param_path, const std::string& bin_path);
    
    /**
     * 处理图像
     * @param input_path 输入图像路径
     * @param output_path 输出图像路径
     * @param scale 放大倍数
     * @return 0表示成功，其他值表示错误
     */
    int process(const std::string& input_path, const std::string& output_path, int scale);
    
private:
    void* net; // ncnn::Net指针，使用void*避免包含ncnn头文件
    bool initialized;
};

#endif // REALESRGAN_WRAPPER_H
