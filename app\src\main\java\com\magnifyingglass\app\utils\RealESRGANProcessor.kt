package com.magnifyingglass.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Paint
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.*

/**
 * Real-ESRGAN图像超分辨率处理器
 * 注意：这是传统图像处理算法的实现，模拟Real-ESRGAN效果
 * 真正的Real-ESRGAN需要NCNN库和预训练模型
 *
 * 要集成真正的Real-ESRGAN，需要：
 * 1. 添加NCNN库依赖
 * 2. 下载Real-ESRGAN预训练模型
 * 3. 实现JNI接口调用NCNN
 * 4. 参考：https://github.com/tumuyan/RealSR-NCNN-Android
 */
class RealESRGANProcessor(private val context: Context) {
    
    companion object {
        private const val TAG = "RealESRGANProcessor"
        private const val SCALE_FACTOR = 2.0f // 放大倍数
    }
    
    /**
     * 处理图像，应用Real-ESRGAN风格的增强
     */
    suspend fun enhanceImage(
        inputBitmap: Bitmap,
        progressCallback: ((Float) -> Unit)? = null
    ): Bitmap = withContext(Dispatchers.Default) {
        try {
            Log.d(TAG, "开始Real-ESRGAN风格图像增强")
            
            // 步骤1: 双三次插值放大 (20%)
            progressCallback?.invoke(0.2f)
            val upscaledBitmap = bicubicUpscale(inputBitmap, SCALE_FACTOR)
            
            // 步骤2: 边缘增强 (40%)
            progressCallback?.invoke(0.4f)
            val edgeEnhanced = enhanceEdges(upscaledBitmap)
            
            // 步骤3: 细节锐化 (60%)
            progressCallback?.invoke(0.6f)
            val sharpened = applySharpeningFilter(edgeEnhanced)
            
            // 步骤4: 噪声抑制 (80%)
            progressCallback?.invoke(0.8f)
            val denoised = applyNoiseReduction(sharpened)
            
            // 步骤5: 颜色增强 (100%)
            progressCallback?.invoke(1.0f)
            val finalResult = enhanceColors(denoised)
            
            Log.d(TAG, "Real-ESRGAN风格图像增强完成")
            finalResult
            
        } catch (e: Exception) {
            Log.e(TAG, "图像增强失败", e)
            inputBitmap // 返回原图
        }
    }
    
    /**
     * 双三次插值放大
     */
    private fun bicubicUpscale(bitmap: Bitmap, scaleFactor: Float): Bitmap {
        val newWidth = (bitmap.width * scaleFactor).toInt()
        val newHeight = (bitmap.height * scaleFactor).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    /**
     * 边缘增强
     */
    private fun enhanceEdges(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val result = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        
        // Sobel边缘检测核
        val sobelX = arrayOf(
            intArrayOf(-1, 0, 1),
            intArrayOf(-2, 0, 2),
            intArrayOf(-1, 0, 1)
        )
        
        val sobelY = arrayOf(
            intArrayOf(-1, -2, -1),
            intArrayOf(0, 0, 0),
            intArrayOf(1, 2, 1)
        )
        
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        for (y in 1 until height - 1) {
            for (x in 1 until width - 1) {
                var gx = 0
                var gy = 0
                
                // 应用Sobel算子
                for (i in -1..1) {
                    for (j in -1..1) {
                        val pixel = pixels[(y + i) * width + (x + j)]
                        val gray = (android.graphics.Color.red(pixel) + 
                                   android.graphics.Color.green(pixel) + 
                                   android.graphics.Color.blue(pixel)) / 3
                        
                        gx += gray * sobelX[i + 1][j + 1]
                        gy += gray * sobelY[i + 1][j + 1]
                    }
                }
                
                val magnitude = sqrt((gx * gx + gy * gy).toDouble()).toInt()
                val edgeStrength = minOf(255, magnitude)
                
                // 混合原像素和边缘信息
                val originalPixel = pixels[y * width + x]
                val r = minOf(255, android.graphics.Color.red(originalPixel) + edgeStrength / 4)
                val g = minOf(255, android.graphics.Color.green(originalPixel) + edgeStrength / 4)
                val b = minOf(255, android.graphics.Color.blue(originalPixel) + edgeStrength / 4)
                
                pixels[y * width + x] = android.graphics.Color.rgb(r, g, b)
            }
        }
        
        result.setPixels(pixels, 0, width, 0, 0, width, height)
        return result
    }
    
    /**
     * 锐化滤镜
     */
    private fun applySharpeningFilter(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val result = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        
        // 锐化核
        val kernel = arrayOf(
            floatArrayOf(0f, -0.5f, 0f),
            floatArrayOf(-0.5f, 3f, -0.5f),
            floatArrayOf(0f, -0.5f, 0f)
        )
        
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        for (y in 1 until height - 1) {
            for (x in 1 until width - 1) {
                var r = 0f
                var g = 0f
                var b = 0f
                
                for (i in -1..1) {
                    for (j in -1..1) {
                        val pixel = pixels[(y + i) * width + (x + j)]
                        val weight = kernel[i + 1][j + 1]
                        
                        r += android.graphics.Color.red(pixel) * weight
                        g += android.graphics.Color.green(pixel) * weight
                        b += android.graphics.Color.blue(pixel) * weight
                    }
                }
                
                val newR = maxOf(0, minOf(255, r.toInt()))
                val newG = maxOf(0, minOf(255, g.toInt()))
                val newB = maxOf(0, minOf(255, b.toInt()))
                
                pixels[y * width + x] = android.graphics.Color.rgb(newR, newG, newB)
            }
        }
        
        result.setPixels(pixels, 0, width, 0, 0, width, height)
        return result
    }
    
    /**
     * 噪声抑制
     */
    private fun applyNoiseReduction(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val result = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        
        // 高斯模糊核（轻微）
        val kernel = arrayOf(
            floatArrayOf(1f, 2f, 1f),
            floatArrayOf(2f, 4f, 2f),
            floatArrayOf(1f, 2f, 1f)
        )
        val kernelSum = 16f
        
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        for (y in 1 until height - 1) {
            for (x in 1 until width - 1) {
                var r = 0f
                var g = 0f
                var b = 0f
                
                for (i in -1..1) {
                    for (j in -1..1) {
                        val pixel = pixels[(y + i) * width + (x + j)]
                        val weight = kernel[i + 1][j + 1] / kernelSum
                        
                        r += android.graphics.Color.red(pixel) * weight
                        g += android.graphics.Color.green(pixel) * weight
                        b += android.graphics.Color.blue(pixel) * weight
                    }
                }
                
                pixels[y * width + x] = android.graphics.Color.rgb(r.toInt(), g.toInt(), b.toInt())
            }
        }
        
        result.setPixels(pixels, 0, width, 0, 0, width, height)
        return result
    }
    
    /**
     * 颜色增强
     */
    private fun enhanceColors(bitmap: Bitmap): Bitmap {
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        val paint = Paint()
        
        // 增强对比度和饱和度
        val colorMatrix = ColorMatrix()
        colorMatrix.setSaturation(1.2f) // 增加饱和度
        
        val contrastMatrix = ColorMatrix()
        val contrast = 1.1f
        val brightness = 10f
        contrastMatrix.set(floatArrayOf(
            contrast, 0f, 0f, 0f, brightness,
            0f, contrast, 0f, 0f, brightness,
            0f, 0f, contrast, 0f, brightness,
            0f, 0f, 0f, 1f, 0f
        ))
        
        colorMatrix.postConcat(contrastMatrix)
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
        
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        return result
    }
}
