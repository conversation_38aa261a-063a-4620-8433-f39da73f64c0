-- Merging decision tree log ---
manifest
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:2:1-61:12
INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:2:1-61:12
INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:2:1-61:12
INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:2:1-61:12
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\43cc88a5671f3293cd198958fea72f5e\transformed\viewbinding-8.1.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\57c380a018d3ece89de602b2e29bfae7\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\b07c208b6db9a8d3b3e4f9ab7334fb16\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-video:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\164f6cb301ed33c39f95ce2878ed616d\transformed\camera-video-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-lifecycle:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8cb7f70f66af4f6f59736a1c00f96889\transformed\camera-lifecycle-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\db9852cb0b43776d58ebc85b43483083\transformed\camera-core-1.3.0\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae5be187424dfc0506d28c248ade9517\transformed\camera-view-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eb12a08a4764e65d15f6602e264e4f36\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c6a6834abbfd4e3a8f7f46272ab8256\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdcdae817c7fdd7e43debdbb47a97548\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\30ed28c8498a67657e4a3fc0cdd507b3\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\56f4c6965c0a6b6d03cd867aba80e8c5\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\69a424dc7e3f40e2d580a60e4e383c80\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\2a9e93e77082b4763ae87c7106914198\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5271c960c9d5837c13d449d528932807\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f9ec7e92f8d4d406ad27d8c385dea9c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\875e99990618f09adce2dff1546f3a2d\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae42766d6e02db0b1f6b69eabcdc1a9e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f780139bef6406ef79afb4cb900e5ba8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a58bea30b3c203a4e21fb4165d042b8\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d58e978e3394c1223afd408e6f32cfd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\396f7b4c55c250b9f355a8e387e9ce65\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e2b7fea092763b32e897070821545898\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f7122888fc5319819edffa73b78584f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e7155f9334b9c4383c4b1601d55c9afb\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee100418dd47cf072226e8524891a88f\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\97a5d874db5e1746f0a50a95712ec8dd\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8e6ff5beee4945705667cf2726cb82e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ff49ffdc3dec1e09d5845574687d06d\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\deea859ca281ef783c49aa66e555f006\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6abe205ad4f588cea4f74f77f187147c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8d95988b2a73f7f32046de679e09acd\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\465c12f326f171fac466a704cf579d14\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\18ecb74868c22748627511e563086e0e\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3c5ddaab6b54e68bdba06db0b4d9431\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\df2adf1ceddebace00ba0f5362e23d87\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a5b1cf00634af388a29081f5f4aa1df\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6af8b7beb58cde75e1604de9d09181e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e79d01637476aae8ee3b3ac9c1cfbe15\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2eaabe2c602885800d2eda9a80cb4a3e\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7aecc70d71a23a17dabeea5faebd9caa\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ca83a6e06c08f7d76fb7ffc5a582dcd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\58d35b5027a5794124355aa75ecb12d3\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a112215e52d46e9b2af8ba9c50f7120f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a0efd54964d9d2de92effb6e8ccc91\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc9fe1b966936a3c698d6a154d3dc6\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\113f6127e759e89e251d1e52aac74860\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ba1a85b6b1e12aeb0d79d1a19cb0f48\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0da67834aba50a40d0c24756abddd86f\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:6:22-62
uses-feature#android.hardware.camera.flash
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:5-91
	android:required
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:64-88
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:9:19-63
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:14:5-15:38
	android:maxSdkVersion
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:15:9-35
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:14:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:18:5-76
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:18:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:19:5-75
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:19:22-72
uses-feature#android.hardware.camera
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:22:5-84
	android:required
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:22:58-81
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:22:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:23:5-95
	android:required
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:23:68-92
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:23:19-67
application
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:25:5-59:19
INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:25:5-59:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\57c380a018d3ece89de602b2e29bfae7\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\57c380a018d3ece89de602b2e29bfae7\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\b07c208b6db9a8d3b3e4f9ab7334fb16\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\b07c208b6db9a8d3b3e4f9ab7334fb16\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\db9852cb0b43776d58ebc85b43483083\transformed\camera-core-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\db9852cb0b43776d58ebc85b43483083\transformed\camera-core-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ca83a6e06c08f7d76fb7ffc5a582dcd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ca83a6e06c08f7d76fb7ffc5a582dcd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a112215e52d46e9b2af8ba9c50f7120f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a112215e52d46e9b2af8ba9c50f7120f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:32:9-35
	android:label
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:30:9-41
	android:fullBackupContent
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:28:9-54
	android:roundIcon
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:31:9-58
	tools:targetApi
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:34:9-29
	android:icon
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:29:9-53
	android:allowBackup
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:26:9-35
	android:theme
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:33:9-53
	android:dataExtractionRules
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:27:9-65
activity#com.magnifyingglass.app.SplashActivity
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:36:9-45:20
	android:screenOrientation
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:39:13-49
	android:exported
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:38:13-36
	android:theme
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:40:13-69
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:37:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:41:13-44:29
action#android.intent.action.MAIN
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:42:17-69
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:42:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:43:17-77
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:43:27-74
activity#com.magnifyingglass.app.MainActivity
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:47:9-52:46
	android:screenOrientation
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:50:13-49
	android:launchMode
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:52:13-43
	android:exported
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:49:13-37
	android:theme
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:51:13-69
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:48:13-41
activity#com.magnifyingglass.app.OnboardingActivity
ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:54:9-58:72
	android:screenOrientation
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:57:13-49
	android:exported
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:56:13-37
	android:theme
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:58:13-69
	android:name
		ADDED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml:55:13-47
uses-sdk
INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml
INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\43cc88a5671f3293cd198958fea72f5e\transformed\viewbinding-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\43cc88a5671f3293cd198958fea72f5e\transformed\viewbinding-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\57c380a018d3ece89de602b2e29bfae7\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\57c380a018d3ece89de602b2e29bfae7\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\b07c208b6db9a8d3b3e4f9ab7334fb16\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\b07c208b6db9a8d3b3e4f9ab7334fb16\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-video:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\164f6cb301ed33c39f95ce2878ed616d\transformed\camera-video-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\164f6cb301ed33c39f95ce2878ed616d\transformed\camera-video-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8cb7f70f66af4f6f59736a1c00f96889\transformed\camera-lifecycle-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8cb7f70f66af4f6f59736a1c00f96889\transformed\camera-lifecycle-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\db9852cb0b43776d58ebc85b43483083\transformed\camera-core-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\db9852cb0b43776d58ebc85b43483083\transformed\camera-core-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae5be187424dfc0506d28c248ade9517\transformed\camera-view-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae5be187424dfc0506d28c248ade9517\transformed\camera-view-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eb12a08a4764e65d15f6602e264e4f36\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eb12a08a4764e65d15f6602e264e4f36\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c6a6834abbfd4e3a8f7f46272ab8256\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c6a6834abbfd4e3a8f7f46272ab8256\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdcdae817c7fdd7e43debdbb47a97548\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdcdae817c7fdd7e43debdbb47a97548\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\30ed28c8498a67657e4a3fc0cdd507b3\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\30ed28c8498a67657e4a3fc0cdd507b3\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\56f4c6965c0a6b6d03cd867aba80e8c5\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\56f4c6965c0a6b6d03cd867aba80e8c5\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\69a424dc7e3f40e2d580a60e4e383c80\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\69a424dc7e3f40e2d580a60e4e383c80\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\2a9e93e77082b4763ae87c7106914198\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\2a9e93e77082b4763ae87c7106914198\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5271c960c9d5837c13d449d528932807\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5271c960c9d5837c13d449d528932807\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f9ec7e92f8d4d406ad27d8c385dea9c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f9ec7e92f8d4d406ad27d8c385dea9c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\875e99990618f09adce2dff1546f3a2d\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\875e99990618f09adce2dff1546f3a2d\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae42766d6e02db0b1f6b69eabcdc1a9e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae42766d6e02db0b1f6b69eabcdc1a9e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f780139bef6406ef79afb4cb900e5ba8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f780139bef6406ef79afb4cb900e5ba8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a58bea30b3c203a4e21fb4165d042b8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a58bea30b3c203a4e21fb4165d042b8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d58e978e3394c1223afd408e6f32cfd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d58e978e3394c1223afd408e6f32cfd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\396f7b4c55c250b9f355a8e387e9ce65\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\396f7b4c55c250b9f355a8e387e9ce65\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e2b7fea092763b32e897070821545898\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e2b7fea092763b32e897070821545898\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f7122888fc5319819edffa73b78584f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f7122888fc5319819edffa73b78584f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e7155f9334b9c4383c4b1601d55c9afb\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e7155f9334b9c4383c4b1601d55c9afb\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee100418dd47cf072226e8524891a88f\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee100418dd47cf072226e8524891a88f\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\97a5d874db5e1746f0a50a95712ec8dd\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\97a5d874db5e1746f0a50a95712ec8dd\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8e6ff5beee4945705667cf2726cb82e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8e6ff5beee4945705667cf2726cb82e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ff49ffdc3dec1e09d5845574687d06d\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ff49ffdc3dec1e09d5845574687d06d\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\deea859ca281ef783c49aa66e555f006\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\deea859ca281ef783c49aa66e555f006\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6abe205ad4f588cea4f74f77f187147c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6abe205ad4f588cea4f74f77f187147c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8d95988b2a73f7f32046de679e09acd\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8d95988b2a73f7f32046de679e09acd\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\465c12f326f171fac466a704cf579d14\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\465c12f326f171fac466a704cf579d14\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\18ecb74868c22748627511e563086e0e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\18ecb74868c22748627511e563086e0e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3c5ddaab6b54e68bdba06db0b4d9431\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3c5ddaab6b54e68bdba06db0b4d9431\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\df2adf1ceddebace00ba0f5362e23d87\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\df2adf1ceddebace00ba0f5362e23d87\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a5b1cf00634af388a29081f5f4aa1df\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a5b1cf00634af388a29081f5f4aa1df\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6af8b7beb58cde75e1604de9d09181e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6af8b7beb58cde75e1604de9d09181e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e79d01637476aae8ee3b3ac9c1cfbe15\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e79d01637476aae8ee3b3ac9c1cfbe15\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2eaabe2c602885800d2eda9a80cb4a3e\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2eaabe2c602885800d2eda9a80cb4a3e\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7aecc70d71a23a17dabeea5faebd9caa\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7aecc70d71a23a17dabeea5faebd9caa\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ca83a6e06c08f7d76fb7ffc5a582dcd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ca83a6e06c08f7d76fb7ffc5a582dcd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\58d35b5027a5794124355aa75ecb12d3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\58d35b5027a5794124355aa75ecb12d3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a112215e52d46e9b2af8ba9c50f7120f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a112215e52d46e9b2af8ba9c50f7120f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a0efd54964d9d2de92effb6e8ccc91\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a0efd54964d9d2de92effb6e8ccc91\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc9fe1b966936a3c698d6a154d3dc6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc9fe1b966936a3c698d6a154d3dc6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\113f6127e759e89e251d1e52aac74860\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\113f6127e759e89e251d1e52aac74860\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ba1a85b6b1e12aeb0d79d1a19cb0f48\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ba1a85b6b1e12aeb0d79d1a19cb0f48\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0da67834aba50a40d0c24756abddd86f\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0da67834aba50a40d0c24756abddd86f\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Magnifying Glass\app\src\main\AndroidManifest.xml
queries
ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:22:5-26:15
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\82444efa7c715e0cd736e715d17d3bc6\transformed\camera-extensions-1.3.0\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\db9852cb0b43776d58ebc85b43483083\transformed\camera-core-1.3.0\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\db9852cb0b43776d58ebc85b43483083\transformed\camera-core-1.3.0\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa46e4440fec6c864b7db8850665e67\transformed\camera-camera2-1.3.0\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a112215e52d46e9b2af8ba9c50f7120f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a112215e52d46e9b2af8ba9c50f7120f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\09f2a64d14e76ce226e936273dcfe3d4\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d78a3fb20d71b2bdb399338a7de8e41b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.magnifyingglass.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.magnifyingglass.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3074531d01ac9ab8cfdf2481696a09e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8331a785c786946de5850f47b315052\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
