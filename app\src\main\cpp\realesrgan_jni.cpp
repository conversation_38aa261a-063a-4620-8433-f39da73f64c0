#include <jni.h>
#include <android/log.h>
#include <android/bitmap.h>
#include <string>
#include "realesrgan_wrapper.h"

#define TAG "RealESRGAN_JNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)

extern "C" {

JNIEXPORT jlong JNICALL
Java_com_magnifyingglass_app_utils_TrueRealESRGANProcessor_realESRGANInit(
    JNIEnv *env, jobject thiz, jstring param_path, jstring bin_path) {
    
    const char* param_str = env->GetStringUTFChars(param_path, 0);
    const char* bin_str = env->GetStringUTFChars(bin_path, 0);
    
    LOGI("初始化Real-ESRGAN模型: param=%s, bin=%s", param_str, bin_str);
    
    RealESRGANWrapper* wrapper = new RealESRGANWrapper();
    bool success = wrapper->load(param_str, bin_str);
    
    env->ReleaseStringUTFChars(param_path, param_str);
    env->ReleaseStringUTFChars(bin_path, bin_str);
    
    if (success) {
        LOGI("Real-ESRGAN模型初始化成功");
        return reinterpret_cast<jlong>(wrapper);
    } else {
        LOGE("Real-ESRGAN模型初始化失败");
        delete wrapper;
        return 0;
    }
}

JNIEXPORT jint JNICALL
Java_com_magnifyingglass_app_utils_TrueRealESRGANProcessor_realESRGANProcess(
    JNIEnv *env, jobject thiz, jlong handle, jstring input_path, jstring output_path, jint scale) {
    
    if (handle == 0) {
        LOGE("无效的模型句柄");
        return -1;
    }
    
    const char* input_str = env->GetStringUTFChars(input_path, 0);
    const char* output_str = env->GetStringUTFChars(output_path, 0);
    
    LOGI("处理图像: input=%s, output=%s, scale=%d", input_str, output_str, scale);
    
    RealESRGANWrapper* wrapper = reinterpret_cast<RealESRGANWrapper*>(handle);
    int result = wrapper->process(input_str, output_str, scale);
    
    env->ReleaseStringUTFChars(input_path, input_str);
    env->ReleaseStringUTFChars(output_path, output_str);
    
    if (result == 0) {
        LOGI("图像处理成功");
    } else {
        LOGE("图像处理失败，错误码: %d", result);
    }
    
    return result;
}

JNIEXPORT void JNICALL
Java_com_magnifyingglass_app_utils_TrueRealESRGANProcessor_realESRGANRelease(
    JNIEnv *env, jobject thiz, jlong handle) {
    
    if (handle != 0) {
        RealESRGANWrapper* wrapper = reinterpret_cast<RealESRGANWrapper*>(handle);
        delete wrapper;
        LOGI("Real-ESRGAN资源已释放");
    }
}

}
