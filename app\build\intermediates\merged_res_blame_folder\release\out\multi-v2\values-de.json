{"logs": [{"outputFile": "com.magnifyingglass.app-mergeReleaseResources-2:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57c380a018d3ece89de602b2e29bfae7\\transformed\\material-1.10.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1089,1183,1253,1312,1420,1486,1555,1613,1685,1749,1803,1931,1991,2053,2107,2185,2322,2414,2498,2643,2727,2813,2946,3036,3115,3172,3223,3289,3363,3445,3538,3613,3687,3765,3837,3911,4021,4113,4195,4284,4373,4447,4525,4611,4666,4745,4812,4892,4976,5038,5102,5165,5234,5341,5448,5547,5653,5714,5769", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,132,89,78,56,50,65,73,81,92,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81", "endOffsets": "278,369,458,542,632,714,815,937,1018,1084,1178,1248,1307,1415,1481,1550,1608,1680,1744,1798,1926,1986,2048,2102,2180,2317,2409,2493,2638,2722,2808,2941,3031,3110,3167,3218,3284,3358,3440,3533,3608,3682,3760,3832,3906,4016,4108,4190,4279,4368,4442,4520,4606,4661,4740,4807,4887,4971,5033,5097,5160,5229,5336,5443,5542,5648,5709,5764,5846"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3060,3151,3240,3324,3414,4227,4328,4450,4531,4597,4691,4761,4820,4928,4994,5063,5121,5193,5257,5311,5439,5499,5561,5615,5693,5830,5922,6006,6151,6235,6321,6454,6544,6623,6680,6731,6797,6871,6953,7046,7121,7195,7273,7345,7419,7529,7621,7703,7792,7881,7955,8033,8119,8174,8253,8320,8400,8484,8546,8610,8673,8742,8849,8956,9055,9161,9222,9277", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,132,89,78,56,50,65,73,81,92,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81", "endOffsets": "328,3146,3235,3319,3409,3491,4323,4445,4526,4592,4686,4756,4815,4923,4989,5058,5116,5188,5252,5306,5434,5494,5556,5610,5688,5825,5917,6001,6146,6230,6316,6449,6539,6618,6675,6726,6792,6866,6948,7041,7116,7190,7268,7340,7414,7524,7616,7698,7787,7876,7950,8028,8114,8169,8248,8315,8395,8479,8541,8605,8668,8737,8844,8951,9050,9156,9217,9272,9354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7c6a6834abbfd4e3a8f7f46272ab8256\\transformed\\appcompat-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,9359", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,9436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c3074531d01ac9ab8cfdf2481696a09e\\transformed\\core-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3496,3594,3696,3796,3896,4004,4109,9441", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3589,3691,3791,3891,3999,4104,4222,9537"}}]}]}