package com.magnifyingglass.app.utils

import android.animation.ValueAnimator
import android.view.animation.DecelerateInterpolator
import androidx.camera.core.Camera
import androidx.camera.core.ZoomState
import androidx.camera.core.FocusMeteringAction
import androidx.camera.core.SurfaceOrientedMeteringPointFactory
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer

class ZoomController(
    private val camera: Camera,
    private val lifecycleOwner: LifecycleOwner
) {
    
    companion object {
        private const val ZOOM_ANIMATION_DURATION = 300L
        private const val MIN_ZOOM_RATIO = 1.0f
        private const val MAX_ZOOM_RATIO = 16.0f
        private const val DEFAULT_ZOOM_STEP = 0.5f
    }
    
    private var currentZoomRatio = MIN_ZOOM_RATIO
    private var previousZoomRatio = MIN_ZOOM_RATIO
    private var maxSupportedZoom = MAX_ZOOM_RATIO
    private var minSupportedZoom = MIN_ZOOM_RATIO
    private var zoomAnimator: ValueAnimator? = null
    
    // 回调接口
    interface ZoomListener {
        fun onZoomChanged(zoomRatio: Float)
        fun onZoomLimitsChanged(minZoom: Float, maxZoom: Float)
    }
    
    private var zoomListener: ZoomListener? = null
    
    init {
        observeZoomState()
    }
    
    fun setZoomListener(listener: ZoomListener) {
        this.zoomListener = listener
    }
    
    private fun observeZoomState() {
        camera.cameraInfo.zoomState.observe(lifecycleOwner, Observer { zoomState ->
            zoomState?.let {
                updateZoomLimits(it)
                currentZoomRatio = it.zoomRatio
                zoomListener?.onZoomChanged(currentZoomRatio)
            }
        })
    }
    
    private fun updateZoomLimits(zoomState: ZoomState) {
        minSupportedZoom = zoomState.minZoomRatio.coerceAtLeast(MIN_ZOOM_RATIO)
        maxSupportedZoom = zoomState.maxZoomRatio.coerceAtMost(MAX_ZOOM_RATIO)
        zoomListener?.onZoomLimitsChanged(minSupportedZoom, maxSupportedZoom)
    }
    
    /**
     * 设置变焦比例
     */
    fun setZoomRatio(ratio: Float, animated: Boolean = true) {
        val targetRatio = ratio.coerceIn(minSupportedZoom, maxSupportedZoom)

        if (animated) {
            animateZoomTo(targetRatio)
        } else {
            camera.cameraControl.setZoomRatio(targetRatio)
            // 变焦后触发自动对焦以获得更清晰的图像
            triggerAutoFocus()
        }
    }

    /**
     * 触发自动对焦
     */
    private fun triggerAutoFocus() {
        try {
            val action = FocusMeteringAction.Builder(
                camera.cameraInfo.sensorRotationDegrees.let {
                    // 使用屏幕中心点进行对焦
                    SurfaceOrientedMeteringPointFactory(1f, 1f).createPoint(0.5f, 0.5f)
                }
            ).build()

            camera.cameraControl.startFocusAndMetering(action)
        } catch (e: Exception) {
            android.util.Log.w("ZoomController", "Failed to trigger auto focus", e)
        }
    }
    
    /**
     * 平滑动画到目标变焦比例
     */
    private fun animateZoomTo(targetRatio: Float) {
        zoomAnimator?.cancel()

        zoomAnimator = ValueAnimator.ofFloat(currentZoomRatio, targetRatio).apply {
            duration = ZOOM_ANIMATION_DURATION
            interpolator = DecelerateInterpolator()

            addUpdateListener { animator ->
                val animatedValue = animator.animatedValue as Float
                camera.cameraControl.setZoomRatio(animatedValue)
            }

            // 动画结束后触发对焦
            addListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    triggerAutoFocus()
                }
            })

            start()
        }
    }
    
    /**
     * 放大
     */
    fun zoomIn(step: Float = DEFAULT_ZOOM_STEP) {
        val newRatio = (currentZoomRatio + step).coerceAtMost(maxSupportedZoom)
        setZoomRatio(newRatio)
    }
    
    /**
     * 缩小
     */
    fun zoomOut(step: Float = DEFAULT_ZOOM_STEP) {
        val newRatio = (currentZoomRatio - step).coerceAtLeast(minSupportedZoom)
        setZoomRatio(newRatio)
    }
    
    /**
     * 双击切换变焦（1x和之前的倍数之间切换）
     */
    fun toggleZoom() {
        if (currentZoomRatio == minSupportedZoom) {
            // 当前是最小倍数，切换到之前的倍数或默认4倍
            val targetRatio = if (previousZoomRatio > minSupportedZoom) {
                previousZoomRatio
            } else {
                4.0f.coerceAtMost(maxSupportedZoom)
            }
            setZoomRatio(targetRatio)
        } else {
            // 当前不是最小倍数，记录当前倍数并切换到最小倍数
            previousZoomRatio = currentZoomRatio
            setZoomRatio(minSupportedZoom)
        }
    }
    
    /**
     * 处理捏合手势变焦
     */
    fun handlePinchZoom(scaleFactor: Float) {
        val newRatio = (currentZoomRatio * scaleFactor).coerceIn(minSupportedZoom, maxSupportedZoom)
        setZoomRatio(newRatio, animated = false) // 手势变焦不使用动画
    }
    
    /**
     * 重置到最小变焦
     */
    fun resetZoom() {
        previousZoomRatio = currentZoomRatio
        setZoomRatio(minSupportedZoom)
    }
    
    /**
     * 设置到最大变焦
     */
    fun setMaxZoom() {
        setZoomRatio(maxSupportedZoom)
    }
    
    /**
     * 获取当前变焦比例
     */
    fun getCurrentZoomRatio(): Float = currentZoomRatio
    
    /**
     * 获取之前的变焦比例
     */
    fun getPreviousZoomRatio(): Float = previousZoomRatio
    
    /**
     * 获取支持的变焦范围
     */
    fun getZoomRange(): Pair<Float, Float> = Pair(minSupportedZoom, maxSupportedZoom)
    
    /**
     * 检查是否可以放大
     */
    fun canZoomIn(): Boolean = currentZoomRatio < maxSupportedZoom
    
    /**
     * 检查是否可以缩小
     */
    fun canZoomOut(): Boolean = currentZoomRatio > minSupportedZoom
    
    /**
     * 获取变焦百分比（0-100）
     */
    fun getZoomPercentage(): Int {
        val range = maxSupportedZoom - minSupportedZoom
        val current = currentZoomRatio - minSupportedZoom
        return ((current / range) * 100).toInt()
    }
    
    /**
     * 根据百分比设置变焦
     */
    fun setZoomByPercentage(percentage: Int) {
        val range = maxSupportedZoom - minSupportedZoom
        val targetRatio = minSupportedZoom + (range * percentage / 100f)
        setZoomRatio(targetRatio)
    }
    
    /**
     * 获取推荐的变焦级别
     */
    fun getRecommendedZoomLevels(): List<Float> {
        val levels = mutableListOf<Float>()
        
        // 添加1x
        levels.add(minSupportedZoom)
        
        // 添加常用倍数
        val commonLevels = listOf(2f, 4f, 6f, 8f, 10f, 12f, 16f)
        for (level in commonLevels) {
            if (level > minSupportedZoom && level <= maxSupportedZoom) {
                levels.add(level)
            }
        }
        
        return levels
    }
    
    /**
     * 跳转到最近的推荐变焦级别
     */
    fun snapToNearestLevel() {
        val levels = getRecommendedZoomLevels()
        val nearest = levels.minByOrNull { kotlin.math.abs(it - currentZoomRatio) }
        nearest?.let { setZoomRatio(it) }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        zoomAnimator?.cancel()
        zoomAnimator = null
        zoomListener = null
    }
}
