package com.magnifyingglass.app.utils

import android.app.Activity
import android.util.Log
import android.widget.ImageButton
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import com.magnifyingglass.app.MainActivity
import com.magnifyingglass.app.R
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 自动化测试管理器
 * 自动点击所有用户可操作的功能进行测试
 */
class AutoTestManager(private val activity: MainActivity) {
    
    companion object {
        private const val TAG = "AutoTestManager"
        private const val TEST_DELAY = 2000L // 每个测试间隔2秒
    }
    
    // 所有可点击的功能列表
    private val testFunctions = listOf(
        TestFunction("缩小按钮", R.id.zoomOutButton, "测试缩小功能"),
        TestFunction("手电筒按钮", R.id.flashlightButton, "测试手电筒开关"),
        TestFunction("冻结按钮", R.id.freezeButton, "测试冻结/拍照功能"),
        TestFunction("模式切换按钮", R.id.modeButton, "测试模式切换"),
        TestFunction("放大按钮", R.id.zoomInButton, "测试放大功能"),
        TestFunction("返回按钮", R.id.backButton, "测试返回功能（冻结状态下）"),
        TestFunction("保存按钮", R.id.saveButton, "测试保存图像功能（冻结状态下）"),
        TestFunction("增强按钮", R.id.enhanceButton, "测试图像增强功能（冻结状态下）"),
        TestFunction("Real-ESRGAN按钮", R.id.realESRGANButton, "测试Real-ESRGAN超分辨率（冻结状态下）")
    )
    
    data class TestFunction(
        val name: String,
        val buttonId: Int,
        val description: String,
        var tested: Boolean = false,
        var result: String = "未测试"
    )
    
    /**
     * 开始自动化测试
     */
    fun startAutoTest() {
        Log.d(TAG, "开始自动化测试")
        Toast.makeText(activity, "🤖 开始自动化测试", Toast.LENGTH_SHORT).show()
        
        activity.lifecycleScope.launch {
            try {
                // 1. 测试基础功能（正常状态下）
                testBasicFunctions()
                
                delay(TEST_DELAY)
                
                // 2. 进入冻结状态
                enterFrozenMode()
                
                delay(TEST_DELAY)
                
                // 3. 测试冻结状态下的功能
                testFrozenFunctions()
                
                // 4. 生成测试报告
                generateTestReport()
                
            } catch (e: Exception) {
                Log.e(TAG, "自动化测试失败", e)
                Toast.makeText(activity, "❌ 自动化测试失败: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }
    
    /**
     * 测试基础功能
     */
    private suspend fun testBasicFunctions() {
        Log.d(TAG, "测试基础功能")
        Toast.makeText(activity, "📱 测试基础功能", Toast.LENGTH_SHORT).show()
        
        val basicFunctions = listOf(
            R.id.zoomOutButton,
            R.id.flashlightButton,
            R.id.modeButton,
            R.id.zoomInButton
        )
        
        for (buttonId in basicFunctions) {
            testButton(buttonId)
            delay(TEST_DELAY)
        }
        
        // 测试冻结按钮（最后测试，因为会改变状态）
        testButton(R.id.freezeButton)
    }
    
    /**
     * 进入冻结模式
     */
    private suspend fun enterFrozenMode() {
        Log.d(TAG, "进入冻结模式")
        Toast.makeText(activity, "🧊 进入冻结模式", Toast.LENGTH_SHORT).show()
        
        val freezeButton = activity.findViewById<ImageButton>(R.id.freezeButton)
        activity.runOnUiThread {
            freezeButton.performClick()
        }
        
        delay(1000) // 等待冻结状态生效
    }
    
    /**
     * 测试冻结状态下的功能
     */
    private suspend fun testFrozenFunctions() {
        Log.d(TAG, "测试冻结状态功能")
        Toast.makeText(activity, "❄️ 测试冻结状态功能", Toast.LENGTH_SHORT).show()
        
        val frozenFunctions = listOf(
            R.id.saveButton,
            R.id.enhanceButton,
            R.id.realESRGANButton
        )
        
        for (buttonId in frozenFunctions) {
            testButton(buttonId)
            delay(TEST_DELAY)
        }
        
        // 最后测试返回按钮
        testButton(R.id.backButton)
    }
    
    /**
     * 测试单个按钮
     */
    private suspend fun testButton(buttonId: Int) {
        val testFunction = testFunctions.find { it.buttonId == buttonId }
        if (testFunction == null) {
            Log.w(TAG, "未找到按钮ID: $buttonId")
            return
        }
        
        Log.d(TAG, "测试: ${testFunction.name}")
        Toast.makeText(activity, "🔍 测试: ${testFunction.name}", Toast.LENGTH_SHORT).show()
        
        try {
            val button = activity.findViewById<ImageButton>(buttonId)
            if (button != null && button.visibility == android.view.View.VISIBLE) {
                activity.runOnUiThread {
                    button.performClick()
                }
                testFunction.tested = true
                testFunction.result = "✅ 成功"
                Log.d(TAG, "${testFunction.name} - 测试成功")
            } else {
                testFunction.result = "⚠️ 按钮不可见"
                Log.w(TAG, "${testFunction.name} - 按钮不可见或不存在")
            }
        } catch (e: Exception) {
            testFunction.result = "❌ 失败: ${e.message}"
            Log.e(TAG, "${testFunction.name} - 测试失败", e)
        }
    }
    
    /**
     * 生成测试报告
     */
    private fun generateTestReport() {
        Log.d(TAG, "生成测试报告")
        
        val report = StringBuilder()
        report.append("🤖 自动化测试报告\n")
        report.append("==================\n\n")
        
        var successCount = 0
        var totalCount = testFunctions.size
        
        for (testFunction in testFunctions) {
            report.append("${testFunction.name}: ${testFunction.result}\n")
            report.append("描述: ${testFunction.description}\n\n")
            
            if (testFunction.result.startsWith("✅")) {
                successCount++
            }
        }
        
        report.append("==================\n")
        report.append("测试结果: $successCount/$totalCount 成功\n")
        report.append("成功率: ${(successCount * 100 / totalCount)}%")
        
        Log.d(TAG, report.toString())
        
        // 显示测试结果
        activity.runOnUiThread {
            Toast.makeText(activity, "🎯 测试完成: $successCount/$totalCount 成功", Toast.LENGTH_LONG).show()
        }
        
        // 可以将报告保存到文件或显示在对话框中
        showTestReportDialog(report.toString())
    }
    
    /**
     * 显示测试报告对话框
     */
    private fun showTestReportDialog(report: String) {
        activity.runOnUiThread {
            android.app.AlertDialog.Builder(activity)
                .setTitle("🤖 自动化测试报告")
                .setMessage(report)
                .setPositiveButton("确定", null)
                .setNeutralButton("重新测试") { _, _ ->
                    // 重置测试状态
                    testFunctions.forEach { 
                        it.tested = false
                        it.result = "未测试"
                    }
                    startAutoTest()
                }
                .show()
        }
    }
    
    /**
     * 获取所有可点击功能的清单
     */
    fun getFunctionList(): List<TestFunction> {
        return testFunctions.toList()
    }
    
    /**
     * 手动测试单个功能
     */
    fun testSingleFunction(buttonId: Int) {
        activity.lifecycleScope.launch {
            testButton(buttonId)
        }
    }
}
