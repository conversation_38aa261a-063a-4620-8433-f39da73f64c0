package com.magnifyingglass.app.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.os.Handler
import android.os.Looper
import android.util.Log
import java.io.File

class PerformanceMonitor(private val context: Context) {
    
    companion object {
        private const val TAG = "PerformanceMonitor"
        private const val MEMORY_CHECK_INTERVAL = 5000L // 5秒检查一次内存
        private const val MEMORY_WARNING_THRESHOLD = 0.8f // 内存使用超过80%时警告
    }
    
    interface PerformanceListener {
        fun onMemoryWarning(usedMemoryMB: Long, totalMemoryMB: Long)
        fun onLowMemory()
        fun onPerformanceReport(report: PerformanceReport)
    }
    
    data class PerformanceReport(
        val appStartupTime: Long,
        val cameraInitTime: Long,
        val averageFrameTime: Long,
        val memoryUsageMB: Long,
        val apkSizeMB: Double,
        val batteryOptimized: Boolean
    )
    
    private var performanceListener: PerformanceListener? = null
    private val handler = Handler(Looper.getMainLooper())
    private var memoryCheckRunnable: Runnable? = null
    private var isMonitoring = false
    
    // 性能指标
    private var appStartTime = 0L
    private var cameraInitStartTime = 0L
    private var cameraInitTime = 0L
    private val frameTimings = mutableListOf<Long>()
    private var lastFrameTime = 0L
    
    fun setPerformanceListener(listener: PerformanceListener) {
        this.performanceListener = listener
    }
    
    /**
     * 开始性能监控
     */
    fun startMonitoring() {
        if (isMonitoring) return
        
        isMonitoring = true
        appStartTime = System.currentTimeMillis()
        
        startMemoryMonitoring()
        Log.d(TAG, "Performance monitoring started")
    }
    
    /**
     * 停止性能监控
     */
    fun stopMonitoring() {
        if (!isMonitoring) return
        
        isMonitoring = false
        stopMemoryMonitoring()
        
        // 生成性能报告
        generatePerformanceReport()
        
        Log.d(TAG, "Performance monitoring stopped")
    }
    
    /**
     * 记录相机初始化开始
     */
    fun recordCameraInitStart() {
        cameraInitStartTime = System.currentTimeMillis()
    }
    
    /**
     * 记录相机初始化完成
     */
    fun recordCameraInitComplete() {
        if (cameraInitStartTime > 0) {
            cameraInitTime = System.currentTimeMillis() - cameraInitStartTime
            Log.d(TAG, "Camera init time: ${cameraInitTime}ms")
        }
    }
    
    /**
     * 记录帧时间
     */
    fun recordFrameTime() {
        val currentTime = System.currentTimeMillis()
        if (lastFrameTime > 0) {
            val frameTime = currentTime - lastFrameTime
            frameTimings.add(frameTime)
            
            // 只保留最近100帧的数据
            if (frameTimings.size > 100) {
                frameTimings.removeAt(0)
            }
        }
        lastFrameTime = currentTime
    }
    
    /**
     * 获取当前内存使用情况
     */
    fun getCurrentMemoryUsage(): Pair<Long, Long> {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val totalMemory = memoryInfo.totalMem / (1024 * 1024) // MB
        val availableMemory = memoryInfo.availMem / (1024 * 1024) // MB
        val usedMemory = totalMemory - availableMemory
        
        return Pair(usedMemory, totalMemory)
    }
    
    /**
     * 获取应用内存使用情况
     */
    fun getAppMemoryUsage(): Long {
        val memoryInfo = Debug.MemoryInfo()
        Debug.getMemoryInfo(memoryInfo)
        return memoryInfo.totalPss / 1024L // MB
    }
    
    /**
     * 获取APK大小
     */
    fun getApkSize(): Double {
        return try {
            val apkFile = File(context.applicationInfo.sourceDir)
            apkFile.length() / (1024.0 * 1024.0) // MB
        } catch (e: Exception) {
            0.0
        }
    }
    
    /**
     * 检查是否启用了电池优化
     */
    fun isBatteryOptimized(): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            // 简化检查：如果是低内存设备，认为启用了电池优化
            activityManager.isLowRamDevice
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 开始内存监控
     */
    private fun startMemoryMonitoring() {
        memoryCheckRunnable = object : Runnable {
            override fun run() {
                if (isMonitoring) {
                    checkMemoryUsage()
                    handler.postDelayed(this, MEMORY_CHECK_INTERVAL)
                }
            }
        }
        handler.post(memoryCheckRunnable!!)
    }
    
    /**
     * 停止内存监控
     */
    private fun stopMemoryMonitoring() {
        memoryCheckRunnable?.let { handler.removeCallbacks(it) }
        memoryCheckRunnable = null
    }
    
    /**
     * 检查内存使用情况
     */
    private fun checkMemoryUsage() {
        val (usedMemory, totalMemory) = getCurrentMemoryUsage()
        val memoryUsageRatio = usedMemory.toFloat() / totalMemory.toFloat()
        
        if (memoryUsageRatio > MEMORY_WARNING_THRESHOLD) {
            performanceListener?.onMemoryWarning(usedMemory, totalMemory)
            Log.w(TAG, "High memory usage: ${usedMemory}MB / ${totalMemory}MB (${(memoryUsageRatio * 100).toInt()}%)")
        }
        
        // 检查系统低内存状态
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        if (memoryInfo.lowMemory) {
            performanceListener?.onLowMemory()
            Log.w(TAG, "System low memory detected")
        }
    }
    
    /**
     * 生成性能报告
     */
    private fun generatePerformanceReport() {
        val appStartupTime = if (appStartTime > 0) {
            System.currentTimeMillis() - appStartTime
        } else 0L
        
        val averageFrameTime = if (frameTimings.isNotEmpty()) {
            frameTimings.average().toLong()
        } else 0L
        
        val memoryUsage = getAppMemoryUsage()
        val apkSize = getApkSize()
        val batteryOptimized = isBatteryOptimized()
        
        val report = PerformanceReport(
            appStartupTime = appStartupTime,
            cameraInitTime = cameraInitTime,
            averageFrameTime = averageFrameTime,
            memoryUsageMB = memoryUsage,
            apkSizeMB = apkSize,
            batteryOptimized = batteryOptimized
        )
        
        performanceListener?.onPerformanceReport(report)
        
        Log.d(TAG, "Performance Report:")
        Log.d(TAG, "  App startup time: ${appStartupTime}ms")
        Log.d(TAG, "  Camera init time: ${cameraInitTime}ms")
        Log.d(TAG, "  Average frame time: ${averageFrameTime}ms")
        Log.d(TAG, "  Memory usage: ${memoryUsage}MB")
        Log.d(TAG, "  APK size: ${"%.2f".format(apkSize)}MB")
        Log.d(TAG, "  Battery optimized: $batteryOptimized")
    }
    
    /**
     * 强制垃圾回收
     */
    fun forceGarbageCollection() {
        System.gc()
        Log.d(TAG, "Forced garbage collection")
    }
    
    /**
     * 获取性能建议
     */
    fun getPerformanceRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        
        val (usedMemory, totalMemory) = getCurrentMemoryUsage()
        val memoryUsageRatio = usedMemory.toFloat() / totalMemory.toFloat()
        
        if (memoryUsageRatio > 0.8f) {
            recommendations.add("内存使用率过高，建议关闭其他应用")
        }
        
        if (cameraInitTime > 3000) {
            recommendations.add("相机初始化较慢，可能是设备性能限制")
        }
        
        val averageFrameTime = if (frameTimings.isNotEmpty()) {
            frameTimings.average()
        } else 0.0
        
        if (averageFrameTime > 33) { // 低于30fps
            recommendations.add("帧率较低，建议降低图像处理复杂度")
        }
        
        val apkSize = getApkSize()
        if (apkSize > 5.0) {
            recommendations.add("APK大小超过5MB，建议优化资源文件")
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("性能表现良好")
        }
        
        return recommendations
    }
}
