  Manifest android  R android  
permission android.Manifest  CAMERA android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  anim 	android.R  fade_in android.R.anim  fade_out android.R.anim  Animator android.animation  AnimatorListenerAdapter android.animation  AnimatorSet android.animation  ObjectAnimator android.animation  TimeInterpolator android.animation  
ValueAnimator android.animation  addListener android.animation.Animator  addUpdateListener android.animation.Animator  apply android.animation.Animator  cancel android.animation.Animator  play android.animation.Animator  playSequentially android.animation.Animator  playTogether android.animation.Animator  start android.animation.Animator  android )android.animation.AnimatorListenerAdapter  triggerAutoFocus )android.animation.AnimatorListenerAdapter   AccelerateDecelerateInterpolator android.animation.AnimatorSet  Builder android.animation.AnimatorSet  OvershootInterpolator android.animation.AnimatorSet  SCALE_DURATION android.animation.AnimatorSet  SLIDE_DURATION android.animation.AnimatorSet  apply android.animation.AnimatorSet  duration android.animation.AnimatorSet  getAPPLY android.animation.AnimatorSet  getApply android.animation.AnimatorSet  getDURATION android.animation.AnimatorSet  getDuration android.animation.AnimatorSet  getINTERPOLATOR android.animation.AnimatorSet  getInterpolator android.animation.AnimatorSet  
getSTARTDelay android.animation.AnimatorSet  
getStartDelay android.animation.AnimatorSet  interpolator android.animation.AnimatorSet  play android.animation.AnimatorSet  playSequentially android.animation.AnimatorSet  playTogether android.animation.AnimatorSet  setDuration android.animation.AnimatorSet  setInterpolator android.animation.AnimatorSet  
setStartDelay android.animation.AnimatorSet  start android.animation.AnimatorSet  
startDelay android.animation.AnimatorSet   AccelerateDecelerateInterpolator  android.animation.ObjectAnimator  BOUNCE_DURATION  android.animation.ObjectAnimator  BounceInterpolator  android.animation.ObjectAnimator  
FADE_DURATION  android.animation.ObjectAnimator  apply  android.animation.ObjectAnimator  duration  android.animation.ObjectAnimator  getAPPLY  android.animation.ObjectAnimator  getApply  android.animation.ObjectAnimator  getDURATION  android.animation.ObjectAnimator  getDuration  android.animation.ObjectAnimator  getINTERPOLATOR  android.animation.ObjectAnimator  getInterpolator  android.animation.ObjectAnimator  getREPEATCount  android.animation.ObjectAnimator  getRepeatCount  android.animation.ObjectAnimator  
getSTARTDelay  android.animation.ObjectAnimator  
getStartDelay  android.animation.ObjectAnimator  interpolator  android.animation.ObjectAnimator  ofFloat  android.animation.ObjectAnimator  repeatCount  android.animation.ObjectAnimator  setDuration  android.animation.ObjectAnimator  setInterpolator  android.animation.ObjectAnimator  setRepeatCount  android.animation.ObjectAnimator  
setStartDelay  android.animation.ObjectAnimator  start  android.animation.ObjectAnimator  
startDelay  android.animation.ObjectAnimator  DecelerateInterpolator android.animation.ValueAnimator  INFINITE android.animation.ValueAnimator  ZOOM_ANIMATION_DURATION android.animation.ValueAnimator  addListener android.animation.ValueAnimator  addUpdateListener android.animation.ValueAnimator  
animatedValue android.animation.ValueAnimator  apply android.animation.ValueAnimator  camera android.animation.ValueAnimator  cancel android.animation.ValueAnimator  duration android.animation.ValueAnimator  getANIMATEDValue android.animation.ValueAnimator  getAPPLY android.animation.ValueAnimator  getAnimatedValue android.animation.ValueAnimator  getApply android.animation.ValueAnimator  	getCAMERA android.animation.ValueAnimator  	getCamera android.animation.ValueAnimator  getDURATION android.animation.ValueAnimator  getDuration android.animation.ValueAnimator  getINTERPOLATOR android.animation.ValueAnimator  getInterpolator android.animation.ValueAnimator  getTRIGGERAutoFocus android.animation.ValueAnimator  getTriggerAutoFocus android.animation.ValueAnimator  interpolator android.animation.ValueAnimator  ofFloat android.animation.ValueAnimator  setAnimatedValue android.animation.ValueAnimator  setDuration android.animation.ValueAnimator  setInterpolator android.animation.ValueAnimator  start android.animation.ValueAnimator  triggerAutoFocus android.animation.ValueAnimator  <SAM-CONSTRUCTOR> 6android.animation.ValueAnimator.AnimatorUpdateListener  Activity android.app  ActivityManager android.app  ActivityMainBinding android.app.Activity  ActivityOnboardingBinding android.app.Activity  ActivityResultContracts android.app.Activity  ActivitySplashBinding android.app.Activity  Array android.app.Activity  Bitmap android.app.Activity  Boolean android.app.Activity  Bundle android.app.Activity  Camera android.app.Activity  CameraSelector android.app.Activity  Canvas android.app.Activity  Context android.app.Activity  
ContextCompat android.app.Activity  	Exception android.app.Activity  	ImageView android.app.Activity  Int android.app.Activity  Intent android.app.Activity  LayoutInflater android.app.Activity  LinearLayout android.app.Activity  List android.app.Activity  MainActivity android.app.Activity  Manifest android.app.Activity  OnboardingAdapter android.app.Activity  OnboardingAnimator android.app.Activity  OnboardingPage android.app.Activity  OnboardingViewHolder android.app.Activity  PackageManager android.app.Activity  Preview android.app.Activity  ProcessCameraProvider android.app.Activity  R android.app.Activity  RecyclerView android.app.Activity  SPLASH_DELAY android.app.Activity  TextView android.app.Activity  Toast android.app.Activity  View android.app.Activity  	ViewGroup android.app.Activity  
ViewPager2 android.app.Activity  addListener android.app.Activity  allPermissionsGranted android.app.Activity  also android.app.Activity  android android.app.Activity  apply android.app.Activity  camera android.app.Activity  captureCurrentFrame android.app.Activity  contentResolver android.app.Activity  currentPage android.app.Activity  delay android.app.Activity  finish android.app.Activity  finishOnboarding android.app.Activity  freezeFrame android.app.Activity  getCONTENTResolver android.app.Activity  getContentResolver android.app.Activity  getPACKAGEName android.app.Activity  getPackageName android.app.Activity  getSharedPreferences android.app.Activity  	getString android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  indices android.app.Activity  invoke android.app.Activity  
isInitialized android.app.Activity  java android.app.Activity  launch android.app.Activity  lifecycleScope android.app.Activity  listOf android.app.Activity  navigateToMain android.app.Activity  onCreate android.app.Activity  overridePendingTransition android.app.Activity  packageName android.app.Activity  registerForActivityResult android.app.Activity  saveCurrentFrame android.app.Activity  setContentResolver android.app.Activity  setContentView android.app.Activity  setPackageName android.app.Activity  	setWindow android.app.Activity  setupButtonListeners android.app.Activity  setupButtons android.app.Activity  setupIndicators android.app.Activity  setupViewPager android.app.Activity  
startActivity android.app.Activity  startCamera android.app.Activity  startEntryAnimations android.app.Activity  startSplashAnimation android.app.Activity  toggleFlashlight android.app.Activity  
unfreezeFrame android.app.Activity  until android.app.Activity  
updateButtons android.app.Activity  updateIndicators android.app.Activity  window android.app.Activity  
MemoryInfo android.app.ActivityManager  getISLowRamDevice android.app.ActivityManager  getIsLowRamDevice android.app.ActivityManager  
getMemoryInfo android.app.ActivityManager  isLowRamDevice android.app.ActivityManager  setLowRamDevice android.app.ActivityManager  availMem &android.app.ActivityManager.MemoryInfo  	lowMemory &android.app.ActivityManager.MemoryInfo  totalMem &android.app.ActivityManager.MemoryInfo  dismiss android.app.Dialog  show android.app.Dialog  ContentResolver android.content  
ContentValues android.content  Context android.content  DialogInterface android.content  Intent android.content  SharedPreferences android.content  insert android.content.ContentResolver  openOutputStream android.content.ContentResolver  update android.content.ContentResolver  Build android.content.ContentValues  FOLDER_NAME android.content.ContentValues  
MediaStore android.content.ContentValues  apply android.content.ContentValues  clear android.content.ContentValues  getAPPLY android.content.ContentValues  getApply android.content.ContentValues  put android.content.ContentValues  ACTIVITY_SERVICE android.content.Context  ActivityMainBinding android.content.Context  ActivityOnboardingBinding android.content.Context  ActivityResultContracts android.content.Context  ActivitySplashBinding android.content.Context  Array android.content.Context  Bitmap android.content.Context  Boolean android.content.Context  Bundle android.content.Context  CAMERA_SERVICE android.content.Context  Camera android.content.Context  CameraSelector android.content.Context  Canvas android.content.Context  Context android.content.Context  
ContextCompat android.content.Context  	Exception android.content.Context  	ImageView android.content.Context  Int android.content.Context  Intent android.content.Context  LayoutInflater android.content.Context  LinearLayout android.content.Context  List android.content.Context  MODE_PRIVATE android.content.Context  MainActivity android.content.Context  Manifest android.content.Context  OnboardingAdapter android.content.Context  OnboardingAnimator android.content.Context  OnboardingPage android.content.Context  OnboardingViewHolder android.content.Context  PackageManager android.content.Context  Preview android.content.Context  ProcessCameraProvider android.content.Context  R android.content.Context  RecyclerView android.content.Context  SENSOR_SERVICE android.content.Context  SPLASH_DELAY android.content.Context  TextView android.content.Context  Toast android.content.Context  View android.content.Context  	ViewGroup android.content.Context  
ViewPager2 android.content.Context  addListener android.content.Context  allPermissionsGranted android.content.Context  also android.content.Context  android android.content.Context  applicationInfo android.content.Context  apply android.content.Context  camera android.content.Context  captureCurrentFrame android.content.Context  contentResolver android.content.Context  currentPage android.content.Context  delay android.content.Context  finish android.content.Context  finishOnboarding android.content.Context  freezeFrame android.content.Context  getAPPLICATIONInfo android.content.Context  getApplicationInfo android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  getExternalFilesDir android.content.Context  getPACKAGEManager android.content.Context  getPackageManager android.content.Context  getRESOURCES android.content.Context  getResources android.content.Context  getSharedPreferences android.content.Context  	getString android.content.Context  getSystemService android.content.Context  indices android.content.Context  invoke android.content.Context  
isInitialized android.content.Context  java android.content.Context  launch android.content.Context  lifecycleScope android.content.Context  listOf android.content.Context  navigateToMain android.content.Context  onCreate android.content.Context  overridePendingTransition android.content.Context  packageManager android.content.Context  registerForActivityResult android.content.Context  	resources android.content.Context  saveCurrentFrame android.content.Context  setApplicationInfo android.content.Context  setContentResolver android.content.Context  setContentView android.content.Context  setPackageManager android.content.Context  setResources android.content.Context  setupButtonListeners android.content.Context  setupButtons android.content.Context  setupIndicators android.content.Context  setupViewPager android.content.Context  
startActivity android.content.Context  startCamera android.content.Context  startEntryAnimations android.content.Context  startSplashAnimation android.content.Context  toggleFlashlight android.content.Context  
unfreezeFrame android.content.Context  until android.content.Context  
updateButtons android.content.Context  updateIndicators android.content.Context  ActivityMainBinding android.content.ContextWrapper  ActivityOnboardingBinding android.content.ContextWrapper  ActivityResultContracts android.content.ContextWrapper  ActivitySplashBinding android.content.ContextWrapper  Array android.content.ContextWrapper  Bitmap android.content.ContextWrapper  Boolean android.content.ContextWrapper  Bundle android.content.ContextWrapper  Camera android.content.ContextWrapper  CameraSelector android.content.ContextWrapper  Canvas android.content.ContextWrapper  Context android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  	Exception android.content.ContextWrapper  	ImageView android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  LayoutInflater android.content.ContextWrapper  LinearLayout android.content.ContextWrapper  List android.content.ContextWrapper  MainActivity android.content.ContextWrapper  Manifest android.content.ContextWrapper  OnboardingAdapter android.content.ContextWrapper  OnboardingAnimator android.content.ContextWrapper  OnboardingPage android.content.ContextWrapper  OnboardingViewHolder android.content.ContextWrapper  PackageManager android.content.ContextWrapper  Preview android.content.ContextWrapper  ProcessCameraProvider android.content.ContextWrapper  R android.content.ContextWrapper  RecyclerView android.content.ContextWrapper  SPLASH_DELAY android.content.ContextWrapper  TextView android.content.ContextWrapper  Toast android.content.ContextWrapper  View android.content.ContextWrapper  	ViewGroup android.content.ContextWrapper  
ViewPager2 android.content.ContextWrapper  addListener android.content.ContextWrapper  allPermissionsGranted android.content.ContextWrapper  also android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  camera android.content.ContextWrapper  captureCurrentFrame android.content.ContextWrapper  currentPage android.content.ContextWrapper  delay android.content.ContextWrapper  finish android.content.ContextWrapper  finishOnboarding android.content.ContextWrapper  freezeFrame android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  	getString android.content.ContextWrapper  indices android.content.ContextWrapper  invoke android.content.ContextWrapper  
isInitialized android.content.ContextWrapper  java android.content.ContextWrapper  launch android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  listOf android.content.ContextWrapper  navigateToMain android.content.ContextWrapper  onCreate android.content.ContextWrapper  overridePendingTransition android.content.ContextWrapper  registerForActivityResult android.content.ContextWrapper  saveCurrentFrame android.content.ContextWrapper  setContentView android.content.ContextWrapper  setupButtonListeners android.content.ContextWrapper  setupButtons android.content.ContextWrapper  setupIndicators android.content.ContextWrapper  setupViewPager android.content.ContextWrapper  
startActivity android.content.ContextWrapper  startCamera android.content.ContextWrapper  startEntryAnimations android.content.ContextWrapper  startSplashAnimation android.content.ContextWrapper  toggleFlashlight android.content.ContextWrapper  
unfreezeFrame android.content.ContextWrapper  until android.content.ContextWrapper  
updateButtons android.content.ContextWrapper  updateIndicators android.content.ContextWrapper  dismiss android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  Uri android.content.Intent  activity android.content.Intent  apply android.content.Intent  data android.content.Intent  getACTIVITY android.content.Intent  getAPPLY android.content.Intent  getActivity android.content.Intent  getApply android.content.Intent  getDATA android.content.Intent  getData android.content.Intent  setData android.content.Intent  edit !android.content.SharedPreferences  getFloat !android.content.SharedPreferences  getInt !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  clear (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putFloat (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  PackageManager android.content.pm  	sourceDir "android.content.pm.ApplicationInfo  FEATURE_CAMERA_ANY !android.content.pm.PackageManager  FEATURE_CAMERA_AUTOFOCUS !android.content.pm.PackageManager  FEATURE_CAMERA_FLASH !android.content.pm.PackageManager  PERMISSION_GRANTED !android.content.pm.PackageManager  hasSystemFeature !android.content.pm.PackageManager  displayMetrics android.content.res.Resources  getDISPLAYMetrics android.content.res.Resources  getDisplayMetrics android.content.res.Resources  setDisplayMetrics android.content.res.Resources  Bitmap android.graphics  
BitmapFactory android.graphics  BlurMaskFilter android.graphics  Canvas android.graphics  Color android.graphics  
ColorDrawable android.graphics  ColorFilter android.graphics  ColorMatrix android.graphics  ColorMatrixColorFilter android.graphics  
FilterType android.graphics  IntArray android.graphics  JvmOverloads android.graphics  
MaskFilter android.graphics  Math android.graphics  Paint android.graphics  Path android.graphics  
PorterDuff android.graphics  PorterDuffXfermode android.graphics  RadialGradient android.graphics  Shader android.graphics  Xfermode android.graphics  apply android.graphics  coerceIn android.graphics  floatArrayOf android.graphics  
intArrayOf android.graphics  java android.graphics  
plusAssign android.graphics  until android.graphics  CompressFormat android.graphics.Bitmap  Config android.graphics.Bitmap  compress android.graphics.Bitmap  copy android.graphics.Bitmap  createBitmap android.graphics.Bitmap  createScaledBitmap android.graphics.Bitmap  equals android.graphics.Bitmap  	getHEIGHT android.graphics.Bitmap  	getHeight android.graphics.Bitmap  	getPixels android.graphics.Bitmap  getWIDTH android.graphics.Bitmap  getWidth android.graphics.Bitmap  height android.graphics.Bitmap  	setHeight android.graphics.Bitmap  	setPixels android.graphics.Bitmap  setWidth android.graphics.Bitmap  width android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  	ARGB_8888 android.graphics.Bitmap.Config  Blur android.graphics.BlurMaskFilter  NORMAL $android.graphics.BlurMaskFilter.Blur  
drawBitmap android.graphics.Canvas  
drawCircle android.graphics.Canvas  drawPath android.graphics.Canvas  drawRect android.graphics.Canvas  restoreToCount android.graphics.Canvas  	saveLayer android.graphics.Canvas  
HSVToColor android.graphics.Color  TRANSPARENT android.graphics.Color  blue android.graphics.Color  
colorToHSV android.graphics.Color  green android.graphics.Color  
parseColor android.graphics.Color  red android.graphics.Color  rgb android.graphics.Color  ColorMatrix android.graphics.ColorMatrix  apply android.graphics.ColorMatrix  equals android.graphics.ColorMatrix  floatArrayOf android.graphics.ColorMatrix  getAPPLY android.graphics.ColorMatrix  getApply android.graphics.ColorMatrix  getFLOATArrayOf android.graphics.ColorMatrix  getFloatArrayOf android.graphics.ColorMatrix  
postConcat android.graphics.ColorMatrix  set android.graphics.ColorMatrix  
setSaturation android.graphics.ColorMatrix  ANTI_ALIAS_FLAG android.graphics.Paint  BlurMaskFilter android.graphics.Paint  Color android.graphics.Paint  ColorMatrixColorFilter android.graphics.Paint  Paint android.graphics.Paint  
PorterDuff android.graphics.Paint  PorterDuffXfermode android.graphics.Paint  Style android.graphics.Paint  alpha android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  colorFilter android.graphics.Paint  getALPHA android.graphics.Paint  getAPPLY android.graphics.Paint  getAlpha android.graphics.Paint  getApply android.graphics.Paint  getCOLOR android.graphics.Paint  getCOLORFilter android.graphics.Paint  getColor android.graphics.Paint  getColorFilter android.graphics.Paint  
getMASKFilter android.graphics.Paint  
getMaskFilter android.graphics.Paint  	getSHADER android.graphics.Paint  getSTROKEWidth android.graphics.Paint  getSTYLE android.graphics.Paint  	getShader android.graphics.Paint  getStrokeWidth android.graphics.Paint  getStyle android.graphics.Paint  getXFERMODE android.graphics.Paint  getXfermode android.graphics.Paint  
maskFilter android.graphics.Paint  setAlpha android.graphics.Paint  setColor android.graphics.Paint  setColorFilter android.graphics.Paint  
setMaskFilter android.graphics.Paint  	setShader android.graphics.Paint  setStrokeWidth android.graphics.Paint  setStyle android.graphics.Paint  setXfermode android.graphics.Paint  shader android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  xfermode android.graphics.Paint  FILL android.graphics.Paint.Style  STROKE android.graphics.Paint.Style  close android.graphics.Path  lineTo android.graphics.Path  moveTo android.graphics.Path  Mode android.graphics.PorterDuff  CLEAR  android.graphics.PorterDuff.Mode  TileMode android.graphics.Shader  CLAMP  android.graphics.Shader.TileMode  BitmapDrawable android.graphics.drawable  
ColorDrawable android.graphics.drawable  Drawable android.graphics.drawable  bitmap (android.graphics.drawable.BitmapDrawable  	getBITMAP (android.graphics.drawable.BitmapDrawable  	getBitmap (android.graphics.drawable.BitmapDrawable  	setBitmap (android.graphics.drawable.BitmapDrawable  apply 'android.graphics.drawable.ColorDrawable  colorFilter 'android.graphics.drawable.ColorDrawable  getAPPLY 'android.graphics.drawable.ColorDrawable  getApply 'android.graphics.drawable.ColorDrawable  getCOLORFilter 'android.graphics.drawable.ColorDrawable  getColorFilter 'android.graphics.drawable.ColorDrawable  setColorFilter 'android.graphics.drawable.ColorDrawable  apply "android.graphics.drawable.Drawable  Sensor android.hardware  SensorEvent android.hardware  SensorEventListener android.hardware  
SensorManager android.hardware  TYPE_ACCELEROMETER android.hardware.Sensor  TYPE_GYROSCOPE android.hardware.Sensor  equals android.hardware.Sensor  getLET android.hardware.Sensor  getLet android.hardware.Sensor  getNAME android.hardware.Sensor  getName android.hardware.Sensor  getTYPE android.hardware.Sensor  getType android.hardware.Sensor  let android.hardware.Sensor  name android.hardware.Sensor  setName android.hardware.Sensor  setType android.hardware.Sensor  type android.hardware.Sensor  getLET android.hardware.SensorEvent  getLet android.hardware.SensorEvent  let android.hardware.SensorEvent  sensor android.hardware.SensorEvent  	timestamp android.hardware.SensorEvent  values android.hardware.SensorEvent  
GRAVITY_EARTH android.hardware.SensorManager  SENSOR_DELAY_FASTEST android.hardware.SensorManager  getDefaultSensor android.hardware.SensorManager  registerListener android.hardware.SensorManager  unregisterListener android.hardware.SensorManager  CameraCharacteristics android.hardware.camera2  
CameraManager android.hardware.camera2  CONTROL_AF_AVAILABLE_MODES .android.hardware.camera2.CameraCharacteristics  CONTROL_AF_MODE_AUTO .android.hardware.camera2.CameraCharacteristics   CONTROL_AF_MODE_CONTINUOUS_VIDEO .android.hardware.camera2.CameraCharacteristics  +CONTROL_AVAILABLE_VIDEO_STABILIZATION_MODES .android.hardware.camera2.CameraCharacteristics  #CONTROL_VIDEO_STABILIZATION_MODE_ON .android.hardware.camera2.CameraCharacteristics  Key .android.hardware.camera2.CameraCharacteristics  )LENS_INFO_AVAILABLE_OPTICAL_STABILIZATION .android.hardware.camera2.CameraCharacteristics   LENS_INFO_MINIMUM_FOCUS_DISTANCE .android.hardware.camera2.CameraCharacteristics  "LENS_OPTICAL_STABILIZATION_MODE_ON .android.hardware.camera2.CameraCharacteristics  get .android.hardware.camera2.CameraCharacteristics  getCameraCharacteristics &android.hardware.camera2.CameraManager  get 'android.hardware.camera2.CameraMetadata  Uri android.net  equals android.net.Uri  	fromParts android.net.Uri  Build 
android.os  Bundle 
android.os  Debug 
android.os  Environment 
android.os  Handler 
android.os  Looper 
android.os  BRAND android.os.Build  MANUFACTURER android.os.Build  MODEL android.os.Build  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  Q android.os.Build.VERSION_CODES  
MemoryInfo android.os.Debug  
getMemoryInfo android.os.Debug  getTOTALPss android.os.Debug.MemoryInfo  getTotalPss android.os.Debug.MemoryInfo  setTotalPss android.os.Debug.MemoryInfo  totalPss android.os.Debug.MemoryInfo  DIRECTORY_PICTURES android.os.Environment  getExternalStorageDirectory android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  post android.os.Handler  postDelayed android.os.Handler  removeCallbacks android.os.Handler  removeCallbacksAndMessages android.os.Handler  
getMainLooper android.os.Looper  
MediaStore android.provider  Settings android.provider  Images android.provider.MediaStore  MediaColumns android.provider.MediaStore  Media "android.provider.MediaStore.Images  DATA (android.provider.MediaStore.Images.Media  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  	MIME_TYPE (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  
IS_PENDING (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  #ACTION_APPLICATION_DETAILS_SETTINGS android.provider.Settings  ACTION_SETTINGS android.provider.Settings  System android.provider.Settings  SCREEN_BRIGHTNESS  android.provider.Settings.System  canWrite  android.provider.Settings.System  getInt  android.provider.Settings.System  BlurMaskFilter android.renderscript  Canvas android.renderscript  Color android.renderscript  JvmOverloads android.renderscript  Math android.renderscript  Paint android.renderscript  Path android.renderscript  
PorterDuff android.renderscript  PorterDuffXfermode android.renderscript  RadialGradient android.renderscript  Shader android.renderscript  apply android.renderscript  floatArrayOf android.renderscript  
intArrayOf android.renderscript  java android.renderscript  until android.renderscript  AttributeSet android.util  Log android.util  Range android.util  density android.util.DisplayMetrics  widthPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  w android.util.Log  getLOWER android.util.Range  getLower android.util.Range  getUPPER android.util.Range  getUpper android.util.Range  lower android.util.Range  setLower android.util.Range  setUpper android.util.Range  upper android.util.Range  toFloat android.util.Rational  GestureDetector android.view  LayoutInflater android.view  MotionEvent android.view  ScaleGestureDetector android.view  View android.view  	ViewGroup android.view  Window android.view  
WindowManager android.view  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityOnboardingBinding  android.view.ContextThemeWrapper  ActivityResultContracts  android.view.ContextThemeWrapper  ActivitySplashBinding  android.view.ContextThemeWrapper  Array  android.view.ContextThemeWrapper  Bitmap  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  Camera  android.view.ContextThemeWrapper  CameraSelector  android.view.ContextThemeWrapper  Canvas  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  	ImageView  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  LayoutInflater  android.view.ContextThemeWrapper  LinearLayout  android.view.ContextThemeWrapper  List  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  OnboardingAdapter  android.view.ContextThemeWrapper  OnboardingAnimator  android.view.ContextThemeWrapper  OnboardingPage  android.view.ContextThemeWrapper  OnboardingViewHolder  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  Preview  android.view.ContextThemeWrapper  ProcessCameraProvider  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  RecyclerView  android.view.ContextThemeWrapper  SPLASH_DELAY  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  	ViewGroup  android.view.ContextThemeWrapper  
ViewPager2  android.view.ContextThemeWrapper  addListener  android.view.ContextThemeWrapper  allPermissionsGranted  android.view.ContextThemeWrapper  also  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  camera  android.view.ContextThemeWrapper  captureCurrentFrame  android.view.ContextThemeWrapper  currentPage  android.view.ContextThemeWrapper  delay  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  finishOnboarding  android.view.ContextThemeWrapper  freezeFrame  android.view.ContextThemeWrapper  getSharedPreferences  android.view.ContextThemeWrapper  	getString  android.view.ContextThemeWrapper  indices  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  
isInitialized  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  navigateToMain  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  overridePendingTransition  android.view.ContextThemeWrapper  registerForActivityResult  android.view.ContextThemeWrapper  saveCurrentFrame  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  setupButtonListeners  android.view.ContextThemeWrapper  setupButtons  android.view.ContextThemeWrapper  setupIndicators  android.view.ContextThemeWrapper  setupViewPager  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  startCamera  android.view.ContextThemeWrapper  startEntryAnimations  android.view.ContextThemeWrapper  startSplashAnimation  android.view.ContextThemeWrapper  toggleFlashlight  android.view.ContextThemeWrapper  
unfreezeFrame  android.view.ContextThemeWrapper  until  android.view.ContextThemeWrapper  
updateButtons  android.view.ContextThemeWrapper  updateIndicators  android.view.ContextThemeWrapper  SimpleOnGestureListener android.view.GestureDetector  onTouchEvent android.view.GestureDetector  Boolean 4android.view.GestureDetector.SimpleOnGestureListener  MotionEvent 4android.view.GestureDetector.SimpleOnGestureListener  gestureListener 4android.view.GestureDetector.SimpleOnGestureListener  isEdgeSwipeActive 4android.view.GestureDetector.SimpleOnGestureListener  isLongPressTriggered 4android.view.GestureDetector.SimpleOnGestureListener  from android.view.LayoutInflater  inflate android.view.LayoutInflater  
ACTION_CANCEL android.view.MotionEvent  ACTION_DOWN android.view.MotionEvent  ACTION_MOVE android.view.MotionEvent  	ACTION_UP android.view.MotionEvent  action android.view.MotionEvent  	getACTION android.view.MotionEvent  	getAction android.view.MotionEvent  getX android.view.MotionEvent  getY android.view.MotionEvent  	setAction android.view.MotionEvent  setX android.view.MotionEvent  setY android.view.MotionEvent  x android.view.MotionEvent  y android.view.MotionEvent  SimpleOnScaleGestureListener !android.view.ScaleGestureDetector  getISInProgress !android.view.ScaleGestureDetector  getIsInProgress !android.view.ScaleGestureDetector  getSCALEFactor !android.view.ScaleGestureDetector  getScaleFactor !android.view.ScaleGestureDetector  isInProgress !android.view.ScaleGestureDetector  onTouchEvent !android.view.ScaleGestureDetector  scaleFactor !android.view.ScaleGestureDetector  
setInProgress !android.view.ScaleGestureDetector  setScaleFactor !android.view.ScaleGestureDetector  Boolean >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  ScaleGestureDetector >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  cancelLongPress >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  gestureListener >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  AttributeSet android.view.View  BlurMaskFilter android.view.View  Canvas android.view.View  Color android.view.View  Context android.view.View  Float android.view.View  GONE android.view.View  Int android.view.View  JvmOverloads android.view.View  Math android.view.View  Paint android.view.View  Path android.view.View  
PorterDuff android.view.View  PorterDuffXfermode android.view.View  RadialGradient android.view.View  Shader android.view.View  VISIBLE android.view.View  addView android.view.View  alpha android.view.View  animate android.view.View  apply android.view.View  draw android.view.View  drawFrostedGlassBackground android.view.View  drawMagnifierHandle android.view.View  findViewById android.view.View  floatArrayOf android.view.View  getALPHA android.view.View  getAlpha android.view.View  
getChildAt android.view.View  	getHEIGHT android.view.View  	getHeight android.view.View  	getScaleX android.view.View  	getScaleY android.view.View  getTranslationX android.view.View  getTranslationY android.view.View  getWIDTH android.view.View  getWidth android.view.View  height android.view.View  
intArrayOf android.view.View  
invalidate android.view.View  java android.view.View  onDraw android.view.View  
onSizeChanged android.view.View  registerOnPageChangeCallback android.view.View  scaleX android.view.View  scaleY android.view.View  setAlpha android.view.View  setColorFilter android.view.View  	setHeight android.view.View  setImageBitmap android.view.View  setImageDrawable android.view.View  setImageResource android.view.View  setOnClickListener android.view.View  setOnLongClickListener android.view.View  	setScaleX android.view.View  	setScaleY android.view.View  setText android.view.View  setTranslationX android.view.View  setTranslationY android.view.View  setWidth android.view.View  translationX android.view.View  translationY android.view.View  until android.view.View  width android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  <SAM-CONSTRUCTOR> %android.view.View.OnLongClickListener  LayoutParams android.view.ViewGroup  addView android.view.ViewGroup  context android.view.ViewGroup  draw android.view.ViewGroup  
getCONTEXT android.view.ViewGroup  
getChildAt android.view.ViewGroup  
getContext android.view.ViewGroup  registerOnPageChangeCallback android.view.ViewGroup  
setContext android.view.ViewGroup  setOnLongClickListener android.view.ViewGroup  WRAP_CONTENT #android.view.ViewGroup.LayoutParams  apply #android.view.ViewGroup.LayoutParams  
setMargins #android.view.ViewGroup.LayoutParams  apply )android.view.ViewGroup.MarginLayoutParams  
setMargins )android.view.ViewGroup.MarginLayoutParams  alpha !android.view.ViewPropertyAnimator  setDuration !android.view.ViewPropertyAnimator  
setStartDelay !android.view.ViewPropertyAnimator  start !android.view.ViewPropertyAnimator  
attributes android.view.Window  
getATTRIBUTES android.view.Window  
getAttributes android.view.Window  
setAttributes android.view.Window  setFlags android.view.Window  LayoutParams android.view.WindowManager  BRIGHTNESS_OVERRIDE_NONE 'android.view.WindowManager.LayoutParams  FLAG_FULLSCREEN 'android.view.WindowManager.LayoutParams  screenBrightness 'android.view.WindowManager.LayoutParams   AccelerateDecelerateInterpolator android.view.animation  BounceInterpolator android.view.animation  DecelerateInterpolator android.view.animation  OvershootInterpolator android.view.animation  Button android.widget  	ImageView android.widget  LinearLayout android.widget  TextView android.widget  Toast android.widget  getTEXT android.widget.Button  getText android.widget.Button  setOnClickListener android.widget.Button  setText android.widget.Button  text android.widget.Button  draw android.widget.FrameLayout  setOnLongClickListener android.widget.FrameLayout  setOnClickListener android.widget.ImageButton  
ContextCompat android.widget.ImageView  R android.widget.ImageView  alpha android.widget.ImageView  animate android.widget.ImageView  apply android.widget.ImageView  drawable android.widget.ImageView  getALPHA android.widget.ImageView  getAPPLY android.widget.ImageView  getAlpha android.widget.ImageView  getApply android.widget.ImageView  getDRAWABLE android.widget.ImageView  getDrawable android.widget.ImageView  getLAYOUTParams android.widget.ImageView  getLayoutParams android.widget.ImageView  	getScaleX android.widget.ImageView  	getScaleY android.widget.ImageView  
getVISIBILITY android.widget.ImageView  
getVisibility android.widget.ImageView  layoutParams android.widget.ImageView  scaleX android.widget.ImageView  scaleY android.widget.ImageView  setAlpha android.widget.ImageView  setColorFilter android.widget.ImageView  setDrawable android.widget.ImageView  setImageBitmap android.widget.ImageView  setImageDrawable android.widget.ImageView  setImageResource android.widget.ImageView  setLayoutParams android.widget.ImageView  setOnClickListener android.widget.ImageView  	setScaleX android.widget.ImageView  	setScaleY android.widget.ImageView  
setVisibility android.widget.ImageView  
visibility android.widget.ImageView  LayoutParams android.widget.LinearLayout  addView android.widget.LinearLayout  
childCount android.widget.LinearLayout  
getCHILDCount android.widget.LinearLayout  
getChildAt android.widget.LinearLayout  
getChildCount android.widget.LinearLayout  
setChildCount android.widget.LinearLayout  apply (android.widget.LinearLayout.LayoutParams  getAPPLY (android.widget.LinearLayout.LayoutParams  getApply (android.widget.LinearLayout.LayoutParams  
setMargins (android.widget.LinearLayout.LayoutParams  alpha android.widget.TextView  animate android.widget.TextView  getALPHA android.widget.TextView  getAlpha android.widget.TextView  getTEXT android.widget.TextView  getText android.widget.TextView  setAlpha android.widget.TextView  setOnClickListener android.widget.TextView  setText android.widget.TextView  text android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityOnboardingBinding #androidx.activity.ComponentActivity  ActivityResultContracts #androidx.activity.ComponentActivity  ActivitySplashBinding #androidx.activity.ComponentActivity  Array #androidx.activity.ComponentActivity  Bitmap #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Camera #androidx.activity.ComponentActivity  CameraSelector #androidx.activity.ComponentActivity  Canvas #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  	ImageView #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  LayoutInflater #androidx.activity.ComponentActivity  LinearLayout #androidx.activity.ComponentActivity  List #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  OnboardingAdapter #androidx.activity.ComponentActivity  OnboardingAnimator #androidx.activity.ComponentActivity  OnboardingPage #androidx.activity.ComponentActivity  OnboardingViewHolder #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  Preview #androidx.activity.ComponentActivity  ProcessCameraProvider #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  RecyclerView #androidx.activity.ComponentActivity  SPLASH_DELAY #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  	ViewGroup #androidx.activity.ComponentActivity  
ViewPager2 #androidx.activity.ComponentActivity  addListener #androidx.activity.ComponentActivity  allPermissionsGranted #androidx.activity.ComponentActivity  also #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  camera #androidx.activity.ComponentActivity  captureCurrentFrame #androidx.activity.ComponentActivity  currentPage #androidx.activity.ComponentActivity  delay #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  finishOnboarding #androidx.activity.ComponentActivity  freezeFrame #androidx.activity.ComponentActivity  getSharedPreferences #androidx.activity.ComponentActivity  	getString #androidx.activity.ComponentActivity  indices #androidx.activity.ComponentActivity  invoke #androidx.activity.ComponentActivity  
isInitialized #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  navigateToMain #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  overridePendingTransition #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  saveCurrentFrame #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  setupButtonListeners #androidx.activity.ComponentActivity  setupButtons #androidx.activity.ComponentActivity  setupIndicators #androidx.activity.ComponentActivity  setupViewPager #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  startCamera #androidx.activity.ComponentActivity  startEntryAnimations #androidx.activity.ComponentActivity  startSplashAnimation #androidx.activity.ComponentActivity  toggleFlashlight #androidx.activity.ComponentActivity  
unfreezeFrame #androidx.activity.ComponentActivity  until #androidx.activity.ComponentActivity  
updateButtons #androidx.activity.ComponentActivity  updateIndicators #androidx.activity.ComponentActivity  dismiss !androidx.activity.ComponentDialog  show !androidx.activity.ComponentDialog  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  Builder "androidx.appcompat.app.AlertDialog  dismiss "androidx.appcompat.app.AlertDialog  show "androidx.appcompat.app.AlertDialog  create *androidx.appcompat.app.AlertDialog.Builder  
setCancelable *androidx.appcompat.app.AlertDialog.Builder  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  setView *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityOnboardingBinding (androidx.appcompat.app.AppCompatActivity  ActivityResultContracts (androidx.appcompat.app.AppCompatActivity  ActivitySplashBinding (androidx.appcompat.app.AppCompatActivity  Array (androidx.appcompat.app.AppCompatActivity  Bitmap (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  Camera (androidx.appcompat.app.AppCompatActivity  CameraSelector (androidx.appcompat.app.AppCompatActivity  Canvas (androidx.appcompat.app.AppCompatActivity  Context (androidx.appcompat.app.AppCompatActivity  
ContextCompat (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  	ImageView (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  LayoutInflater (androidx.appcompat.app.AppCompatActivity  LinearLayout (androidx.appcompat.app.AppCompatActivity  List (androidx.appcompat.app.AppCompatActivity  MainActivity (androidx.appcompat.app.AppCompatActivity  Manifest (androidx.appcompat.app.AppCompatActivity  OnboardingAdapter (androidx.appcompat.app.AppCompatActivity  OnboardingAnimator (androidx.appcompat.app.AppCompatActivity  OnboardingPage (androidx.appcompat.app.AppCompatActivity  OnboardingViewHolder (androidx.appcompat.app.AppCompatActivity  PackageManager (androidx.appcompat.app.AppCompatActivity  Preview (androidx.appcompat.app.AppCompatActivity  ProcessCameraProvider (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  RecyclerView (androidx.appcompat.app.AppCompatActivity  SPLASH_DELAY (androidx.appcompat.app.AppCompatActivity  TextView (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  	ViewGroup (androidx.appcompat.app.AppCompatActivity  
ViewPager2 (androidx.appcompat.app.AppCompatActivity  addListener (androidx.appcompat.app.AppCompatActivity  allPermissionsGranted (androidx.appcompat.app.AppCompatActivity  also (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  camera (androidx.appcompat.app.AppCompatActivity  captureCurrentFrame (androidx.appcompat.app.AppCompatActivity  currentPage (androidx.appcompat.app.AppCompatActivity  delay (androidx.appcompat.app.AppCompatActivity  finish (androidx.appcompat.app.AppCompatActivity  finishOnboarding (androidx.appcompat.app.AppCompatActivity  freezeFrame (androidx.appcompat.app.AppCompatActivity  getSharedPreferences (androidx.appcompat.app.AppCompatActivity  	getString (androidx.appcompat.app.AppCompatActivity  indices (androidx.appcompat.app.AppCompatActivity  invoke (androidx.appcompat.app.AppCompatActivity  
isInitialized (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  launch (androidx.appcompat.app.AppCompatActivity  lifecycleScope (androidx.appcompat.app.AppCompatActivity  listOf (androidx.appcompat.app.AppCompatActivity  navigateToMain (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  overridePendingTransition (androidx.appcompat.app.AppCompatActivity  registerForActivityResult (androidx.appcompat.app.AppCompatActivity  saveCurrentFrame (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  setupButtonListeners (androidx.appcompat.app.AppCompatActivity  setupButtons (androidx.appcompat.app.AppCompatActivity  setupIndicators (androidx.appcompat.app.AppCompatActivity  setupViewPager (androidx.appcompat.app.AppCompatActivity  
startActivity (androidx.appcompat.app.AppCompatActivity  startCamera (androidx.appcompat.app.AppCompatActivity  startEntryAnimations (androidx.appcompat.app.AppCompatActivity  startSplashAnimation (androidx.appcompat.app.AppCompatActivity  toggleFlashlight (androidx.appcompat.app.AppCompatActivity  
unfreezeFrame (androidx.appcompat.app.AppCompatActivity  until (androidx.appcompat.app.AppCompatActivity  
updateButtons (androidx.appcompat.app.AppCompatActivity  updateIndicators (androidx.appcompat.app.AppCompatActivity  dismiss &androidx.appcompat.app.AppCompatDialog  show &androidx.appcompat.app.AppCompatDialog  Camera2CameraInfo androidx.camera.camera2.interop  Camera2Interop androidx.camera.camera2.interop  ExperimentalCamera2Interop androidx.camera.camera2.interop  cameraId 1androidx.camera.camera2.interop.Camera2CameraInfo  from 1androidx.camera.camera2.interop.Camera2CameraInfo  getCAMERAId 1androidx.camera.camera2.interop.Camera2CameraInfo  getCameraId 1androidx.camera.camera2.interop.Camera2CameraInfo  setCameraId 1androidx.camera.camera2.interop.Camera2CameraInfo  ActivityMainBinding androidx.camera.core  ActivityResultContracts androidx.camera.core  Bitmap androidx.camera.core  Camera androidx.camera.core  
CameraInfo androidx.camera.core  CameraSelector androidx.camera.core  Canvas androidx.camera.core  
ContextCompat androidx.camera.core  	Exception androidx.camera.core  
ExposureState androidx.camera.core  FocusMeteringAction androidx.camera.core  FocusMeteringResult androidx.camera.core  Manifest androidx.camera.core  
MeteringPoint androidx.camera.core  PackageManager androidx.camera.core  Preview androidx.camera.core  ProcessCameraProvider androidx.camera.core  #SurfaceOrientedMeteringPointFactory androidx.camera.core  Toast androidx.camera.core  View androidx.camera.core  	ZoomState androidx.camera.core  addListener androidx.camera.core  also androidx.camera.core  android androidx.camera.core  
isInitialized androidx.camera.core  
cameraControl androidx.camera.core.Camera  
cameraInfo androidx.camera.core.Camera  getCAMERAControl androidx.camera.core.Camera  
getCAMERAInfo androidx.camera.core.Camera  getCameraControl androidx.camera.core.Camera  
getCameraInfo androidx.camera.core.Camera  getLET androidx.camera.core.Camera  getLet androidx.camera.core.Camera  let androidx.camera.core.Camera  setCameraControl androidx.camera.core.Camera  
setCameraInfo androidx.camera.core.Camera  enableTorch "androidx.camera.core.CameraControl  setExposureCompensationIndex "androidx.camera.core.CameraControl  setZoomRatio "androidx.camera.core.CameraControl  startFocusAndMetering "androidx.camera.core.CameraControl  
exposureState androidx.camera.core.CameraInfo  getEXPOSUREState androidx.camera.core.CameraInfo  getExposureState androidx.camera.core.CameraInfo  getSENSORRotationDegrees androidx.camera.core.CameraInfo  getSensorRotationDegrees androidx.camera.core.CameraInfo  getZOOMState androidx.camera.core.CameraInfo  getZoomState androidx.camera.core.CameraInfo  hasFlashUnit androidx.camera.core.CameraInfo  sensorRotationDegrees androidx.camera.core.CameraInfo  setExposureState androidx.camera.core.CameraInfo  setSensorRotationDegrees androidx.camera.core.CameraInfo  setZoomState androidx.camera.core.CameraInfo  	zoomState androidx.camera.core.CameraInfo  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  exposureCompensationRange "androidx.camera.core.ExposureState  exposureCompensationStep "androidx.camera.core.ExposureState  getEXPOSURECompensationRange "androidx.camera.core.ExposureState  getEXPOSURECompensationStep "androidx.camera.core.ExposureState  getExposureCompensationRange "androidx.camera.core.ExposureState  getExposureCompensationStep "androidx.camera.core.ExposureState  "getISExposureCompensationSupported "androidx.camera.core.ExposureState  "getIsExposureCompensationSupported "androidx.camera.core.ExposureState  isExposureCompensationSupported "androidx.camera.core.ExposureState  setExposureCompensationRange "androidx.camera.core.ExposureState  setExposureCompensationStep "androidx.camera.core.ExposureState   setExposureCompensationSupported "androidx.camera.core.ExposureState  Builder (androidx.camera.core.FocusMeteringAction  build 0androidx.camera.core.FocusMeteringAction.Builder  createPoint )androidx.camera.core.MeteringPointFactory  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  getALSO androidx.camera.core.Preview  getAlso androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  createPoint 8androidx.camera.core.SurfaceOrientedMeteringPointFactory  also androidx.camera.core.UseCase  setSurfaceProvider androidx.camera.core.UseCase  getLET androidx.camera.core.ZoomState  getLet androidx.camera.core.ZoomState  getMAXZoomRatio androidx.camera.core.ZoomState  getMINZoomRatio androidx.camera.core.ZoomState  getMaxZoomRatio androidx.camera.core.ZoomState  getMinZoomRatio androidx.camera.core.ZoomState  getZOOMRatio androidx.camera.core.ZoomState  getZoomRatio androidx.camera.core.ZoomState  let androidx.camera.core.ZoomState  maxZoomRatio androidx.camera.core.ZoomState  minZoomRatio androidx.camera.core.ZoomState  setMaxZoomRatio androidx.camera.core.ZoomState  setMinZoomRatio androidx.camera.core.ZoomState  setZoomRatio androidx.camera.core.ZoomState  	zoomRatio androidx.camera.core.ZoomState  ProcessCameraProvider androidx.camera.lifecycle  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  PreviewView androidx.camera.view  alpha  androidx.camera.view.PreviewView  draw  androidx.camera.view.PreviewView  
foreground  androidx.camera.view.PreviewView  getALPHA  androidx.camera.view.PreviewView  getAlpha  androidx.camera.view.PreviewView  
getFOREGROUND  androidx.camera.view.PreviewView  
getForeground  androidx.camera.view.PreviewView  	getHEIGHT  androidx.camera.view.PreviewView  	getHeight  androidx.camera.view.PreviewView  getSURFACEProvider  androidx.camera.view.PreviewView  getSurfaceProvider  androidx.camera.view.PreviewView  getWIDTH  androidx.camera.view.PreviewView  getWidth  androidx.camera.view.PreviewView  height  androidx.camera.view.PreviewView  setAlpha  androidx.camera.view.PreviewView  
setForeground  androidx.camera.view.PreviewView  	setHeight  androidx.camera.view.PreviewView  setOnLongClickListener  androidx.camera.view.PreviewView  setSurfaceProvider  androidx.camera.view.PreviewView  setWidth  androidx.camera.view.PreviewView  surfaceProvider  androidx.camera.view.PreviewView  width  androidx.camera.view.PreviewView  ConstraintLayout  androidx.constraintlayout.widget  ActivityCompat androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  $shouldShowRequestPermissionRationale  androidx.core.app.ActivityCompat  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityOnboardingBinding #androidx.core.app.ComponentActivity  ActivityResultContracts #androidx.core.app.ComponentActivity  ActivitySplashBinding #androidx.core.app.ComponentActivity  Array #androidx.core.app.ComponentActivity  Bitmap #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Camera #androidx.core.app.ComponentActivity  CameraSelector #androidx.core.app.ComponentActivity  Canvas #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  	ImageView #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  LayoutInflater #androidx.core.app.ComponentActivity  LinearLayout #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  OnboardingAdapter #androidx.core.app.ComponentActivity  OnboardingAnimator #androidx.core.app.ComponentActivity  OnboardingPage #androidx.core.app.ComponentActivity  OnboardingViewHolder #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  Preview #androidx.core.app.ComponentActivity  ProcessCameraProvider #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  RecyclerView #androidx.core.app.ComponentActivity  SPLASH_DELAY #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  	ViewGroup #androidx.core.app.ComponentActivity  
ViewPager2 #androidx.core.app.ComponentActivity  addListener #androidx.core.app.ComponentActivity  allPermissionsGranted #androidx.core.app.ComponentActivity  also #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  camera #androidx.core.app.ComponentActivity  captureCurrentFrame #androidx.core.app.ComponentActivity  currentPage #androidx.core.app.ComponentActivity  delay #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  finishOnboarding #androidx.core.app.ComponentActivity  freezeFrame #androidx.core.app.ComponentActivity  getSharedPreferences #androidx.core.app.ComponentActivity  	getString #androidx.core.app.ComponentActivity  indices #androidx.core.app.ComponentActivity  invoke #androidx.core.app.ComponentActivity  
isInitialized #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  navigateToMain #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  overridePendingTransition #androidx.core.app.ComponentActivity  registerForActivityResult #androidx.core.app.ComponentActivity  saveCurrentFrame #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  setupButtonListeners #androidx.core.app.ComponentActivity  setupButtons #androidx.core.app.ComponentActivity  setupIndicators #androidx.core.app.ComponentActivity  setupViewPager #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  startCamera #androidx.core.app.ComponentActivity  startEntryAnimations #androidx.core.app.ComponentActivity  startSplashAnimation #androidx.core.app.ComponentActivity  toggleFlashlight #androidx.core.app.ComponentActivity  
unfreezeFrame #androidx.core.app.ComponentActivity  until #androidx.core.app.ComponentActivity  
updateButtons #androidx.core.app.ComponentActivity  updateIndicators #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getColor #androidx.core.content.ContextCompat  getDrawable #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityOnboardingBinding &androidx.fragment.app.FragmentActivity  ActivityResultContracts &androidx.fragment.app.FragmentActivity  ActivitySplashBinding &androidx.fragment.app.FragmentActivity  Array &androidx.fragment.app.FragmentActivity  Bitmap &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  Camera &androidx.fragment.app.FragmentActivity  CameraSelector &androidx.fragment.app.FragmentActivity  Canvas &androidx.fragment.app.FragmentActivity  Context &androidx.fragment.app.FragmentActivity  
ContextCompat &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  	ImageView &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  LayoutInflater &androidx.fragment.app.FragmentActivity  LinearLayout &androidx.fragment.app.FragmentActivity  List &androidx.fragment.app.FragmentActivity  MainActivity &androidx.fragment.app.FragmentActivity  Manifest &androidx.fragment.app.FragmentActivity  OnboardingAdapter &androidx.fragment.app.FragmentActivity  OnboardingAnimator &androidx.fragment.app.FragmentActivity  OnboardingPage &androidx.fragment.app.FragmentActivity  OnboardingViewHolder &androidx.fragment.app.FragmentActivity  PackageManager &androidx.fragment.app.FragmentActivity  Preview &androidx.fragment.app.FragmentActivity  ProcessCameraProvider &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  RecyclerView &androidx.fragment.app.FragmentActivity  SPLASH_DELAY &androidx.fragment.app.FragmentActivity  TextView &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  	ViewGroup &androidx.fragment.app.FragmentActivity  
ViewPager2 &androidx.fragment.app.FragmentActivity  addListener &androidx.fragment.app.FragmentActivity  allPermissionsGranted &androidx.fragment.app.FragmentActivity  also &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  camera &androidx.fragment.app.FragmentActivity  captureCurrentFrame &androidx.fragment.app.FragmentActivity  currentPage &androidx.fragment.app.FragmentActivity  delay &androidx.fragment.app.FragmentActivity  finish &androidx.fragment.app.FragmentActivity  finishOnboarding &androidx.fragment.app.FragmentActivity  freezeFrame &androidx.fragment.app.FragmentActivity  getSharedPreferences &androidx.fragment.app.FragmentActivity  	getString &androidx.fragment.app.FragmentActivity  indices &androidx.fragment.app.FragmentActivity  invoke &androidx.fragment.app.FragmentActivity  
isInitialized &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  launch &androidx.fragment.app.FragmentActivity  lifecycleScope &androidx.fragment.app.FragmentActivity  listOf &androidx.fragment.app.FragmentActivity  navigateToMain &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  overridePendingTransition &androidx.fragment.app.FragmentActivity  registerForActivityResult &androidx.fragment.app.FragmentActivity  saveCurrentFrame &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  setupButtonListeners &androidx.fragment.app.FragmentActivity  setupButtons &androidx.fragment.app.FragmentActivity  setupIndicators &androidx.fragment.app.FragmentActivity  setupViewPager &androidx.fragment.app.FragmentActivity  
startActivity &androidx.fragment.app.FragmentActivity  startCamera &androidx.fragment.app.FragmentActivity  startEntryAnimations &androidx.fragment.app.FragmentActivity  startSplashAnimation &androidx.fragment.app.FragmentActivity  toggleFlashlight &androidx.fragment.app.FragmentActivity  
unfreezeFrame &androidx.fragment.app.FragmentActivity  until &androidx.fragment.app.FragmentActivity  
updateButtons &androidx.fragment.app.FragmentActivity  updateIndicators &androidx.fragment.app.FragmentActivity  LifecycleCoroutineScope androidx.lifecycle  LifecycleOwner androidx.lifecycle  Observer androidx.lifecycle  lifecycleScope androidx.lifecycle  	getLAUNCH *androidx.lifecycle.LifecycleCoroutineScope  	getLaunch *androidx.lifecycle.LifecycleCoroutineScope  launch *androidx.lifecycle.LifecycleCoroutineScope  getVALUE androidx.lifecycle.LiveData  getValue androidx.lifecycle.LiveData  observe androidx.lifecycle.LiveData  setValue androidx.lifecycle.LiveData  value androidx.lifecycle.LiveData  <SAM-CONSTRUCTOR> androidx.lifecycle.Observer  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  	ImageView 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  List 1androidx.recyclerview.widget.RecyclerView.Adapter  OnboardingAnimator 1androidx.recyclerview.widget.RecyclerView.Adapter  OnboardingPage 1androidx.recyclerview.widget.RecyclerView.Adapter  OnboardingViewHolder 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  TextView 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  invoke 1androidx.recyclerview.widget.RecyclerView.Adapter  	ImageView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  OnboardingAnimator 4androidx.recyclerview.widget.RecyclerView.ViewHolder  OnboardingPage 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  TextView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  View 4androidx.recyclerview.widget.RecyclerView.ViewHolder  bind 4androidx.recyclerview.widget.RecyclerView.ViewHolder  invoke 4androidx.recyclerview.widget.RecyclerView.ViewHolder  startPageAnimations 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
ViewPager2 androidx.viewpager2.widget  OnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  adapter %androidx.viewpager2.widget.ViewPager2  currentItem %androidx.viewpager2.widget.ViewPager2  
getADAPTER %androidx.viewpager2.widget.ViewPager2  
getAdapter %androidx.viewpager2.widget.ViewPager2  getCURRENTItem %androidx.viewpager2.widget.ViewPager2  getCurrentItem %androidx.viewpager2.widget.ViewPager2  registerOnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  
setAdapter %androidx.viewpager2.widget.ViewPager2  setCurrentItem %androidx.viewpager2.widget.ViewPager2  Int :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  currentPage :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  
updateButtons :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  updateIndicators :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  getADDListener 2com.google.common.util.concurrent.ListenableFuture  getAddListener 2com.google.common.util.concurrent.ListenableFuture  ActivityMainBinding com.magnifyingglass.app  ActivityOnboardingBinding com.magnifyingglass.app  ActivityResultContracts com.magnifyingglass.app  ActivitySplashBinding com.magnifyingglass.app  Array com.magnifyingglass.app  Bitmap com.magnifyingglass.app  Boolean com.magnifyingglass.app  Camera com.magnifyingglass.app  CameraSelector com.magnifyingglass.app  Canvas com.magnifyingglass.app  Context com.magnifyingglass.app  
ContextCompat com.magnifyingglass.app  	Exception com.magnifyingglass.app  	ImageView com.magnifyingglass.app  Int com.magnifyingglass.app  Intent com.magnifyingglass.app  LayoutInflater com.magnifyingglass.app  LinearLayout com.magnifyingglass.app  List com.magnifyingglass.app  MainActivity com.magnifyingglass.app  Manifest com.magnifyingglass.app  OnboardingActivity com.magnifyingglass.app  OnboardingAdapter com.magnifyingglass.app  OnboardingAnimator com.magnifyingglass.app  OnboardingPage com.magnifyingglass.app  OnboardingViewHolder com.magnifyingglass.app  PackageManager com.magnifyingglass.app  Preview com.magnifyingglass.app  ProcessCameraProvider com.magnifyingglass.app  R com.magnifyingglass.app  SPLASH_DELAY com.magnifyingglass.app  SplashActivity com.magnifyingglass.app  Toast com.magnifyingglass.app  View com.magnifyingglass.app  	ViewGroup com.magnifyingglass.app  addListener com.magnifyingglass.app  also com.magnifyingglass.app  android com.magnifyingglass.app  apply com.magnifyingglass.app  currentPage com.magnifyingglass.app  delay com.magnifyingglass.app  indices com.magnifyingglass.app  
isInitialized com.magnifyingglass.app  java com.magnifyingglass.app  launch com.magnifyingglass.app  lifecycleScope com.magnifyingglass.app  listOf com.magnifyingglass.app  navigateToMain com.magnifyingglass.app  until com.magnifyingglass.app  
updateButtons com.magnifyingglass.app  updateIndicators com.magnifyingglass.app  ActivityMainBinding $com.magnifyingglass.app.MainActivity  ActivityResultContracts $com.magnifyingglass.app.MainActivity  Bitmap $com.magnifyingglass.app.MainActivity  Boolean $com.magnifyingglass.app.MainActivity  Bundle $com.magnifyingglass.app.MainActivity  Camera $com.magnifyingglass.app.MainActivity  CameraSelector $com.magnifyingglass.app.MainActivity  Canvas $com.magnifyingglass.app.MainActivity  
ContextCompat $com.magnifyingglass.app.MainActivity  	Exception $com.magnifyingglass.app.MainActivity  Manifest $com.magnifyingglass.app.MainActivity  PackageManager $com.magnifyingglass.app.MainActivity  Preview $com.magnifyingglass.app.MainActivity  ProcessCameraProvider $com.magnifyingglass.app.MainActivity  Toast $com.magnifyingglass.app.MainActivity  View $com.magnifyingglass.app.MainActivity  addListener $com.magnifyingglass.app.MainActivity  allPermissionsGranted $com.magnifyingglass.app.MainActivity  also $com.magnifyingglass.app.MainActivity  android $com.magnifyingglass.app.MainActivity  binding $com.magnifyingglass.app.MainActivity  camera $com.magnifyingglass.app.MainActivity  captureCurrentFrame $com.magnifyingglass.app.MainActivity  finish $com.magnifyingglass.app.MainActivity  freezeFrame $com.magnifyingglass.app.MainActivity  getADDListener $com.magnifyingglass.app.MainActivity  getALSO $com.magnifyingglass.app.MainActivity  getAddListener $com.magnifyingglass.app.MainActivity  getAlso $com.magnifyingglass.app.MainActivity  getLAYOUTInflater $com.magnifyingglass.app.MainActivity  getLayoutInflater $com.magnifyingglass.app.MainActivity  isFlashlightOn $com.magnifyingglass.app.MainActivity  
isFrameFrozen $com.magnifyingglass.app.MainActivity  
isInitialized $com.magnifyingglass.app.MainActivity  layoutInflater $com.magnifyingglass.app.MainActivity  registerForActivityResult $com.magnifyingglass.app.MainActivity  requestPermissionLauncher $com.magnifyingglass.app.MainActivity  saveCurrentFrame $com.magnifyingglass.app.MainActivity  setContentView $com.magnifyingglass.app.MainActivity  setLayoutInflater $com.magnifyingglass.app.MainActivity  setupButtonListeners $com.magnifyingglass.app.MainActivity  startCamera $com.magnifyingglass.app.MainActivity  toggleFlashlight $com.magnifyingglass.app.MainActivity  
unfreezeFrame $com.magnifyingglass.app.MainActivity  ActivityOnboardingBinding *com.magnifyingglass.app.OnboardingActivity  Array *com.magnifyingglass.app.OnboardingActivity  Bundle *com.magnifyingglass.app.OnboardingActivity  Context *com.magnifyingglass.app.OnboardingActivity  
ContextCompat *com.magnifyingglass.app.OnboardingActivity  	ImageView *com.magnifyingglass.app.OnboardingActivity  Int *com.magnifyingglass.app.OnboardingActivity  Intent *com.magnifyingglass.app.OnboardingActivity  LayoutInflater *com.magnifyingglass.app.OnboardingActivity  LinearLayout *com.magnifyingglass.app.OnboardingActivity  List *com.magnifyingglass.app.OnboardingActivity  MainActivity *com.magnifyingglass.app.OnboardingActivity  OnboardingAdapter *com.magnifyingglass.app.OnboardingActivity  OnboardingAnimator *com.magnifyingglass.app.OnboardingActivity  OnboardingPage *com.magnifyingglass.app.OnboardingActivity  OnboardingViewHolder *com.magnifyingglass.app.OnboardingActivity  R *com.magnifyingglass.app.OnboardingActivity  RecyclerView *com.magnifyingglass.app.OnboardingActivity  TextView *com.magnifyingglass.app.OnboardingActivity  View *com.magnifyingglass.app.OnboardingActivity  	ViewGroup *com.magnifyingglass.app.OnboardingActivity  
ViewPager2 *com.magnifyingglass.app.OnboardingActivity  animator *com.magnifyingglass.app.OnboardingActivity  apply *com.magnifyingglass.app.OnboardingActivity  binding *com.magnifyingglass.app.OnboardingActivity  currentPage *com.magnifyingglass.app.OnboardingActivity  finish *com.magnifyingglass.app.OnboardingActivity  finishOnboarding *com.magnifyingglass.app.OnboardingActivity  getAPPLY *com.magnifyingglass.app.OnboardingActivity  getApply *com.magnifyingglass.app.OnboardingActivity  getLAYOUTInflater *com.magnifyingglass.app.OnboardingActivity  	getLISTOf *com.magnifyingglass.app.OnboardingActivity  getLayoutInflater *com.magnifyingglass.app.OnboardingActivity  	getListOf *com.magnifyingglass.app.OnboardingActivity  getSharedPreferences *com.magnifyingglass.app.OnboardingActivity  	getString *com.magnifyingglass.app.OnboardingActivity  getUNTIL *com.magnifyingglass.app.OnboardingActivity  getUntil *com.magnifyingglass.app.OnboardingActivity  indices *com.magnifyingglass.app.OnboardingActivity  invoke *com.magnifyingglass.app.OnboardingActivity  java *com.magnifyingglass.app.OnboardingActivity  layoutInflater *com.magnifyingglass.app.OnboardingActivity  listOf *com.magnifyingglass.app.OnboardingActivity  onboardingAdapter *com.magnifyingglass.app.OnboardingActivity  onboardingPages *com.magnifyingglass.app.OnboardingActivity  setContentView *com.magnifyingglass.app.OnboardingActivity  setLayoutInflater *com.magnifyingglass.app.OnboardingActivity  setupButtons *com.magnifyingglass.app.OnboardingActivity  setupIndicators *com.magnifyingglass.app.OnboardingActivity  setupViewPager *com.magnifyingglass.app.OnboardingActivity  
startActivity *com.magnifyingglass.app.OnboardingActivity  startEntryAnimations *com.magnifyingglass.app.OnboardingActivity  until *com.magnifyingglass.app.OnboardingActivity  
updateButtons *com.magnifyingglass.app.OnboardingActivity  updateIndicators *com.magnifyingglass.app.OnboardingActivity  	ImageView <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  Int <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  LayoutInflater <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  List <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  OnboardingAnimator <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  OnboardingPage <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  OnboardingViewHolder <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  R <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  RecyclerView <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  TextView <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  View <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  	ViewGroup <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  invoke <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  pages <com.magnifyingglass.app.OnboardingActivity.OnboardingAdapter  	ImageView Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  OnboardingAnimator Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  OnboardingPage Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  R Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  TextView Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  View Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  adapterPosition Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  bind Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  descriptionTextView Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  getADAPTERPosition Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  getAdapterPosition Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  
iconImageView Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  invoke Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  setAdapterPosition Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  startPageAnimations Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  
titleTextView Qcom.magnifyingglass.app.OnboardingActivity.OnboardingAdapter.OnboardingViewHolder  Int 9com.magnifyingglass.app.OnboardingActivity.OnboardingPage  descriptionRes 9com.magnifyingglass.app.OnboardingActivity.OnboardingPage  iconRes 9com.magnifyingglass.app.OnboardingActivity.OnboardingPage  titleRes 9com.magnifyingglass.app.OnboardingActivity.OnboardingPage  getCURRENTPage Lcom.magnifyingglass.app.OnboardingActivity.setupViewPager.<no name provided>  getCurrentPage Lcom.magnifyingglass.app.OnboardingActivity.setupViewPager.<no name provided>  getUPDATEButtons Lcom.magnifyingglass.app.OnboardingActivity.setupViewPager.<no name provided>  getUPDATEIndicators Lcom.magnifyingglass.app.OnboardingActivity.setupViewPager.<no name provided>  getUpdateButtons Lcom.magnifyingglass.app.OnboardingActivity.setupViewPager.<no name provided>  getUpdateIndicators Lcom.magnifyingglass.app.OnboardingActivity.setupViewPager.<no name provided>  color com.magnifyingglass.app.R  drawable com.magnifyingglass.app.R  id com.magnifyingglass.app.R  layout com.magnifyingglass.app.R  string com.magnifyingglass.app.R  inactive com.magnifyingglass.app.R.color  primary com.magnifyingglass.app.R.color  	ic_camera "com.magnifyingglass.app.R.drawable  
ic_flashlight "com.magnifyingglass.app.R.drawable  	ic_freeze "com.magnifyingglass.app.R.drawable  ic_mode "com.magnifyingglass.app.R.drawable  ic_save "com.magnifyingglass.app.R.drawable  
ic_zoom_in "com.magnifyingglass.app.R.drawable  cancelButton com.magnifyingglass.app.R.id  descriptionTextView com.magnifyingglass.app.R.id  grantButton com.magnifyingglass.app.R.id  
iconImageView com.magnifyingglass.app.R.id  permissionIcon com.magnifyingglass.app.R.id  permissionMessage com.magnifyingglass.app.R.id  permissionTitle com.magnifyingglass.app.R.id  
titleTextView com.magnifyingglass.app.R.id  dialog_permission  com.magnifyingglass.app.R.layout  onboarding_page  com.magnifyingglass.app.R.layout  high_contrast_mode  com.magnifyingglass.app.R.string  low_light_mode  com.magnifyingglass.app.R.string  normal_mode  com.magnifyingglass.app.R.string  onboarding_desc_1  com.magnifyingglass.app.R.string  onboarding_desc_2  com.magnifyingglass.app.R.string  onboarding_desc_3  com.magnifyingglass.app.R.string  onboarding_next  com.magnifyingglass.app.R.string  onboarding_start  com.magnifyingglass.app.R.string  onboarding_title_1  com.magnifyingglass.app.R.string  onboarding_title_2  com.magnifyingglass.app.R.string  onboarding_title_3  com.magnifyingglass.app.R.string  permission_camera_message  com.magnifyingglass.app.R.string  permission_camera_title  com.magnifyingglass.app.R.string  permission_storage_message  com.magnifyingglass.app.R.string  permission_storage_title  com.magnifyingglass.app.R.string  reading_mode  com.magnifyingglass.app.R.string  ActivitySplashBinding &com.magnifyingglass.app.SplashActivity  Bundle &com.magnifyingglass.app.SplashActivity  Intent &com.magnifyingglass.app.SplashActivity  MainActivity &com.magnifyingglass.app.SplashActivity  SPLASH_DELAY &com.magnifyingglass.app.SplashActivity  android &com.magnifyingglass.app.SplashActivity  binding &com.magnifyingglass.app.SplashActivity  delay &com.magnifyingglass.app.SplashActivity  finish &com.magnifyingglass.app.SplashActivity  
getANDROID &com.magnifyingglass.app.SplashActivity  
getAndroid &com.magnifyingglass.app.SplashActivity  getDELAY &com.magnifyingglass.app.SplashActivity  getDelay &com.magnifyingglass.app.SplashActivity  	getLAUNCH &com.magnifyingglass.app.SplashActivity  getLAYOUTInflater &com.magnifyingglass.app.SplashActivity  getLIFECYCLEScope &com.magnifyingglass.app.SplashActivity  	getLaunch &com.magnifyingglass.app.SplashActivity  getLayoutInflater &com.magnifyingglass.app.SplashActivity  getLifecycleScope &com.magnifyingglass.app.SplashActivity  	getWINDOW &com.magnifyingglass.app.SplashActivity  	getWindow &com.magnifyingglass.app.SplashActivity  java &com.magnifyingglass.app.SplashActivity  launch &com.magnifyingglass.app.SplashActivity  layoutInflater &com.magnifyingglass.app.SplashActivity  lifecycleScope &com.magnifyingglass.app.SplashActivity  navigateToMain &com.magnifyingglass.app.SplashActivity  overridePendingTransition &com.magnifyingglass.app.SplashActivity  setContentView &com.magnifyingglass.app.SplashActivity  setLayoutInflater &com.magnifyingglass.app.SplashActivity  	setWindow &com.magnifyingglass.app.SplashActivity  
startActivity &com.magnifyingglass.app.SplashActivity  startSplashAnimation &com.magnifyingglass.app.SplashActivity  window &com.magnifyingglass.app.SplashActivity  ActivitySplashBinding 0com.magnifyingglass.app.SplashActivity.Companion  Bundle 0com.magnifyingglass.app.SplashActivity.Companion  Intent 0com.magnifyingglass.app.SplashActivity.Companion  MainActivity 0com.magnifyingglass.app.SplashActivity.Companion  SPLASH_DELAY 0com.magnifyingglass.app.SplashActivity.Companion  android 0com.magnifyingglass.app.SplashActivity.Companion  delay 0com.magnifyingglass.app.SplashActivity.Companion  
getANDROID 0com.magnifyingglass.app.SplashActivity.Companion  
getAndroid 0com.magnifyingglass.app.SplashActivity.Companion  getDELAY 0com.magnifyingglass.app.SplashActivity.Companion  getDelay 0com.magnifyingglass.app.SplashActivity.Companion  	getLAUNCH 0com.magnifyingglass.app.SplashActivity.Companion  	getLaunch 0com.magnifyingglass.app.SplashActivity.Companion  java 0com.magnifyingglass.app.SplashActivity.Companion  launch 0com.magnifyingglass.app.SplashActivity.Companion  lifecycleScope 0com.magnifyingglass.app.SplashActivity.Companion  navigateToMain 0com.magnifyingglass.app.SplashActivity.Companion  ActivityMainBinding #com.magnifyingglass.app.databinding  ActivityOnboardingBinding #com.magnifyingglass.app.databinding  ActivitySplashBinding #com.magnifyingglass.app.databinding  flashlightButton 7com.magnifyingglass.app.databinding.ActivityMainBinding  frozenImageView 7com.magnifyingglass.app.databinding.ActivityMainBinding  getROOT 7com.magnifyingglass.app.databinding.ActivityMainBinding  getRoot 7com.magnifyingglass.app.databinding.ActivityMainBinding  inflate 7com.magnifyingglass.app.databinding.ActivityMainBinding  previewView 7com.magnifyingglass.app.databinding.ActivityMainBinding  root 7com.magnifyingglass.app.databinding.ActivityMainBinding  
saveButton 7com.magnifyingglass.app.databinding.ActivityMainBinding  setRoot 7com.magnifyingglass.app.databinding.ActivityMainBinding  buttonLayout =com.magnifyingglass.app.databinding.ActivityOnboardingBinding  getROOT =com.magnifyingglass.app.databinding.ActivityOnboardingBinding  getRoot =com.magnifyingglass.app.databinding.ActivityOnboardingBinding  indicatorLayout =com.magnifyingglass.app.databinding.ActivityOnboardingBinding  inflate =com.magnifyingglass.app.databinding.ActivityOnboardingBinding  
nextButton =com.magnifyingglass.app.databinding.ActivityOnboardingBinding  root =com.magnifyingglass.app.databinding.ActivityOnboardingBinding  setRoot =com.magnifyingglass.app.databinding.ActivityOnboardingBinding  
skipButton =com.magnifyingglass.app.databinding.ActivityOnboardingBinding  	viewPager =com.magnifyingglass.app.databinding.ActivityOnboardingBinding  getROOT 9com.magnifyingglass.app.databinding.ActivitySplashBinding  getRoot 9com.magnifyingglass.app.databinding.ActivitySplashBinding  inflate 9com.magnifyingglass.app.databinding.ActivitySplashBinding  root 9com.magnifyingglass.app.databinding.ActivitySplashBinding  setRoot 9com.magnifyingglass.app.databinding.ActivitySplashBinding  
splashIcon 9com.magnifyingglass.app.databinding.ActivitySplashBinding  splashSubtitle 9com.magnifyingglass.app.databinding.ActivitySplashBinding  splashTitle 9com.magnifyingglass.app.databinding.ActivitySplashBinding  ACCEL_THRESHOLD_HIGH com.magnifyingglass.app.utils  ACCEL_THRESHOLD_LOW com.magnifyingglass.app.utils  ALL_PERMISSIONS_REQUEST_CODE com.magnifyingglass.app.utils   AccelerateDecelerateInterpolator com.magnifyingglass.app.utils  ActivityCompat com.magnifyingglass.app.utils  ActivityManager com.magnifyingglass.app.utils  AlertDialog com.magnifyingglass.app.utils  AnimatorSet com.magnifyingglass.app.utils  Array com.magnifyingglass.app.utils  
AutoFocusMode com.magnifyingglass.app.utils  BOUNCE_DURATION com.magnifyingglass.app.utils  BRIGHTNESS_INDICATOR_TIMEOUT com.magnifyingglass.app.utils  BRIGHTNESS_SENSITIVITY com.magnifyingglass.app.utils  Bitmap com.magnifyingglass.app.utils  BlurMaskFilter com.magnifyingglass.app.utils  Boolean com.magnifyingglass.app.utils  BounceInterpolator com.magnifyingglass.app.utils  BrightnessController com.magnifyingglass.app.utils  Build com.magnifyingglass.app.utils  CAMERA_PERMISSION_REQUEST_CODE com.magnifyingglass.app.utils  Camera2CameraInfo com.magnifyingglass.app.utils  CameraCharacteristics com.magnifyingglass.app.utils  CameraController com.magnifyingglass.app.utils  
CameraMode com.magnifyingglass.app.utils  CameraSettings com.magnifyingglass.app.utils  Canvas com.magnifyingglass.app.utils  Color com.magnifyingglass.app.utils  
ColorDrawable com.magnifyingglass.app.utils  ColorMatrix com.magnifyingglass.app.utils  ColorMatrixColorFilter com.magnifyingglass.app.utils  
ContentValues com.magnifyingglass.app.utils  Context com.magnifyingglass.app.utils  
ContextCompat com.magnifyingglass.app.utils  DATE_FORMAT com.magnifyingglass.app.utils  DEFAULT_BRIGHTNESS com.magnifyingglass.app.utils  DEFAULT_ZOOM_STEP com.magnifyingglass.app.utils  Date com.magnifyingglass.app.utils  Debug com.magnifyingglass.app.utils  DecelerateInterpolator com.magnifyingglass.app.utils  
DeviceInfo com.magnifyingglass.app.utils  Dispatchers com.magnifyingglass.app.utils  Double com.magnifyingglass.app.utils  EDGE_THRESHOLD_DP com.magnifyingglass.app.utils  Environment com.magnifyingglass.app.utils  	Exception com.magnifyingglass.app.utils  ExperimentalCamera2Interop com.magnifyingglass.app.utils  
FADE_DURATION com.magnifyingglass.app.utils  FILTER_ALPHA_HIGH com.magnifyingglass.app.utils  FILTER_ALPHA_LOW com.magnifyingglass.app.utils  FOLDER_NAME com.magnifyingglass.app.utils  File com.magnifyingglass.app.utils  FileOutputStream com.magnifyingglass.app.utils  
FilterType com.magnifyingglass.app.utils  Float com.magnifyingglass.app.utils  
FloatArray com.magnifyingglass.app.utils  FocusMeteringAction com.magnifyingglass.app.utils  GYRO_THRESHOLD_HIGH com.magnifyingglass.app.utils  GYRO_THRESHOLD_LOW com.magnifyingglass.app.utils  GestureDetector com.magnifyingglass.app.utils  GestureHandler com.magnifyingglass.app.utils  GestureType com.magnifyingglass.app.utils  HIGH_ZOOM_THRESHOLD com.magnifyingglass.app.utils  Handler com.magnifyingglass.app.utils  
IMAGE_QUALITY com.magnifyingglass.app.utils  IllegalArgumentException com.magnifyingglass.app.utils  ImageFilterProcessor com.magnifyingglass.app.utils  
ImageSaver com.magnifyingglass.app.utils  Int com.magnifyingglass.app.utils  IntArray com.magnifyingglass.app.utils  Intent com.magnifyingglass.app.utils  KEY_CURRENT_MODE com.magnifyingglass.app.utils  KEY_LAST_BRIGHTNESS com.magnifyingglass.app.utils  KEY_LAST_ZOOM_RATIO com.magnifyingglass.app.utils  LONG_PRESS_TIMEOUT com.magnifyingglass.app.utils  LayoutInflater com.magnifyingglass.app.utils  List com.magnifyingglass.app.utils  Locale com.magnifyingglass.app.utils  Log com.magnifyingglass.app.utils  Long com.magnifyingglass.app.utils  Looper com.magnifyingglass.app.utils  
LowPassFilter com.magnifyingglass.app.utils  MAX_BRIGHTNESS com.magnifyingglass.app.utils  MAX_ZOOM_RATIO com.magnifyingglass.app.utils  MEMORY_CHECK_INTERVAL com.magnifyingglass.app.utils  MEMORY_WARNING_THRESHOLD com.magnifyingglass.app.utils  MIN_BRIGHTNESS com.magnifyingglass.app.utils  MIN_SWIPE_DISTANCE com.magnifyingglass.app.utils  MIN_ZOOM_RATIO com.magnifyingglass.app.utils  Manifest com.magnifyingglass.app.utils  Map com.magnifyingglass.app.utils  
MediaStore com.magnifyingglass.app.utils  ModeConfiguration com.magnifyingglass.app.utils  ModeManager com.magnifyingglass.app.utils  	ModelType com.magnifyingglass.app.utils  MotionEvent com.magnifyingglass.app.utils  ObjectAnimator com.magnifyingglass.app.utils  Observer com.magnifyingglass.app.utils  OnboardingAnimator com.magnifyingglass.app.utils  OptIn com.magnifyingglass.app.utils  OvershootInterpolator com.magnifyingglass.app.utils  
PREFS_NAME com.magnifyingglass.app.utils  PackageManager com.magnifyingglass.app.utils  Paint com.magnifyingglass.app.utils  Pair com.magnifyingglass.app.utils  PerformanceMonitor com.magnifyingglass.app.utils  PerformanceReport com.magnifyingglass.app.utils  PermissionDialogManager com.magnifyingglass.app.utils  PermissionHelper com.magnifyingglass.app.utils  R com.magnifyingglass.app.utils  RealESRGANProcessor com.magnifyingglass.app.utils  Result com.magnifyingglass.app.utils  Runnable com.magnifyingglass.app.utils  SCALE_DURATION com.magnifyingglass.app.utils  SCALE_FACTOR com.magnifyingglass.app.utils  SENSOR_DELAY com.magnifyingglass.app.utils  SLIDE_DURATION com.magnifyingglass.app.utils  STORAGE_PERMISSION_REQUEST_CODE com.magnifyingglass.app.utils  SaveStatistics com.magnifyingglass.app.utils  ScaleGestureDetector com.magnifyingglass.app.utils  Sensor com.magnifyingglass.app.utils  
SensorManager com.magnifyingglass.app.utils  Settings com.magnifyingglass.app.utils  SimpleDateFormat com.magnifyingglass.app.utils  StabilizationController com.magnifyingglass.app.utils  String com.magnifyingglass.app.utils  #SurfaceOrientedMeteringPointFactory com.magnifyingglass.app.utils  System com.magnifyingglass.app.utils  TAG com.magnifyingglass.app.utils  TransitionDirection com.magnifyingglass.app.utils  Triple com.magnifyingglass.app.utils  TrueRealESRGANProcessor com.magnifyingglass.app.utils  Unit com.magnifyingglass.app.utils  Uri com.magnifyingglass.app.utils  
ValueAnimator com.magnifyingglass.app.utils  
WindowManager com.magnifyingglass.app.utils  ZOOM_ANIMATION_DURATION com.magnifyingglass.app.utils  ZoomController com.magnifyingglass.app.utils  abs com.magnifyingglass.app.utils  activity com.magnifyingglass.app.utils  aiStyleEdgeEnhancement com.magnifyingglass.app.utils  all com.magnifyingglass.app.utils  android com.magnifyingglass.app.utils  apply com.magnifyingglass.app.utils  applyNoiseReduction com.magnifyingglass.app.utils  applySharpeningFilter com.magnifyingglass.app.utils  arrayOf com.magnifyingglass.app.utils  average com.magnifyingglass.app.utils  bicubicUpscale com.magnifyingglass.app.utils  camera com.magnifyingglass.app.utils  cancelLongPress com.magnifyingglass.app.utils  checkMemoryUsage com.magnifyingglass.app.utils  
coerceAtLeast com.magnifyingglass.app.utils  coerceAtMost com.magnifyingglass.app.utils  coerceIn com.magnifyingglass.app.utils  !colorEnhancementAndDetailRecovery com.magnifyingglass.app.utils  contains com.magnifyingglass.app.utils  context com.magnifyingglass.app.utils  count com.magnifyingglass.app.utils  deepSharpening com.magnifyingglass.app.utils  endsWith com.magnifyingglass.app.utils  
enhanceColors com.magnifyingglass.app.utils  enhanceEdges com.magnifyingglass.app.utils  equals com.magnifyingglass.app.utils  floatArrayOf com.magnifyingglass.app.utils  forEach com.magnifyingglass.app.utils  format com.magnifyingglass.app.utils  generateFileName com.magnifyingglass.app.utils  gestureListener com.magnifyingglass.app.utils  handler com.magnifyingglass.app.utils  indexOf com.magnifyingglass.app.utils  indices com.magnifyingglass.app.utils  
intArrayOf com.magnifyingglass.app.utils  intelligentDenoising com.magnifyingglass.app.utils  invoke com.magnifyingglass.app.utils  isEdgeSwipeActive com.magnifyingglass.app.utils  
isInitialized com.magnifyingglass.app.utils  isLongPressTriggered com.magnifyingglass.app.utils  isMonitoring com.magnifyingglass.app.utils  
isNotEmpty com.magnifyingglass.app.utils  kotlin com.magnifyingglass.app.utils  kotlinx com.magnifyingglass.app.utils  let com.magnifyingglass.app.utils  listOf com.magnifyingglass.app.utils  mapOf com.magnifyingglass.app.utils  maxByOrNull com.magnifyingglass.app.utils  maxOf com.magnifyingglass.app.utils  minByOrNull com.magnifyingglass.app.utils  minOf com.magnifyingglass.app.utils  
mutableListOf com.magnifyingglass.app.utils  mutableMapOf com.magnifyingglass.app.utils  
plusAssign com.magnifyingglass.app.utils  saveToExternalStorage com.magnifyingglass.app.utils  saveToMediaStore com.magnifyingglass.app.utils  set com.magnifyingglass.app.utils  smartUpscale com.magnifyingglass.app.utils  sqrt com.magnifyingglass.app.utils  to com.magnifyingglass.app.utils  toTypedArray com.magnifyingglass.app.utils  triggerAutoFocus com.magnifyingglass.app.utils  until com.magnifyingglass.app.utils  use com.magnifyingglass.app.utils  withContext com.magnifyingglass.app.utils  Activity 2com.magnifyingglass.app.utils.BrightnessController  BRIGHTNESS_INDICATOR_TIMEOUT 2com.magnifyingglass.app.utils.BrightnessController  Boolean 2com.magnifyingglass.app.utils.BrightnessController  BrightnessListener 2com.magnifyingglass.app.utils.BrightnessController  Camera 2com.magnifyingglass.app.utils.BrightnessController  DEFAULT_BRIGHTNESS 2com.magnifyingglass.app.utils.BrightnessController  	Exception 2com.magnifyingglass.app.utils.BrightnessController  Float 2com.magnifyingglass.app.utils.BrightnessController  Handler 2com.magnifyingglass.app.utils.BrightnessController  Int 2com.magnifyingglass.app.utils.BrightnessController  Looper 2com.magnifyingglass.app.utils.BrightnessController  MAX_BRIGHTNESS 2com.magnifyingglass.app.utils.BrightnessController  MIN_BRIGHTNESS 2com.magnifyingglass.app.utils.BrightnessController  PreviewView 2com.magnifyingglass.app.utils.BrightnessController  Runnable 2com.magnifyingglass.app.utils.BrightnessController  Settings 2com.magnifyingglass.app.utils.BrightnessController  
WindowManager 2com.magnifyingglass.app.utils.BrightnessController  activity 2com.magnifyingglass.app.utils.BrightnessController  adjustCameraExposure 2com.magnifyingglass.app.utils.BrightnessController  adjustWindowBrightness 2com.magnifyingglass.app.utils.BrightnessController  applyBrightness 2com.magnifyingglass.app.utils.BrightnessController  brightnessListener 2com.magnifyingglass.app.utils.BrightnessController  camera 2com.magnifyingglass.app.utils.BrightnessController  coerceIn 2com.magnifyingglass.app.utils.BrightnessController  currentBrightness 2com.magnifyingglass.app.utils.BrightnessController  getBrightnessRecommendation 2com.magnifyingglass.app.utils.BrightnessController  getCOERCEIn 2com.magnifyingglass.app.utils.BrightnessController  getCoerceIn 2com.magnifyingglass.app.utils.BrightnessController  getLET 2com.magnifyingglass.app.utils.BrightnessController  getLet 2com.magnifyingglass.app.utils.BrightnessController  handler 2com.magnifyingglass.app.utils.BrightnessController  hideIndicatorRunnable 2com.magnifyingglass.app.utils.BrightnessController  let 2com.magnifyingglass.app.utils.BrightnessController  previewView 2com.magnifyingglass.app.utils.BrightnessController  resetWindowBrightness 2com.magnifyingglass.app.utils.BrightnessController  
setBrightness 2com.magnifyingglass.app.utils.BrightnessController  showBrightnessIndicator 2com.magnifyingglass.app.utils.BrightnessController  Boolean Ecom.magnifyingglass.app.utils.BrightnessController.BrightnessListener  Int Ecom.magnifyingglass.app.utils.BrightnessController.BrightnessListener  onBrightnessChanged Ecom.magnifyingglass.app.utils.BrightnessController.BrightnessListener  &onBrightnessIndicatorVisibilityChanged Ecom.magnifyingglass.app.utils.BrightnessController.BrightnessListener  onLowLightDetected Ecom.magnifyingglass.app.utils.BrightnessController.BrightnessListener  Activity <com.magnifyingglass.app.utils.BrightnessController.Companion  BRIGHTNESS_INDICATOR_TIMEOUT <com.magnifyingglass.app.utils.BrightnessController.Companion  Boolean <com.magnifyingglass.app.utils.BrightnessController.Companion  Camera <com.magnifyingglass.app.utils.BrightnessController.Companion  DEFAULT_BRIGHTNESS <com.magnifyingglass.app.utils.BrightnessController.Companion  	Exception <com.magnifyingglass.app.utils.BrightnessController.Companion  Float <com.magnifyingglass.app.utils.BrightnessController.Companion  Handler <com.magnifyingglass.app.utils.BrightnessController.Companion  Int <com.magnifyingglass.app.utils.BrightnessController.Companion  Looper <com.magnifyingglass.app.utils.BrightnessController.Companion  MAX_BRIGHTNESS <com.magnifyingglass.app.utils.BrightnessController.Companion  MIN_BRIGHTNESS <com.magnifyingglass.app.utils.BrightnessController.Companion  PreviewView <com.magnifyingglass.app.utils.BrightnessController.Companion  Runnable <com.magnifyingglass.app.utils.BrightnessController.Companion  Settings <com.magnifyingglass.app.utils.BrightnessController.Companion  
WindowManager <com.magnifyingglass.app.utils.BrightnessController.Companion  coerceIn <com.magnifyingglass.app.utils.BrightnessController.Companion  getCOERCEIn <com.magnifyingglass.app.utils.BrightnessController.Companion  getCoerceIn <com.magnifyingglass.app.utils.BrightnessController.Companion  getLET <com.magnifyingglass.app.utils.BrightnessController.Companion  getLet <com.magnifyingglass.app.utils.BrightnessController.Companion  let <com.magnifyingglass.app.utils.BrightnessController.Companion  Boolean .com.magnifyingglass.app.utils.CameraController  Camera .com.magnifyingglass.app.utils.CameraController  Camera2CameraInfo .com.magnifyingglass.app.utils.CameraController  CameraCharacteristics .com.magnifyingglass.app.utils.CameraController  
CameraManager .com.magnifyingglass.app.utils.CameraController  CameraSettings .com.magnifyingglass.app.utils.CameraController  Context .com.magnifyingglass.app.utils.CameraController  
DeviceInfo .com.magnifyingglass.app.utils.CameraController  	Exception .com.magnifyingglass.app.utils.CameraController  Float .com.magnifyingglass.app.utils.CameraController  Int .com.magnifyingglass.app.utils.CameraController  Log .com.magnifyingglass.app.utils.CameraController  Long .com.magnifyingglass.app.utils.CameraController  MAX_ZOOM_RATIO .com.magnifyingglass.app.utils.CameraController  MIN_ZOOM_RATIO .com.magnifyingglass.app.utils.CameraController  Pair .com.magnifyingglass.app.utils.CameraController  String .com.magnifyingglass.app.utils.CameraController  TAG .com.magnifyingglass.app.utils.CameraController  android .com.magnifyingglass.app.utils.CameraController  
cameraManager .com.magnifyingglass.app.utils.CameraController  contains .com.magnifyingglass.app.utils.CameraController  context .com.magnifyingglass.app.utils.CameraController  equals .com.magnifyingglass.app.utils.CameraController  
getANDROID .com.magnifyingglass.app.utils.CameraController  
getAndroid .com.magnifyingglass.app.utils.CameraController  getCONTAINS .com.magnifyingglass.app.utils.CameraController  getContains .com.magnifyingglass.app.utils.CameraController  	getEQUALS .com.magnifyingglass.app.utils.CameraController  	getEquals .com.magnifyingglass.app.utils.CameraController  supportsOpticalStabilization .com.magnifyingglass.app.utils.CameraController  supportsVideoStabilization .com.magnifyingglass.app.utils.CameraController  Boolean =com.magnifyingglass.app.utils.CameraController.CameraSettings  Float =com.magnifyingglass.app.utils.CameraController.CameraSettings  Long =com.magnifyingglass.app.utils.CameraController.CameraSettings  Boolean 8com.magnifyingglass.app.utils.CameraController.Companion  Camera 8com.magnifyingglass.app.utils.CameraController.Companion  Camera2CameraInfo 8com.magnifyingglass.app.utils.CameraController.Companion  CameraCharacteristics 8com.magnifyingglass.app.utils.CameraController.Companion  
CameraManager 8com.magnifyingglass.app.utils.CameraController.Companion  CameraSettings 8com.magnifyingglass.app.utils.CameraController.Companion  Context 8com.magnifyingglass.app.utils.CameraController.Companion  
DeviceInfo 8com.magnifyingglass.app.utils.CameraController.Companion  	Exception 8com.magnifyingglass.app.utils.CameraController.Companion  Float 8com.magnifyingglass.app.utils.CameraController.Companion  Int 8com.magnifyingglass.app.utils.CameraController.Companion  Log 8com.magnifyingglass.app.utils.CameraController.Companion  Long 8com.magnifyingglass.app.utils.CameraController.Companion  MAX_ZOOM_RATIO 8com.magnifyingglass.app.utils.CameraController.Companion  MIN_ZOOM_RATIO 8com.magnifyingglass.app.utils.CameraController.Companion  Pair 8com.magnifyingglass.app.utils.CameraController.Companion  String 8com.magnifyingglass.app.utils.CameraController.Companion  TAG 8com.magnifyingglass.app.utils.CameraController.Companion  android 8com.magnifyingglass.app.utils.CameraController.Companion  contains 8com.magnifyingglass.app.utils.CameraController.Companion  equals 8com.magnifyingglass.app.utils.CameraController.Companion  
getANDROID 8com.magnifyingglass.app.utils.CameraController.Companion  
getAndroid 8com.magnifyingglass.app.utils.CameraController.Companion  getCONTAINS 8com.magnifyingglass.app.utils.CameraController.Companion  getContains 8com.magnifyingglass.app.utils.CameraController.Companion  	getEQUALS 8com.magnifyingglass.app.utils.CameraController.Companion  	getEquals 8com.magnifyingglass.app.utils.CameraController.Companion  invoke 8com.magnifyingglass.app.utils.CameraController.Companion  String 9com.magnifyingglass.app.utils.CameraController.DeviceInfo  manufacturer 9com.magnifyingglass.app.utils.CameraController.DeviceInfo  BRIGHTNESS_SENSITIVITY ,com.magnifyingglass.app.utils.GestureHandler  Boolean ,com.magnifyingglass.app.utils.GestureHandler  Context ,com.magnifyingglass.app.utils.GestureHandler  EDGE_THRESHOLD_DP ,com.magnifyingglass.app.utils.GestureHandler  	Exception ,com.magnifyingglass.app.utils.GestureHandler  Float ,com.magnifyingglass.app.utils.GestureHandler  GestureDetector ,com.magnifyingglass.app.utils.GestureHandler  GestureListener ,com.magnifyingglass.app.utils.GestureHandler  Handler ,com.magnifyingglass.app.utils.GestureHandler  Int ,com.magnifyingglass.app.utils.GestureHandler  LONG_PRESS_TIMEOUT ,com.magnifyingglass.app.utils.GestureHandler  Looper ,com.magnifyingglass.app.utils.GestureHandler  MIN_SWIPE_DISTANCE ,com.magnifyingglass.app.utils.GestureHandler  MotionEvent ,com.magnifyingglass.app.utils.GestureHandler  Runnable ,com.magnifyingglass.app.utils.GestureHandler  ScaleGestureDetector ,com.magnifyingglass.app.utils.GestureHandler  
ScaleListener ,com.magnifyingglass.app.utils.GestureHandler  SimpleGestureListener ,com.magnifyingglass.app.utils.GestureHandler  System ,com.magnifyingglass.app.utils.GestureHandler  View ,com.magnifyingglass.app.utils.GestureHandler  abs ,com.magnifyingglass.app.utils.GestureHandler  android ,com.magnifyingglass.app.utils.GestureHandler  cancelLongPress ,com.magnifyingglass.app.utils.GestureHandler  context ,com.magnifyingglass.app.utils.GestureHandler  edgeSwipeStartY ,com.magnifyingglass.app.utils.GestureHandler  edgeThresholdPx ,com.magnifyingglass.app.utils.GestureHandler  gestureDetector ,com.magnifyingglass.app.utils.GestureHandler  gestureListener ,com.magnifyingglass.app.utils.GestureHandler  getABS ,com.magnifyingglass.app.utils.GestureHandler  
getANDROID ,com.magnifyingglass.app.utils.GestureHandler  getAbs ,com.magnifyingglass.app.utils.GestureHandler  
getAndroid ,com.magnifyingglass.app.utils.GestureHandler  getLET ,com.magnifyingglass.app.utils.GestureHandler  getLet ,com.magnifyingglass.app.utils.GestureHandler  handleEdgeSwipe ,com.magnifyingglass.app.utils.GestureHandler  handleLongPress ,com.magnifyingglass.app.utils.GestureHandler  handler ,com.magnifyingglass.app.utils.GestureHandler  hasMovedDuringTouch ,com.magnifyingglass.app.utils.GestureHandler  isAtScreenEdge ,com.magnifyingglass.app.utils.GestureHandler  isEdgeSwipeActive ,com.magnifyingglass.app.utils.GestureHandler  	isEnabled ,com.magnifyingglass.app.utils.GestureHandler  isLongPressTriggered ,com.magnifyingglass.app.utils.GestureHandler  lastBrightnessY ,com.magnifyingglass.app.utils.GestureHandler  let ,com.magnifyingglass.app.utils.GestureHandler  longPressRunnable ,com.magnifyingglass.app.utils.GestureHandler  scaleGestureDetector ,com.magnifyingglass.app.utils.GestureHandler  touchStartTime ,com.magnifyingglass.app.utils.GestureHandler  touchStartX ,com.magnifyingglass.app.utils.GestureHandler  touchStartY ,com.magnifyingglass.app.utils.GestureHandler  BRIGHTNESS_SENSITIVITY 6com.magnifyingglass.app.utils.GestureHandler.Companion  Boolean 6com.magnifyingglass.app.utils.GestureHandler.Companion  Context 6com.magnifyingglass.app.utils.GestureHandler.Companion  EDGE_THRESHOLD_DP 6com.magnifyingglass.app.utils.GestureHandler.Companion  	Exception 6com.magnifyingglass.app.utils.GestureHandler.Companion  Float 6com.magnifyingglass.app.utils.GestureHandler.Companion  GestureDetector 6com.magnifyingglass.app.utils.GestureHandler.Companion  Handler 6com.magnifyingglass.app.utils.GestureHandler.Companion  Int 6com.magnifyingglass.app.utils.GestureHandler.Companion  LONG_PRESS_TIMEOUT 6com.magnifyingglass.app.utils.GestureHandler.Companion  Looper 6com.magnifyingglass.app.utils.GestureHandler.Companion  MIN_SWIPE_DISTANCE 6com.magnifyingglass.app.utils.GestureHandler.Companion  MotionEvent 6com.magnifyingglass.app.utils.GestureHandler.Companion  Runnable 6com.magnifyingglass.app.utils.GestureHandler.Companion  ScaleGestureDetector 6com.magnifyingglass.app.utils.GestureHandler.Companion  System 6com.magnifyingglass.app.utils.GestureHandler.Companion  View 6com.magnifyingglass.app.utils.GestureHandler.Companion  abs 6com.magnifyingglass.app.utils.GestureHandler.Companion  android 6com.magnifyingglass.app.utils.GestureHandler.Companion  cancelLongPress 6com.magnifyingglass.app.utils.GestureHandler.Companion  gestureListener 6com.magnifyingglass.app.utils.GestureHandler.Companion  getABS 6com.magnifyingglass.app.utils.GestureHandler.Companion  
getANDROID 6com.magnifyingglass.app.utils.GestureHandler.Companion  getAbs 6com.magnifyingglass.app.utils.GestureHandler.Companion  
getAndroid 6com.magnifyingglass.app.utils.GestureHandler.Companion  getLET 6com.magnifyingglass.app.utils.GestureHandler.Companion  getLet 6com.magnifyingglass.app.utils.GestureHandler.Companion  isEdgeSwipeActive 6com.magnifyingglass.app.utils.GestureHandler.Companion  isLongPressTriggered 6com.magnifyingglass.app.utils.GestureHandler.Companion  let 6com.magnifyingglass.app.utils.GestureHandler.Companion  Float <com.magnifyingglass.app.utils.GestureHandler.GestureListener  Int <com.magnifyingglass.app.utils.GestureHandler.GestureListener  onBrightnessAdjust <com.magnifyingglass.app.utils.GestureHandler.GestureListener  onDoubleTap <com.magnifyingglass.app.utils.GestureHandler.GestureListener  onEdgeSwipeEnd <com.magnifyingglass.app.utils.GestureHandler.GestureListener  onEdgeSwipeStart <com.magnifyingglass.app.utils.GestureHandler.GestureListener  onLongPress <com.magnifyingglass.app.utils.GestureHandler.GestureListener  onSingleTap <com.magnifyingglass.app.utils.GestureHandler.GestureListener  
onZoomGesture <com.magnifyingglass.app.utils.GestureHandler.GestureListener  Boolean :com.magnifyingglass.app.utils.GestureHandler.ScaleListener  ScaleGestureDetector :com.magnifyingglass.app.utils.GestureHandler.ScaleListener  cancelLongPress :com.magnifyingglass.app.utils.GestureHandler.ScaleListener  gestureListener :com.magnifyingglass.app.utils.GestureHandler.ScaleListener  getCANCELLongPress :com.magnifyingglass.app.utils.GestureHandler.ScaleListener  getCancelLongPress :com.magnifyingglass.app.utils.GestureHandler.ScaleListener  getGESTUREListener :com.magnifyingglass.app.utils.GestureHandler.ScaleListener  getGestureListener :com.magnifyingglass.app.utils.GestureHandler.ScaleListener  Boolean Bcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener  MotionEvent Bcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener  gestureListener Bcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener  getGESTUREListener Bcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener  getGestureListener Bcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener  getISEdgeSwipeActive Bcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener  getISLongPressTriggered Bcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener  getIsEdgeSwipeActive Bcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener  getIsLongPressTriggered Bcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener  isEdgeSwipeActive Bcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener  isLongPressTriggered Bcom.magnifyingglass.app.utils.GestureHandler.SimpleGestureListener  Bitmap 2com.magnifyingglass.app.utils.ImageFilterProcessor  BlurMaskFilter 2com.magnifyingglass.app.utils.ImageFilterProcessor  Boolean 2com.magnifyingglass.app.utils.ImageFilterProcessor  Canvas 2com.magnifyingglass.app.utils.ImageFilterProcessor  Color 2com.magnifyingglass.app.utils.ImageFilterProcessor  
ColorDrawable 2com.magnifyingglass.app.utils.ImageFilterProcessor  ColorMatrix 2com.magnifyingglass.app.utils.ImageFilterProcessor  ColorMatrixColorFilter 2com.magnifyingglass.app.utils.ImageFilterProcessor  
FilterType 2com.magnifyingglass.app.utils.ImageFilterProcessor  Float 2com.magnifyingglass.app.utils.ImageFilterProcessor  IntArray 2com.magnifyingglass.app.utils.ImageFilterProcessor  Paint 2com.magnifyingglass.app.utils.ImageFilterProcessor  PreviewView 2com.magnifyingglass.app.utils.ImageFilterProcessor  adjustContrast 2com.magnifyingglass.app.utils.ImageFilterProcessor  apply 2com.magnifyingglass.app.utils.ImageFilterProcessor  applySharpen 2com.magnifyingglass.app.utils.ImageFilterProcessor  coerceIn 2com.magnifyingglass.app.utils.ImageFilterProcessor  createGrayscaleMatrix 2com.magnifyingglass.app.utils.ImageFilterProcessor  createHighContrastMatrix 2com.magnifyingglass.app.utils.ImageFilterProcessor  createNegativeMatrix 2com.magnifyingglass.app.utils.ImageFilterProcessor  createReadingModeMatrix 2com.magnifyingglass.app.utils.ImageFilterProcessor  detectImageBrightness 2com.magnifyingglass.app.utils.ImageFilterProcessor  floatArrayOf 2com.magnifyingglass.app.utils.ImageFilterProcessor  getAPPLY 2com.magnifyingglass.app.utils.ImageFilterProcessor  getApply 2com.magnifyingglass.app.utils.ImageFilterProcessor  getCOERCEIn 2com.magnifyingglass.app.utils.ImageFilterProcessor  getCoerceIn 2com.magnifyingglass.app.utils.ImageFilterProcessor  getFLOATArrayOf 2com.magnifyingglass.app.utils.ImageFilterProcessor  getFloatArrayOf 2com.magnifyingglass.app.utils.ImageFilterProcessor  
getPLUSAssign 2com.magnifyingglass.app.utils.ImageFilterProcessor  
getPlusAssign 2com.magnifyingglass.app.utils.ImageFilterProcessor  
plusAssign 2com.magnifyingglass.app.utils.ImageFilterProcessor  Bitmap <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  BlurMaskFilter <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  Boolean <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  Canvas <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  Color <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  
ColorDrawable <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  ColorMatrix <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  ColorMatrixColorFilter <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  
FilterType <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  Float <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  IntArray <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  Paint <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  PreviewView <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  apply <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  coerceIn <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  floatArrayOf <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  getAPPLY <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  getApply <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  getCOERCEIn <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  getCoerceIn <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  getFLOATArrayOf <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  getFloatArrayOf <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  
getPLUSAssign <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  
getPlusAssign <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  
plusAssign <com.magnifyingglass.app.utils.ImageFilterProcessor.Companion  	GRAYSCALE =com.magnifyingglass.app.utils.ImageFilterProcessor.FilterType  
HIGH_CONTRAST =com.magnifyingglass.app.utils.ImageFilterProcessor.FilterType  NEGATIVE =com.magnifyingglass.app.utils.ImageFilterProcessor.FilterType  NONE =com.magnifyingglass.app.utils.ImageFilterProcessor.FilterType  READING_MODE =com.magnifyingglass.app.utils.ImageFilterProcessor.FilterType  equals =com.magnifyingglass.app.utils.ImageFilterProcessor.FilterType  Bitmap (com.magnifyingglass.app.utils.ImageSaver  Boolean (com.magnifyingglass.app.utils.ImageSaver  Build (com.magnifyingglass.app.utils.ImageSaver  
ContentValues (com.magnifyingglass.app.utils.ImageSaver  Context (com.magnifyingglass.app.utils.ImageSaver  DATE_FORMAT (com.magnifyingglass.app.utils.ImageSaver  Date (com.magnifyingglass.app.utils.ImageSaver  Dispatchers (com.magnifyingglass.app.utils.ImageSaver  Double (com.magnifyingglass.app.utils.ImageSaver  Environment (com.magnifyingglass.app.utils.ImageSaver  	Exception (com.magnifyingglass.app.utils.ImageSaver  FOLDER_NAME (com.magnifyingglass.app.utils.ImageSaver  File (com.magnifyingglass.app.utils.ImageSaver  FileOutputStream (com.magnifyingglass.app.utils.ImageSaver  
IMAGE_QUALITY (com.magnifyingglass.app.utils.ImageSaver  Int (com.magnifyingglass.app.utils.ImageSaver  Locale (com.magnifyingglass.app.utils.ImageSaver  Long (com.magnifyingglass.app.utils.ImageSaver  
MediaStore (com.magnifyingglass.app.utils.ImageSaver  Pair (com.magnifyingglass.app.utils.ImageSaver  Result (com.magnifyingglass.app.utils.ImageSaver  SaveListener (com.magnifyingglass.app.utils.ImageSaver  SaveStatistics (com.magnifyingglass.app.utils.ImageSaver  SimpleDateFormat (com.magnifyingglass.app.utils.ImageSaver  String (com.magnifyingglass.app.utils.ImageSaver  Uri (com.magnifyingglass.app.utils.ImageSaver  apply (com.magnifyingglass.app.utils.ImageSaver  context (com.magnifyingglass.app.utils.ImageSaver  endsWith (com.magnifyingglass.app.utils.ImageSaver  forEach (com.magnifyingglass.app.utils.ImageSaver  generateFileName (com.magnifyingglass.app.utils.ImageSaver  getAPPLY (com.magnifyingglass.app.utils.ImageSaver  getApply (com.magnifyingglass.app.utils.ImageSaver  getENDSWith (com.magnifyingglass.app.utils.ImageSaver  getEndsWith (com.magnifyingglass.app.utils.ImageSaver  
getFOREach (com.magnifyingglass.app.utils.ImageSaver  
getForEach (com.magnifyingglass.app.utils.ImageSaver  getLET (com.magnifyingglass.app.utils.ImageSaver  getLet (com.magnifyingglass.app.utils.ImageSaver  getMINOf (com.magnifyingglass.app.utils.ImageSaver  getMinOf (com.magnifyingglass.app.utils.ImageSaver  
getPLUSAssign (com.magnifyingglass.app.utils.ImageSaver  
getPlusAssign (com.magnifyingglass.app.utils.ImageSaver  getSaveDirectory (com.magnifyingglass.app.utils.ImageSaver  getUSE (com.magnifyingglass.app.utils.ImageSaver  getUse (com.magnifyingglass.app.utils.ImageSaver  getWITHContext (com.magnifyingglass.app.utils.ImageSaver  getWithContext (com.magnifyingglass.app.utils.ImageSaver  let (com.magnifyingglass.app.utils.ImageSaver  minOf (com.magnifyingglass.app.utils.ImageSaver  
plusAssign (com.magnifyingglass.app.utils.ImageSaver  saveToExternalStorage (com.magnifyingglass.app.utils.ImageSaver  saveToMediaStore (com.magnifyingglass.app.utils.ImageSaver  use (com.magnifyingglass.app.utils.ImageSaver  withContext (com.magnifyingglass.app.utils.ImageSaver  Bitmap 2com.magnifyingglass.app.utils.ImageSaver.Companion  Boolean 2com.magnifyingglass.app.utils.ImageSaver.Companion  Build 2com.magnifyingglass.app.utils.ImageSaver.Companion  
ContentValues 2com.magnifyingglass.app.utils.ImageSaver.Companion  Context 2com.magnifyingglass.app.utils.ImageSaver.Companion  DATE_FORMAT 2com.magnifyingglass.app.utils.ImageSaver.Companion  Date 2com.magnifyingglass.app.utils.ImageSaver.Companion  Dispatchers 2com.magnifyingglass.app.utils.ImageSaver.Companion  Double 2com.magnifyingglass.app.utils.ImageSaver.Companion  Environment 2com.magnifyingglass.app.utils.ImageSaver.Companion  	Exception 2com.magnifyingglass.app.utils.ImageSaver.Companion  FOLDER_NAME 2com.magnifyingglass.app.utils.ImageSaver.Companion  File 2com.magnifyingglass.app.utils.ImageSaver.Companion  FileOutputStream 2com.magnifyingglass.app.utils.ImageSaver.Companion  
IMAGE_QUALITY 2com.magnifyingglass.app.utils.ImageSaver.Companion  Int 2com.magnifyingglass.app.utils.ImageSaver.Companion  Locale 2com.magnifyingglass.app.utils.ImageSaver.Companion  Long 2com.magnifyingglass.app.utils.ImageSaver.Companion  
MediaStore 2com.magnifyingglass.app.utils.ImageSaver.Companion  Pair 2com.magnifyingglass.app.utils.ImageSaver.Companion  Result 2com.magnifyingglass.app.utils.ImageSaver.Companion  SaveStatistics 2com.magnifyingglass.app.utils.ImageSaver.Companion  SimpleDateFormat 2com.magnifyingglass.app.utils.ImageSaver.Companion  String 2com.magnifyingglass.app.utils.ImageSaver.Companion  Uri 2com.magnifyingglass.app.utils.ImageSaver.Companion  apply 2com.magnifyingglass.app.utils.ImageSaver.Companion  context 2com.magnifyingglass.app.utils.ImageSaver.Companion  endsWith 2com.magnifyingglass.app.utils.ImageSaver.Companion  forEach 2com.magnifyingglass.app.utils.ImageSaver.Companion  generateFileName 2com.magnifyingglass.app.utils.ImageSaver.Companion  getAPPLY 2com.magnifyingglass.app.utils.ImageSaver.Companion  getApply 2com.magnifyingglass.app.utils.ImageSaver.Companion  getENDSWith 2com.magnifyingglass.app.utils.ImageSaver.Companion  getEndsWith 2com.magnifyingglass.app.utils.ImageSaver.Companion  
getFOREach 2com.magnifyingglass.app.utils.ImageSaver.Companion  
getForEach 2com.magnifyingglass.app.utils.ImageSaver.Companion  getLET 2com.magnifyingglass.app.utils.ImageSaver.Companion  getLet 2com.magnifyingglass.app.utils.ImageSaver.Companion  getMINOf 2com.magnifyingglass.app.utils.ImageSaver.Companion  getMinOf 2com.magnifyingglass.app.utils.ImageSaver.Companion  
getPLUSAssign 2com.magnifyingglass.app.utils.ImageSaver.Companion  
getPlusAssign 2com.magnifyingglass.app.utils.ImageSaver.Companion  getUSE 2com.magnifyingglass.app.utils.ImageSaver.Companion  getUse 2com.magnifyingglass.app.utils.ImageSaver.Companion  getWITHContext 2com.magnifyingglass.app.utils.ImageSaver.Companion  getWithContext 2com.magnifyingglass.app.utils.ImageSaver.Companion  let 2com.magnifyingglass.app.utils.ImageSaver.Companion  minOf 2com.magnifyingglass.app.utils.ImageSaver.Companion  
plusAssign 2com.magnifyingglass.app.utils.ImageSaver.Companion  saveToExternalStorage 2com.magnifyingglass.app.utils.ImageSaver.Companion  saveToMediaStore 2com.magnifyingglass.app.utils.ImageSaver.Companion  use 2com.magnifyingglass.app.utils.ImageSaver.Companion  withContext 2com.magnifyingglass.app.utils.ImageSaver.Companion  Int 5com.magnifyingglass.app.utils.ImageSaver.SaveListener  String 5com.magnifyingglass.app.utils.ImageSaver.SaveListener  Uri 5com.magnifyingglass.app.utils.ImageSaver.SaveListener  onSaveError 5com.magnifyingglass.app.utils.ImageSaver.SaveListener  onSaveProgress 5com.magnifyingglass.app.utils.ImageSaver.SaveListener  
onSaveSuccess 5com.magnifyingglass.app.utils.ImageSaver.SaveListener  Double 7com.magnifyingglass.app.utils.ImageSaver.SaveStatistics  Int 7com.magnifyingglass.app.utils.ImageSaver.SaveStatistics  Long 7com.magnifyingglass.app.utils.ImageSaver.SaveStatistics  	totalSize 7com.magnifyingglass.app.utils.ImageSaver.SaveStatistics  
AutoFocusMode )com.magnifyingglass.app.utils.ModeManager  Boolean )com.magnifyingglass.app.utils.ModeManager  
CameraMode )com.magnifyingglass.app.utils.ModeManager  Context )com.magnifyingglass.app.utils.ModeManager  Float )com.magnifyingglass.app.utils.ModeManager  IllegalArgumentException )com.magnifyingglass.app.utils.ModeManager  ImageFilterProcessor )com.magnifyingglass.app.utils.ModeManager  Int )com.magnifyingglass.app.utils.ModeManager  KEY_CURRENT_MODE )com.magnifyingglass.app.utils.ModeManager  KEY_LAST_BRIGHTNESS )com.magnifyingglass.app.utils.ModeManager  KEY_LAST_ZOOM_RATIO )com.magnifyingglass.app.utils.ModeManager  Map )com.magnifyingglass.app.utils.ModeManager  ModeChangeListener )com.magnifyingglass.app.utils.ModeManager  ModeConfiguration )com.magnifyingglass.app.utils.ModeManager  
PREFS_NAME )com.magnifyingglass.app.utils.ModeManager  Pair )com.magnifyingglass.app.utils.ModeManager  R )com.magnifyingglass.app.utils.ModeManager  SharedPreferences )com.magnifyingglass.app.utils.ModeManager  String )com.magnifyingglass.app.utils.ModeManager  currentMode )com.magnifyingglass.app.utils.ModeManager  forEach )com.magnifyingglass.app.utils.ModeManager  
getFOREach )com.magnifyingglass.app.utils.ModeManager  
getForEach )com.magnifyingglass.app.utils.ModeManager  
getINDEXOf )com.magnifyingglass.app.utils.ModeManager  
getIndexOf )com.magnifyingglass.app.utils.ModeManager  getMAPOf )com.magnifyingglass.app.utils.ModeManager  getMAXByOrNull )com.magnifyingglass.app.utils.ModeManager  getMUTABLEMapOf )com.magnifyingglass.app.utils.ModeManager  getMapOf )com.magnifyingglass.app.utils.ModeManager  getMaxByOrNull )com.magnifyingglass.app.utils.ModeManager  getModeConfiguration )com.magnifyingglass.app.utils.ModeManager  getModeUsageStatistics )com.magnifyingglass.app.utils.ModeManager  getMutableMapOf )com.magnifyingglass.app.utils.ModeManager  getSET )com.magnifyingglass.app.utils.ModeManager  getSet )com.magnifyingglass.app.utils.ModeManager  getTO )com.magnifyingglass.app.utils.ModeManager  getTo )com.magnifyingglass.app.utils.ModeManager  indexOf )com.magnifyingglass.app.utils.ModeManager  loadCurrentMode )com.magnifyingglass.app.utils.ModeManager  mapOf )com.magnifyingglass.app.utils.ModeManager  maxByOrNull )com.magnifyingglass.app.utils.ModeManager  modeChangeListener )com.magnifyingglass.app.utils.ModeManager  modeConfigurations )com.magnifyingglass.app.utils.ModeManager  mutableMapOf )com.magnifyingglass.app.utils.ModeManager  recordModeUsage )com.magnifyingglass.app.utils.ModeManager  saveCurrentMode )com.magnifyingglass.app.utils.ModeManager  set )com.magnifyingglass.app.utils.ModeManager  setMode )com.magnifyingglass.app.utils.ModeManager  sharedPreferences )com.magnifyingglass.app.utils.ModeManager  to )com.magnifyingglass.app.utils.ModeManager  
CONTINUOUS 7com.magnifyingglass.app.utils.ModeManager.AutoFocusMode  SINGLE 7com.magnifyingglass.app.utils.ModeManager.AutoFocusMode  
CameraMode 4com.magnifyingglass.app.utils.ModeManager.CameraMode  
HIGH_CONTRAST 4com.magnifyingglass.app.utils.ModeManager.CameraMode  Int 4com.magnifyingglass.app.utils.ModeManager.CameraMode  	LOW_LIGHT 4com.magnifyingglass.app.utils.ModeManager.CameraMode  NORMAL 4com.magnifyingglass.app.utils.ModeManager.CameraMode  R 4com.magnifyingglass.app.utils.ModeManager.CameraMode  READING 4com.magnifyingglass.app.utils.ModeManager.CameraMode  String 4com.magnifyingglass.app.utils.ModeManager.CameraMode  equals 4com.magnifyingglass.app.utils.ModeManager.CameraMode  getTO 4com.magnifyingglass.app.utils.ModeManager.CameraMode  getTo 4com.magnifyingglass.app.utils.ModeManager.CameraMode  name 4com.magnifyingglass.app.utils.ModeManager.CameraMode  to 4com.magnifyingglass.app.utils.ModeManager.CameraMode  valueOf 4com.magnifyingglass.app.utils.ModeManager.CameraMode  values 4com.magnifyingglass.app.utils.ModeManager.CameraMode  R Bcom.magnifyingglass.app.utils.ModeManager.CameraMode.HIGH_CONTRAST  R >com.magnifyingglass.app.utils.ModeManager.CameraMode.LOW_LIGHT  R ;com.magnifyingglass.app.utils.ModeManager.CameraMode.NORMAL  R <com.magnifyingglass.app.utils.ModeManager.CameraMode.READING  
AutoFocusMode 3com.magnifyingglass.app.utils.ModeManager.Companion  Boolean 3com.magnifyingglass.app.utils.ModeManager.Companion  
CameraMode 3com.magnifyingglass.app.utils.ModeManager.Companion  Context 3com.magnifyingglass.app.utils.ModeManager.Companion  Float 3com.magnifyingglass.app.utils.ModeManager.Companion  IllegalArgumentException 3com.magnifyingglass.app.utils.ModeManager.Companion  ImageFilterProcessor 3com.magnifyingglass.app.utils.ModeManager.Companion  Int 3com.magnifyingglass.app.utils.ModeManager.Companion  KEY_CURRENT_MODE 3com.magnifyingglass.app.utils.ModeManager.Companion  KEY_LAST_BRIGHTNESS 3com.magnifyingglass.app.utils.ModeManager.Companion  KEY_LAST_ZOOM_RATIO 3com.magnifyingglass.app.utils.ModeManager.Companion  Map 3com.magnifyingglass.app.utils.ModeManager.Companion  ModeConfiguration 3com.magnifyingglass.app.utils.ModeManager.Companion  
PREFS_NAME 3com.magnifyingglass.app.utils.ModeManager.Companion  Pair 3com.magnifyingglass.app.utils.ModeManager.Companion  R 3com.magnifyingglass.app.utils.ModeManager.Companion  SharedPreferences 3com.magnifyingglass.app.utils.ModeManager.Companion  String 3com.magnifyingglass.app.utils.ModeManager.Companion  forEach 3com.magnifyingglass.app.utils.ModeManager.Companion  
getFOREach 3com.magnifyingglass.app.utils.ModeManager.Companion  
getForEach 3com.magnifyingglass.app.utils.ModeManager.Companion  
getINDEXOf 3com.magnifyingglass.app.utils.ModeManager.Companion  
getIndexOf 3com.magnifyingglass.app.utils.ModeManager.Companion  getMAPOf 3com.magnifyingglass.app.utils.ModeManager.Companion  getMAXByOrNull 3com.magnifyingglass.app.utils.ModeManager.Companion  getMUTABLEMapOf 3com.magnifyingglass.app.utils.ModeManager.Companion  getMapOf 3com.magnifyingglass.app.utils.ModeManager.Companion  getMaxByOrNull 3com.magnifyingglass.app.utils.ModeManager.Companion  getMutableMapOf 3com.magnifyingglass.app.utils.ModeManager.Companion  getSET 3com.magnifyingglass.app.utils.ModeManager.Companion  getSet 3com.magnifyingglass.app.utils.ModeManager.Companion  getTO 3com.magnifyingglass.app.utils.ModeManager.Companion  getTo 3com.magnifyingglass.app.utils.ModeManager.Companion  indexOf 3com.magnifyingglass.app.utils.ModeManager.Companion  mapOf 3com.magnifyingglass.app.utils.ModeManager.Companion  maxByOrNull 3com.magnifyingglass.app.utils.ModeManager.Companion  mutableMapOf 3com.magnifyingglass.app.utils.ModeManager.Companion  set 3com.magnifyingglass.app.utils.ModeManager.Companion  to 3com.magnifyingglass.app.utils.ModeManager.Companion  
CameraMode <com.magnifyingglass.app.utils.ModeManager.ModeChangeListener  ModeConfiguration <com.magnifyingglass.app.utils.ModeManager.ModeChangeListener  
onModeChanged <com.magnifyingglass.app.utils.ModeManager.ModeChangeListener  onModeConfigurationChanged <com.magnifyingglass.app.utils.ModeManager.ModeChangeListener  
AutoFocusMode ;com.magnifyingglass.app.utils.ModeManager.ModeConfiguration  Boolean ;com.magnifyingglass.app.utils.ModeManager.ModeConfiguration  ImageFilterProcessor ;com.magnifyingglass.app.utils.ModeManager.ModeConfiguration  Int ;com.magnifyingglass.app.utils.ModeManager.ModeConfiguration   AccelerateDecelerateInterpolator 0com.magnifyingglass.app.utils.OnboardingAnimator  AnimatorSet 0com.magnifyingglass.app.utils.OnboardingAnimator  BOUNCE_DURATION 0com.magnifyingglass.app.utils.OnboardingAnimator  BounceInterpolator 0com.magnifyingglass.app.utils.OnboardingAnimator  
FADE_DURATION 0com.magnifyingglass.app.utils.OnboardingAnimator  GestureType 0com.magnifyingglass.app.utils.OnboardingAnimator  Long 0com.magnifyingglass.app.utils.OnboardingAnimator  ObjectAnimator 0com.magnifyingglass.app.utils.OnboardingAnimator  OvershootInterpolator 0com.magnifyingglass.app.utils.OnboardingAnimator  SCALE_DURATION 0com.magnifyingglass.app.utils.OnboardingAnimator  SLIDE_DURATION 0com.magnifyingglass.app.utils.OnboardingAnimator  TransitionDirection 0com.magnifyingglass.app.utils.OnboardingAnimator  
ValueAnimator 0com.magnifyingglass.app.utils.OnboardingAnimator  View 0com.magnifyingglass.app.utils.OnboardingAnimator  animateGestureDemo 0com.magnifyingglass.app.utils.OnboardingAnimator  animateIconEntry 0com.magnifyingglass.app.utils.OnboardingAnimator  animateTextEntry 0com.magnifyingglass.app.utils.OnboardingAnimator  apply 0com.magnifyingglass.app.utils.OnboardingAnimator  bounce 0com.magnifyingglass.app.utils.OnboardingAnimator  fadeIn 0com.magnifyingglass.app.utils.OnboardingAnimator  getAPPLY 0com.magnifyingglass.app.utils.OnboardingAnimator  getApply 0com.magnifyingglass.app.utils.OnboardingAnimator  pulse 0com.magnifyingglass.app.utils.OnboardingAnimator  scaleIn 0com.magnifyingglass.app.utils.OnboardingAnimator  slideInFromBottom 0com.magnifyingglass.app.utils.OnboardingAnimator   AccelerateDecelerateInterpolator :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  AnimatorSet :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  BOUNCE_DURATION :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  BounceInterpolator :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  
FADE_DURATION :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  GestureType :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  Long :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  ObjectAnimator :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  OvershootInterpolator :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  SCALE_DURATION :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  SLIDE_DURATION :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  TransitionDirection :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  
ValueAnimator :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  View :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  apply :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  getAPPLY :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  getApply :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  invoke :com.magnifyingglass.app.utils.OnboardingAnimator.Companion  
DOUBLE_TAP <com.magnifyingglass.app.utils.OnboardingAnimator.GestureType  
EDGE_SWIPE <com.magnifyingglass.app.utils.OnboardingAnimator.GestureType  
LONG_PRESS <com.magnifyingglass.app.utils.OnboardingAnimator.GestureType  
PINCH_ZOOM <com.magnifyingglass.app.utils.OnboardingAnimator.GestureType  NEXT Dcom.magnifyingglass.app.utils.OnboardingAnimator.TransitionDirection  PREVIOUS Dcom.magnifyingglass.app.utils.OnboardingAnimator.TransitionDirection  ActivityManager 0com.magnifyingglass.app.utils.PerformanceMonitor  Boolean 0com.magnifyingglass.app.utils.PerformanceMonitor  Context 0com.magnifyingglass.app.utils.PerformanceMonitor  Debug 0com.magnifyingglass.app.utils.PerformanceMonitor  Double 0com.magnifyingglass.app.utils.PerformanceMonitor  	Exception 0com.magnifyingglass.app.utils.PerformanceMonitor  File 0com.magnifyingglass.app.utils.PerformanceMonitor  Handler 0com.magnifyingglass.app.utils.PerformanceMonitor  List 0com.magnifyingglass.app.utils.PerformanceMonitor  Log 0com.magnifyingglass.app.utils.PerformanceMonitor  Long 0com.magnifyingglass.app.utils.PerformanceMonitor  Looper 0com.magnifyingglass.app.utils.PerformanceMonitor  MEMORY_CHECK_INTERVAL 0com.magnifyingglass.app.utils.PerformanceMonitor  MEMORY_WARNING_THRESHOLD 0com.magnifyingglass.app.utils.PerformanceMonitor  Pair 0com.magnifyingglass.app.utils.PerformanceMonitor  PerformanceListener 0com.magnifyingglass.app.utils.PerformanceMonitor  PerformanceReport 0com.magnifyingglass.app.utils.PerformanceMonitor  Runnable 0com.magnifyingglass.app.utils.PerformanceMonitor  String 0com.magnifyingglass.app.utils.PerformanceMonitor  System 0com.magnifyingglass.app.utils.PerformanceMonitor  TAG 0com.magnifyingglass.app.utils.PerformanceMonitor  appStartTime 0com.magnifyingglass.app.utils.PerformanceMonitor  average 0com.magnifyingglass.app.utils.PerformanceMonitor  cameraInitStartTime 0com.magnifyingglass.app.utils.PerformanceMonitor  cameraInitTime 0com.magnifyingglass.app.utils.PerformanceMonitor  checkMemoryUsage 0com.magnifyingglass.app.utils.PerformanceMonitor  context 0com.magnifyingglass.app.utils.PerformanceMonitor  format 0com.magnifyingglass.app.utils.PerformanceMonitor  frameTimings 0com.magnifyingglass.app.utils.PerformanceMonitor  generatePerformanceReport 0com.magnifyingglass.app.utils.PerformanceMonitor  
getAVERAGE 0com.magnifyingglass.app.utils.PerformanceMonitor  
getApkSize 0com.magnifyingglass.app.utils.PerformanceMonitor  getAppMemoryUsage 0com.magnifyingglass.app.utils.PerformanceMonitor  
getAverage 0com.magnifyingglass.app.utils.PerformanceMonitor  getCurrentMemoryUsage 0com.magnifyingglass.app.utils.PerformanceMonitor  	getFORMAT 0com.magnifyingglass.app.utils.PerformanceMonitor  	getFormat 0com.magnifyingglass.app.utils.PerformanceMonitor  
getISNotEmpty 0com.magnifyingglass.app.utils.PerformanceMonitor  
getIsNotEmpty 0com.magnifyingglass.app.utils.PerformanceMonitor  getLET 0com.magnifyingglass.app.utils.PerformanceMonitor  getLet 0com.magnifyingglass.app.utils.PerformanceMonitor  getMUTABLEListOf 0com.magnifyingglass.app.utils.PerformanceMonitor  getMutableListOf 0com.magnifyingglass.app.utils.PerformanceMonitor  handler 0com.magnifyingglass.app.utils.PerformanceMonitor  isBatteryOptimized 0com.magnifyingglass.app.utils.PerformanceMonitor  isMonitoring 0com.magnifyingglass.app.utils.PerformanceMonitor  
isNotEmpty 0com.magnifyingglass.app.utils.PerformanceMonitor  
lastFrameTime 0com.magnifyingglass.app.utils.PerformanceMonitor  let 0com.magnifyingglass.app.utils.PerformanceMonitor  memoryCheckRunnable 0com.magnifyingglass.app.utils.PerformanceMonitor  
mutableListOf 0com.magnifyingglass.app.utils.PerformanceMonitor  performanceListener 0com.magnifyingglass.app.utils.PerformanceMonitor  startMemoryMonitoring 0com.magnifyingglass.app.utils.PerformanceMonitor  stopMemoryMonitoring 0com.magnifyingglass.app.utils.PerformanceMonitor  ActivityManager :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  Boolean :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  Context :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  Debug :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  Double :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  	Exception :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  File :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  Handler :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  List :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  Log :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  Long :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  Looper :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  MEMORY_CHECK_INTERVAL :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  MEMORY_WARNING_THRESHOLD :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  Pair :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  PerformanceReport :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  Runnable :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  String :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  System :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  TAG :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  average :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  checkMemoryUsage :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  format :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  
getAVERAGE :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  
getAverage :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  	getFORMAT :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  	getFormat :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  
getISNotEmpty :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  
getIsNotEmpty :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  getLET :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  getLet :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  getMUTABLEListOf :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  getMutableListOf :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  handler :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  isMonitoring :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  
isNotEmpty :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  let :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  
mutableListOf :com.magnifyingglass.app.utils.PerformanceMonitor.Companion  Long Dcom.magnifyingglass.app.utils.PerformanceMonitor.PerformanceListener  PerformanceReport Dcom.magnifyingglass.app.utils.PerformanceMonitor.PerformanceListener  onLowMemory Dcom.magnifyingglass.app.utils.PerformanceMonitor.PerformanceListener  onMemoryWarning Dcom.magnifyingglass.app.utils.PerformanceMonitor.PerformanceListener  onPerformanceReport Dcom.magnifyingglass.app.utils.PerformanceMonitor.PerformanceListener  Boolean Bcom.magnifyingglass.app.utils.PerformanceMonitor.PerformanceReport  Double Bcom.magnifyingglass.app.utils.PerformanceMonitor.PerformanceReport  Long Bcom.magnifyingglass.app.utils.PerformanceMonitor.PerformanceReport  getCHECKMemoryUsage Ycom.magnifyingglass.app.utils.PerformanceMonitor.startMemoryMonitoring.<no name provided>  getCheckMemoryUsage Ycom.magnifyingglass.app.utils.PerformanceMonitor.startMemoryMonitoring.<no name provided>  
getHANDLER Ycom.magnifyingglass.app.utils.PerformanceMonitor.startMemoryMonitoring.<no name provided>  
getHandler Ycom.magnifyingglass.app.utils.PerformanceMonitor.startMemoryMonitoring.<no name provided>  getISMonitoring Ycom.magnifyingglass.app.utils.PerformanceMonitor.startMemoryMonitoring.<no name provided>  getIsMonitoring Ycom.magnifyingglass.app.utils.PerformanceMonitor.startMemoryMonitoring.<no name provided>  isMonitoring Ycom.magnifyingglass.app.utils.PerformanceMonitor.startMemoryMonitoring.<no name provided>  Activity 5com.magnifyingglass.app.utils.PermissionDialogManager  AlertDialog 5com.magnifyingglass.app.utils.PermissionDialogManager  Button 5com.magnifyingglass.app.utils.PermissionDialogManager  	Exception 5com.magnifyingglass.app.utils.PermissionDialogManager  	ImageView 5com.magnifyingglass.app.utils.PermissionDialogManager  Int 5com.magnifyingglass.app.utils.PermissionDialogManager  Intent 5com.magnifyingglass.app.utils.PermissionDialogManager  LayoutInflater 5com.magnifyingglass.app.utils.PermissionDialogManager  PermissionDialogListener 5com.magnifyingglass.app.utils.PermissionDialogManager  R 5com.magnifyingglass.app.utils.PermissionDialogManager  Settings 5com.magnifyingglass.app.utils.PermissionDialogManager  String 5com.magnifyingglass.app.utils.PermissionDialogManager  TextView 5com.magnifyingglass.app.utils.PermissionDialogManager  Triple 5com.magnifyingglass.app.utils.PermissionDialogManager  Uri 5com.magnifyingglass.app.utils.PermissionDialogManager  activity 5com.magnifyingglass.app.utils.PermissionDialogManager  apply 5com.magnifyingglass.app.utils.PermissionDialogManager  getAPPLY 5com.magnifyingglass.app.utils.PermissionDialogManager  getApply 5com.magnifyingglass.app.utils.PermissionDialogManager  openAppSettings 5com.magnifyingglass.app.utils.PermissionDialogManager  showPermissionDialog 5com.magnifyingglass.app.utils.PermissionDialogManager  onPermissionDenied Ncom.magnifyingglass.app.utils.PermissionDialogManager.PermissionDialogListener  onPermissionGranted Ncom.magnifyingglass.app.utils.PermissionDialogManager.PermissionDialogListener  onSettingsRequested Ncom.magnifyingglass.app.utils.PermissionDialogManager.PermissionDialogListener  ALL_PERMISSIONS_REQUEST_CODE .com.magnifyingglass.app.utils.PermissionHelper  Activity .com.magnifyingglass.app.utils.PermissionHelper  ActivityCompat .com.magnifyingglass.app.utils.PermissionHelper  Array .com.magnifyingglass.app.utils.PermissionHelper  Boolean .com.magnifyingglass.app.utils.PermissionHelper  Build .com.magnifyingglass.app.utils.PermissionHelper  CAMERA_PERMISSION_REQUEST_CODE .com.magnifyingglass.app.utils.PermissionHelper  Context .com.magnifyingglass.app.utils.PermissionHelper  
ContextCompat .com.magnifyingglass.app.utils.PermissionHelper  Int .com.magnifyingglass.app.utils.PermissionHelper  IntArray .com.magnifyingglass.app.utils.PermissionHelper  Manifest .com.magnifyingglass.app.utils.PermissionHelper  PackageManager .com.magnifyingglass.app.utils.PermissionHelper  STORAGE_PERMISSION_REQUEST_CODE .com.magnifyingglass.app.utils.PermissionHelper  String .com.magnifyingglass.app.utils.PermissionHelper  Unit .com.magnifyingglass.app.utils.PermissionHelper  arrayOf .com.magnifyingglass.app.utils.PermissionHelper  context .com.magnifyingglass.app.utils.PermissionHelper  
getARRAYOf .com.magnifyingglass.app.utils.PermissionHelper  
getArrayOf .com.magnifyingglass.app.utils.PermissionHelper  
getISNotEmpty .com.magnifyingglass.app.utils.PermissionHelper  
getIsNotEmpty .com.magnifyingglass.app.utils.PermissionHelper  getMUTABLEListOf .com.magnifyingglass.app.utils.PermissionHelper  getMutableListOf .com.magnifyingglass.app.utils.PermissionHelper  getRequiredPermissions .com.magnifyingglass.app.utils.PermissionHelper  getTOTypedArray .com.magnifyingglass.app.utils.PermissionHelper  getToTypedArray .com.magnifyingglass.app.utils.PermissionHelper  hasAllRequiredPermissions .com.magnifyingglass.app.utils.PermissionHelper  hasCameraPermission .com.magnifyingglass.app.utils.PermissionHelper  hasStoragePermission .com.magnifyingglass.app.utils.PermissionHelper  indices .com.magnifyingglass.app.utils.PermissionHelper  
isNotEmpty .com.magnifyingglass.app.utils.PermissionHelper  
mutableListOf .com.magnifyingglass.app.utils.PermissionHelper  toTypedArray .com.magnifyingglass.app.utils.PermissionHelper  ALL_PERMISSIONS_REQUEST_CODE 8com.magnifyingglass.app.utils.PermissionHelper.Companion  Activity 8com.magnifyingglass.app.utils.PermissionHelper.Companion  ActivityCompat 8com.magnifyingglass.app.utils.PermissionHelper.Companion  Array 8com.magnifyingglass.app.utils.PermissionHelper.Companion  Boolean 8com.magnifyingglass.app.utils.PermissionHelper.Companion  Build 8com.magnifyingglass.app.utils.PermissionHelper.Companion  CAMERA_PERMISSION_REQUEST_CODE 8com.magnifyingglass.app.utils.PermissionHelper.Companion  Context 8com.magnifyingglass.app.utils.PermissionHelper.Companion  
ContextCompat 8com.magnifyingglass.app.utils.PermissionHelper.Companion  Int 8com.magnifyingglass.app.utils.PermissionHelper.Companion  IntArray 8com.magnifyingglass.app.utils.PermissionHelper.Companion  Manifest 8com.magnifyingglass.app.utils.PermissionHelper.Companion  PackageManager 8com.magnifyingglass.app.utils.PermissionHelper.Companion  STORAGE_PERMISSION_REQUEST_CODE 8com.magnifyingglass.app.utils.PermissionHelper.Companion  String 8com.magnifyingglass.app.utils.PermissionHelper.Companion  Unit 8com.magnifyingglass.app.utils.PermissionHelper.Companion  arrayOf 8com.magnifyingglass.app.utils.PermissionHelper.Companion  
getARRAYOf 8com.magnifyingglass.app.utils.PermissionHelper.Companion  
getArrayOf 8com.magnifyingglass.app.utils.PermissionHelper.Companion  
getISNotEmpty 8com.magnifyingglass.app.utils.PermissionHelper.Companion  
getIsNotEmpty 8com.magnifyingglass.app.utils.PermissionHelper.Companion  getMUTABLEListOf 8com.magnifyingglass.app.utils.PermissionHelper.Companion  getMutableListOf 8com.magnifyingglass.app.utils.PermissionHelper.Companion  getTOTypedArray 8com.magnifyingglass.app.utils.PermissionHelper.Companion  getToTypedArray 8com.magnifyingglass.app.utils.PermissionHelper.Companion  indices 8com.magnifyingglass.app.utils.PermissionHelper.Companion  
isNotEmpty 8com.magnifyingglass.app.utils.PermissionHelper.Companion  
mutableListOf 8com.magnifyingglass.app.utils.PermissionHelper.Companion  toTypedArray 8com.magnifyingglass.app.utils.PermissionHelper.Companion  Bitmap 1com.magnifyingglass.app.utils.RealESRGANProcessor  Canvas 1com.magnifyingglass.app.utils.RealESRGANProcessor  ColorMatrix 1com.magnifyingglass.app.utils.RealESRGANProcessor  ColorMatrixColorFilter 1com.magnifyingglass.app.utils.RealESRGANProcessor  Context 1com.magnifyingglass.app.utils.RealESRGANProcessor  Dispatchers 1com.magnifyingglass.app.utils.RealESRGANProcessor  	Exception 1com.magnifyingglass.app.utils.RealESRGANProcessor  Float 1com.magnifyingglass.app.utils.RealESRGANProcessor  IntArray 1com.magnifyingglass.app.utils.RealESRGANProcessor  Log 1com.magnifyingglass.app.utils.RealESRGANProcessor  Paint 1com.magnifyingglass.app.utils.RealESRGANProcessor  SCALE_FACTOR 1com.magnifyingglass.app.utils.RealESRGANProcessor  TAG 1com.magnifyingglass.app.utils.RealESRGANProcessor  Unit 1com.magnifyingglass.app.utils.RealESRGANProcessor  android 1com.magnifyingglass.app.utils.RealESRGANProcessor  applyNoiseReduction 1com.magnifyingglass.app.utils.RealESRGANProcessor  applySharpeningFilter 1com.magnifyingglass.app.utils.RealESRGANProcessor  arrayOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  bicubicUpscale 1com.magnifyingglass.app.utils.RealESRGANProcessor  
enhanceColors 1com.magnifyingglass.app.utils.RealESRGANProcessor  enhanceEdges 1com.magnifyingglass.app.utils.RealESRGANProcessor  floatArrayOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  
getANDROID 1com.magnifyingglass.app.utils.RealESRGANProcessor  
getARRAYOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  
getAndroid 1com.magnifyingglass.app.utils.RealESRGANProcessor  
getArrayOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  getFLOATArrayOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  getFloatArrayOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  
getINTArrayOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  
getIntArrayOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  getMAXOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  getMINOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  getMaxOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  getMinOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  
getPLUSAssign 1com.magnifyingglass.app.utils.RealESRGANProcessor  
getPlusAssign 1com.magnifyingglass.app.utils.RealESRGANProcessor  getSQRT 1com.magnifyingglass.app.utils.RealESRGANProcessor  getSqrt 1com.magnifyingglass.app.utils.RealESRGANProcessor  getUNTIL 1com.magnifyingglass.app.utils.RealESRGANProcessor  getUntil 1com.magnifyingglass.app.utils.RealESRGANProcessor  getWITHContext 1com.magnifyingglass.app.utils.RealESRGANProcessor  getWithContext 1com.magnifyingglass.app.utils.RealESRGANProcessor  
intArrayOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  maxOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  minOf 1com.magnifyingglass.app.utils.RealESRGANProcessor  
plusAssign 1com.magnifyingglass.app.utils.RealESRGANProcessor  sqrt 1com.magnifyingglass.app.utils.RealESRGANProcessor  until 1com.magnifyingglass.app.utils.RealESRGANProcessor  withContext 1com.magnifyingglass.app.utils.RealESRGANProcessor  Bitmap ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  Canvas ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  ColorMatrix ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  ColorMatrixColorFilter ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  Context ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  Dispatchers ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  	Exception ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  Float ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  IntArray ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  Log ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  Paint ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  SCALE_FACTOR ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  TAG ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  Unit ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  android ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  applyNoiseReduction ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  applySharpeningFilter ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  arrayOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  bicubicUpscale ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  
enhanceColors ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  enhanceEdges ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  floatArrayOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  
getANDROID ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  
getARRAYOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  
getAndroid ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  
getArrayOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getFLOATArrayOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getFloatArrayOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  
getINTArrayOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  
getIntArrayOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getMAXOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getMINOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getMaxOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getMinOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  
getPLUSAssign ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  
getPlusAssign ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getSQRT ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getSqrt ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getUNTIL ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getUntil ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getWITHContext ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  getWithContext ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  
intArrayOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  maxOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  minOf ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  
plusAssign ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  sqrt ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  until ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  withContext ;com.magnifyingglass.app.utils.RealESRGANProcessor.Companion  ACCEL_THRESHOLD_HIGH 5com.magnifyingglass.app.utils.StabilizationController  ACCEL_THRESHOLD_LOW 5com.magnifyingglass.app.utils.StabilizationController  Boolean 5com.magnifyingglass.app.utils.StabilizationController  Camera 5com.magnifyingglass.app.utils.StabilizationController  CameraController 5com.magnifyingglass.app.utils.StabilizationController  Context 5com.magnifyingglass.app.utils.StabilizationController  	Exception 5com.magnifyingglass.app.utils.StabilizationController  ExperimentalCamera2Interop 5com.magnifyingglass.app.utils.StabilizationController  FILTER_ALPHA_HIGH 5com.magnifyingglass.app.utils.StabilizationController  FILTER_ALPHA_LOW 5com.magnifyingglass.app.utils.StabilizationController  Float 5com.magnifyingglass.app.utils.StabilizationController  
FloatArray 5com.magnifyingglass.app.utils.StabilizationController  GYRO_THRESHOLD_HIGH 5com.magnifyingglass.app.utils.StabilizationController  GYRO_THRESHOLD_LOW 5com.magnifyingglass.app.utils.StabilizationController  HIGH_ZOOM_THRESHOLD 5com.magnifyingglass.app.utils.StabilizationController  Int 5com.magnifyingglass.app.utils.StabilizationController  Log 5com.magnifyingglass.app.utils.StabilizationController  Long 5com.magnifyingglass.app.utils.StabilizationController  
LowPassFilter 5com.magnifyingglass.app.utils.StabilizationController  OptIn 5com.magnifyingglass.app.utils.StabilizationController  SENSOR_DELAY 5com.magnifyingglass.app.utils.StabilizationController  Sensor 5com.magnifyingglass.app.utils.StabilizationController  SensorEvent 5com.magnifyingglass.app.utils.StabilizationController  
SensorManager 5com.magnifyingglass.app.utils.StabilizationController  StabilizationListener 5com.magnifyingglass.app.utils.StabilizationController  String 5com.magnifyingglass.app.utils.StabilizationController  TAG 5com.magnifyingglass.app.utils.StabilizationController  abs 5com.magnifyingglass.app.utils.StabilizationController  	accelData 5com.magnifyingglass.app.utils.StabilizationController  accelFilter 5com.magnifyingglass.app.utils.StabilizationController  
accelerometer 5com.magnifyingglass.app.utils.StabilizationController  all 5com.magnifyingglass.app.utils.StabilizationController  camera 5com.magnifyingglass.app.utils.StabilizationController  !checkHardwareStabilizationSupport 5com.magnifyingglass.app.utils.StabilizationController  context 5com.magnifyingglass.app.utils.StabilizationController  count 5com.magnifyingglass.app.utils.StabilizationController  currentZoomLevel 5com.magnifyingglass.app.utils.StabilizationController  enableHardwareStabilization 5com.magnifyingglass.app.utils.StabilizationController  getABS 5com.magnifyingglass.app.utils.StabilizationController  getALL 5com.magnifyingglass.app.utils.StabilizationController  getAbs 5com.magnifyingglass.app.utils.StabilizationController  getAll 5com.magnifyingglass.app.utils.StabilizationController  getCOUNT 5com.magnifyingglass.app.utils.StabilizationController  getCount 5com.magnifyingglass.app.utils.StabilizationController  getLET 5com.magnifyingglass.app.utils.StabilizationController  getLet 5com.magnifyingglass.app.utils.StabilizationController  getMUTABLEListOf 5com.magnifyingglass.app.utils.StabilizationController  getMutableListOf 5com.magnifyingglass.app.utils.StabilizationController  getSQRT 5com.magnifyingglass.app.utils.StabilizationController  getSqrt 5com.magnifyingglass.app.utils.StabilizationController  gyroData 5com.magnifyingglass.app.utils.StabilizationController  
gyroFilter 5com.magnifyingglass.app.utils.StabilizationController  	gyroscope 5com.magnifyingglass.app.utils.StabilizationController  indices 5com.magnifyingglass.app.utils.StabilizationController  initializeSensors 5com.magnifyingglass.app.utils.StabilizationController  invoke 5com.magnifyingglass.app.utils.StabilizationController  isCurrentlyStable 5com.magnifyingglass.app.utils.StabilizationController  	isEnabled 5com.magnifyingglass.app.utils.StabilizationController   isHardwareStabilizationSupported 5com.magnifyingglass.app.utils.StabilizationController  
lastAccelTime 5com.magnifyingglass.app.utils.StabilizationController  lastGyroTime 5com.magnifyingglass.app.utils.StabilizationController  let 5com.magnifyingglass.app.utils.StabilizationController  maxHistorySize 5com.magnifyingglass.app.utils.StabilizationController  movementIntensity 5com.magnifyingglass.app.utils.StabilizationController  
mutableListOf 5com.magnifyingglass.app.utils.StabilizationController  processAccelerometerData 5com.magnifyingglass.app.utils.StabilizationController  processGyroscopeData 5com.magnifyingglass.app.utils.StabilizationController  
sensorManager 5com.magnifyingglass.app.utils.StabilizationController  sqrt 5com.magnifyingglass.app.utils.StabilizationController  stabilityHistory 5com.magnifyingglass.app.utils.StabilizationController  stabilizationListener 5com.magnifyingglass.app.utils.StabilizationController  startStabilization 5com.magnifyingglass.app.utils.StabilizationController  stopStabilization 5com.magnifyingglass.app.utils.StabilizationController  updateStabilityState 5com.magnifyingglass.app.utils.StabilizationController  ACCEL_THRESHOLD_HIGH ?com.magnifyingglass.app.utils.StabilizationController.Companion  ACCEL_THRESHOLD_LOW ?com.magnifyingglass.app.utils.StabilizationController.Companion  Boolean ?com.magnifyingglass.app.utils.StabilizationController.Companion  Camera ?com.magnifyingglass.app.utils.StabilizationController.Companion  CameraController ?com.magnifyingglass.app.utils.StabilizationController.Companion  Context ?com.magnifyingglass.app.utils.StabilizationController.Companion  	Exception ?com.magnifyingglass.app.utils.StabilizationController.Companion  ExperimentalCamera2Interop ?com.magnifyingglass.app.utils.StabilizationController.Companion  FILTER_ALPHA_HIGH ?com.magnifyingglass.app.utils.StabilizationController.Companion  FILTER_ALPHA_LOW ?com.magnifyingglass.app.utils.StabilizationController.Companion  Float ?com.magnifyingglass.app.utils.StabilizationController.Companion  
FloatArray ?com.magnifyingglass.app.utils.StabilizationController.Companion  GYRO_THRESHOLD_HIGH ?com.magnifyingglass.app.utils.StabilizationController.Companion  GYRO_THRESHOLD_LOW ?com.magnifyingglass.app.utils.StabilizationController.Companion  HIGH_ZOOM_THRESHOLD ?com.magnifyingglass.app.utils.StabilizationController.Companion  Int ?com.magnifyingglass.app.utils.StabilizationController.Companion  Log ?com.magnifyingglass.app.utils.StabilizationController.Companion  Long ?com.magnifyingglass.app.utils.StabilizationController.Companion  
LowPassFilter ?com.magnifyingglass.app.utils.StabilizationController.Companion  OptIn ?com.magnifyingglass.app.utils.StabilizationController.Companion  SENSOR_DELAY ?com.magnifyingglass.app.utils.StabilizationController.Companion  Sensor ?com.magnifyingglass.app.utils.StabilizationController.Companion  SensorEvent ?com.magnifyingglass.app.utils.StabilizationController.Companion  
SensorManager ?com.magnifyingglass.app.utils.StabilizationController.Companion  String ?com.magnifyingglass.app.utils.StabilizationController.Companion  TAG ?com.magnifyingglass.app.utils.StabilizationController.Companion  abs ?com.magnifyingglass.app.utils.StabilizationController.Companion  all ?com.magnifyingglass.app.utils.StabilizationController.Companion  count ?com.magnifyingglass.app.utils.StabilizationController.Companion  getABS ?com.magnifyingglass.app.utils.StabilizationController.Companion  getALL ?com.magnifyingglass.app.utils.StabilizationController.Companion  getAbs ?com.magnifyingglass.app.utils.StabilizationController.Companion  getAll ?com.magnifyingglass.app.utils.StabilizationController.Companion  getCOUNT ?com.magnifyingglass.app.utils.StabilizationController.Companion  getCount ?com.magnifyingglass.app.utils.StabilizationController.Companion  getLET ?com.magnifyingglass.app.utils.StabilizationController.Companion  getLet ?com.magnifyingglass.app.utils.StabilizationController.Companion  getMUTABLEListOf ?com.magnifyingglass.app.utils.StabilizationController.Companion  getMutableListOf ?com.magnifyingglass.app.utils.StabilizationController.Companion  getSQRT ?com.magnifyingglass.app.utils.StabilizationController.Companion  getSqrt ?com.magnifyingglass.app.utils.StabilizationController.Companion  indices ?com.magnifyingglass.app.utils.StabilizationController.Companion  invoke ?com.magnifyingglass.app.utils.StabilizationController.Companion  let ?com.magnifyingglass.app.utils.StabilizationController.Companion  
mutableListOf ?com.magnifyingglass.app.utils.StabilizationController.Companion  sqrt ?com.magnifyingglass.app.utils.StabilizationController.Companion  Float Ccom.magnifyingglass.app.utils.StabilizationController.LowPassFilter  
FloatArray Ccom.magnifyingglass.app.utils.StabilizationController.LowPassFilter  alpha Ccom.magnifyingglass.app.utils.StabilizationController.LowPassFilter  filter Ccom.magnifyingglass.app.utils.StabilizationController.LowPassFilter  indices Ccom.magnifyingglass.app.utils.StabilizationController.LowPassFilter  output Ccom.magnifyingglass.app.utils.StabilizationController.LowPassFilter  Boolean Kcom.magnifyingglass.app.utils.StabilizationController.StabilizationListener  Float Kcom.magnifyingglass.app.utils.StabilizationController.StabilizationListener  onMovementDetected Kcom.magnifyingglass.app.utils.StabilizationController.StabilizationListener  onStabilizationStateChanged Kcom.magnifyingglass.app.utils.StabilizationController.StabilizationListener  Bitmap 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  Boolean 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  Context 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  Dispatchers 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  	Exception 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  Float 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  
FloatArray 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  Int 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  IntArray 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  Log 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  	ModelType 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  Pair 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  String 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  TAG 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  Unit 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  aiStyleEdgeEnhancement 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  android 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  arrayOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  coerceIn 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  !colorEnhancementAndDetailRecovery 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  deepSharpening 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  enhanceContrast 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  floatArrayOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  
getANDROID 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  
getARRAYOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  
getAndroid 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  
getArrayOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getCOERCEIn 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getCoerceIn 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getFLOATArrayOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getFloatArrayOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  	getKOTLIN 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  
getKOTLINX 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  	getKotlin 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  
getKotlinx 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getMAXOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getMINOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getMaxOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getMinOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  
getPLUSAssign 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  
getPlusAssign 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getUNTIL 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getUntil 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getWITHContext 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  getWithContext 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  indices 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  intelligentDenoising 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  
isInitialized 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  kotlin 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  kotlinx 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  maxOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  minOf 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  
plusAssign 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  smartUpscale 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  until 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  withContext 5com.magnifyingglass.app.utils.TrueRealESRGANProcessor  Bitmap ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  Boolean ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  Context ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  Dispatchers ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  	Exception ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  Float ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  
FloatArray ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  Int ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  IntArray ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  Log ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  	ModelType ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  Pair ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  String ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  TAG ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  Unit ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  aiStyleEdgeEnhancement ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  android ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  arrayOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  coerceIn ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  !colorEnhancementAndDetailRecovery ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  deepSharpening ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  floatArrayOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  
getANDROID ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  
getARRAYOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  
getAndroid ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  
getArrayOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getCOERCEIn ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getCoerceIn ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getFLOATArrayOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getFloatArrayOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  	getKOTLIN ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  
getKOTLINX ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  	getKotlin ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  
getKotlinx ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getMAXOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getMINOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getMaxOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getMinOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  
getPLUSAssign ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  
getPlusAssign ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getUNTIL ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getUntil ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getWITHContext ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  getWithContext ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  indices ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  intelligentDenoising ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  
isInitialized ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  kotlin ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  kotlinx ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  maxOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  minOf ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  
plusAssign ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  smartUpscale ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  until ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  withContext ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.Companion  ANIME ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.ModelType  GENERAL ?com.magnifyingglass.app.utils.TrueRealESRGANProcessor.ModelType  Boolean ,com.magnifyingglass.app.utils.ZoomController  Camera ,com.magnifyingglass.app.utils.ZoomController  DEFAULT_ZOOM_STEP ,com.magnifyingglass.app.utils.ZoomController  DecelerateInterpolator ,com.magnifyingglass.app.utils.ZoomController  	Exception ,com.magnifyingglass.app.utils.ZoomController  Float ,com.magnifyingglass.app.utils.ZoomController  FocusMeteringAction ,com.magnifyingglass.app.utils.ZoomController  Int ,com.magnifyingglass.app.utils.ZoomController  LifecycleOwner ,com.magnifyingglass.app.utils.ZoomController  List ,com.magnifyingglass.app.utils.ZoomController  MAX_ZOOM_RATIO ,com.magnifyingglass.app.utils.ZoomController  MIN_ZOOM_RATIO ,com.magnifyingglass.app.utils.ZoomController  Observer ,com.magnifyingglass.app.utils.ZoomController  Pair ,com.magnifyingglass.app.utils.ZoomController  #SurfaceOrientedMeteringPointFactory ,com.magnifyingglass.app.utils.ZoomController  
ValueAnimator ,com.magnifyingglass.app.utils.ZoomController  ZOOM_ANIMATION_DURATION ,com.magnifyingglass.app.utils.ZoomController  ZoomListener ,com.magnifyingglass.app.utils.ZoomController  	ZoomState ,com.magnifyingglass.app.utils.ZoomController  android ,com.magnifyingglass.app.utils.ZoomController  
animateZoomTo ,com.magnifyingglass.app.utils.ZoomController  apply ,com.magnifyingglass.app.utils.ZoomController  camera ,com.magnifyingglass.app.utils.ZoomController  
coerceAtLeast ,com.magnifyingglass.app.utils.ZoomController  coerceAtMost ,com.magnifyingglass.app.utils.ZoomController  coerceIn ,com.magnifyingglass.app.utils.ZoomController  currentZoomRatio ,com.magnifyingglass.app.utils.ZoomController  
getANDROID ,com.magnifyingglass.app.utils.ZoomController  getAPPLY ,com.magnifyingglass.app.utils.ZoomController  
getAndroid ,com.magnifyingglass.app.utils.ZoomController  getApply ,com.magnifyingglass.app.utils.ZoomController  getCOERCEAtLeast ,com.magnifyingglass.app.utils.ZoomController  getCOERCEAtMost ,com.magnifyingglass.app.utils.ZoomController  getCOERCEIn ,com.magnifyingglass.app.utils.ZoomController  getCoerceAtLeast ,com.magnifyingglass.app.utils.ZoomController  getCoerceAtMost ,com.magnifyingglass.app.utils.ZoomController  getCoerceIn ,com.magnifyingglass.app.utils.ZoomController  	getKOTLIN ,com.magnifyingglass.app.utils.ZoomController  	getKotlin ,com.magnifyingglass.app.utils.ZoomController  getLET ,com.magnifyingglass.app.utils.ZoomController  	getLISTOf ,com.magnifyingglass.app.utils.ZoomController  getLet ,com.magnifyingglass.app.utils.ZoomController  	getListOf ,com.magnifyingglass.app.utils.ZoomController  getMINByOrNull ,com.magnifyingglass.app.utils.ZoomController  getMUTABLEListOf ,com.magnifyingglass.app.utils.ZoomController  getMinByOrNull ,com.magnifyingglass.app.utils.ZoomController  getMutableListOf ,com.magnifyingglass.app.utils.ZoomController  getRecommendedZoomLevels ,com.magnifyingglass.app.utils.ZoomController  kotlin ,com.magnifyingglass.app.utils.ZoomController  let ,com.magnifyingglass.app.utils.ZoomController  lifecycleOwner ,com.magnifyingglass.app.utils.ZoomController  listOf ,com.magnifyingglass.app.utils.ZoomController  maxSupportedZoom ,com.magnifyingglass.app.utils.ZoomController  minByOrNull ,com.magnifyingglass.app.utils.ZoomController  minSupportedZoom ,com.magnifyingglass.app.utils.ZoomController  
mutableListOf ,com.magnifyingglass.app.utils.ZoomController  observeZoomState ,com.magnifyingglass.app.utils.ZoomController  previousZoomRatio ,com.magnifyingglass.app.utils.ZoomController  setZoomRatio ,com.magnifyingglass.app.utils.ZoomController  triggerAutoFocus ,com.magnifyingglass.app.utils.ZoomController  updateZoomLimits ,com.magnifyingglass.app.utils.ZoomController  zoomAnimator ,com.magnifyingglass.app.utils.ZoomController  zoomListener ,com.magnifyingglass.app.utils.ZoomController  Boolean 6com.magnifyingglass.app.utils.ZoomController.Companion  Camera 6com.magnifyingglass.app.utils.ZoomController.Companion  DEFAULT_ZOOM_STEP 6com.magnifyingglass.app.utils.ZoomController.Companion  DecelerateInterpolator 6com.magnifyingglass.app.utils.ZoomController.Companion  	Exception 6com.magnifyingglass.app.utils.ZoomController.Companion  Float 6com.magnifyingglass.app.utils.ZoomController.Companion  FocusMeteringAction 6com.magnifyingglass.app.utils.ZoomController.Companion  Int 6com.magnifyingglass.app.utils.ZoomController.Companion  LifecycleOwner 6com.magnifyingglass.app.utils.ZoomController.Companion  List 6com.magnifyingglass.app.utils.ZoomController.Companion  MAX_ZOOM_RATIO 6com.magnifyingglass.app.utils.ZoomController.Companion  MIN_ZOOM_RATIO 6com.magnifyingglass.app.utils.ZoomController.Companion  Observer 6com.magnifyingglass.app.utils.ZoomController.Companion  Pair 6com.magnifyingglass.app.utils.ZoomController.Companion  #SurfaceOrientedMeteringPointFactory 6com.magnifyingglass.app.utils.ZoomController.Companion  
ValueAnimator 6com.magnifyingglass.app.utils.ZoomController.Companion  ZOOM_ANIMATION_DURATION 6com.magnifyingglass.app.utils.ZoomController.Companion  	ZoomState 6com.magnifyingglass.app.utils.ZoomController.Companion  android 6com.magnifyingglass.app.utils.ZoomController.Companion  apply 6com.magnifyingglass.app.utils.ZoomController.Companion  camera 6com.magnifyingglass.app.utils.ZoomController.Companion  
coerceAtLeast 6com.magnifyingglass.app.utils.ZoomController.Companion  coerceAtMost 6com.magnifyingglass.app.utils.ZoomController.Companion  coerceIn 6com.magnifyingglass.app.utils.ZoomController.Companion  
getANDROID 6com.magnifyingglass.app.utils.ZoomController.Companion  getAPPLY 6com.magnifyingglass.app.utils.ZoomController.Companion  
getAndroid 6com.magnifyingglass.app.utils.ZoomController.Companion  getApply 6com.magnifyingglass.app.utils.ZoomController.Companion  getCOERCEAtLeast 6com.magnifyingglass.app.utils.ZoomController.Companion  getCOERCEAtMost 6com.magnifyingglass.app.utils.ZoomController.Companion  getCOERCEIn 6com.magnifyingglass.app.utils.ZoomController.Companion  getCoerceAtLeast 6com.magnifyingglass.app.utils.ZoomController.Companion  getCoerceAtMost 6com.magnifyingglass.app.utils.ZoomController.Companion  getCoerceIn 6com.magnifyingglass.app.utils.ZoomController.Companion  	getKOTLIN 6com.magnifyingglass.app.utils.ZoomController.Companion  	getKotlin 6com.magnifyingglass.app.utils.ZoomController.Companion  getLET 6com.magnifyingglass.app.utils.ZoomController.Companion  	getLISTOf 6com.magnifyingglass.app.utils.ZoomController.Companion  getLet 6com.magnifyingglass.app.utils.ZoomController.Companion  	getListOf 6com.magnifyingglass.app.utils.ZoomController.Companion  getMINByOrNull 6com.magnifyingglass.app.utils.ZoomController.Companion  getMUTABLEListOf 6com.magnifyingglass.app.utils.ZoomController.Companion  getMinByOrNull 6com.magnifyingglass.app.utils.ZoomController.Companion  getMutableListOf 6com.magnifyingglass.app.utils.ZoomController.Companion  kotlin 6com.magnifyingglass.app.utils.ZoomController.Companion  let 6com.magnifyingglass.app.utils.ZoomController.Companion  listOf 6com.magnifyingglass.app.utils.ZoomController.Companion  minByOrNull 6com.magnifyingglass.app.utils.ZoomController.Companion  
mutableListOf 6com.magnifyingglass.app.utils.ZoomController.Companion  triggerAutoFocus 6com.magnifyingglass.app.utils.ZoomController.Companion  Float 9com.magnifyingglass.app.utils.ZoomController.ZoomListener  
onZoomChanged 9com.magnifyingglass.app.utils.ZoomController.ZoomListener  onZoomLimitsChanged 9com.magnifyingglass.app.utils.ZoomController.ZoomListener  getTRIGGERAutoFocus Ycom.magnifyingglass.app.utils.ZoomController.animateZoomTo.<anonymous>.<no name provided>  getTriggerAutoFocus Ycom.magnifyingglass.app.utils.ZoomController.animateZoomTo.<anonymous>.<no name provided>  BlurMaskFilter com.magnifyingglass.app.views  Canvas com.magnifyingglass.app.views  Color com.magnifyingglass.app.views  Float com.magnifyingglass.app.views  Int com.magnifyingglass.app.views  JvmOverloads com.magnifyingglass.app.views  MagnifierOverlayView com.magnifyingglass.app.views  Math com.magnifyingglass.app.views  Paint com.magnifyingglass.app.views  Path com.magnifyingglass.app.views  
PorterDuff com.magnifyingglass.app.views  PorterDuffXfermode com.magnifyingglass.app.views  RadialGradient com.magnifyingglass.app.views  Shader com.magnifyingglass.app.views  apply com.magnifyingglass.app.views  floatArrayOf com.magnifyingglass.app.views  
intArrayOf com.magnifyingglass.app.views  java com.magnifyingglass.app.views  until com.magnifyingglass.app.views  AttributeSet 2com.magnifyingglass.app.views.MagnifierOverlayView  BlurMaskFilter 2com.magnifyingglass.app.views.MagnifierOverlayView  Canvas 2com.magnifyingglass.app.views.MagnifierOverlayView  Color 2com.magnifyingglass.app.views.MagnifierOverlayView  Context 2com.magnifyingglass.app.views.MagnifierOverlayView  Float 2com.magnifyingglass.app.views.MagnifierOverlayView  Int 2com.magnifyingglass.app.views.MagnifierOverlayView  JvmOverloads 2com.magnifyingglass.app.views.MagnifierOverlayView  Math 2com.magnifyingglass.app.views.MagnifierOverlayView  Paint 2com.magnifyingglass.app.views.MagnifierOverlayView  Path 2com.magnifyingglass.app.views.MagnifierOverlayView  
PorterDuff 2com.magnifyingglass.app.views.MagnifierOverlayView  PorterDuffXfermode 2com.magnifyingglass.app.views.MagnifierOverlayView  RadialGradient 2com.magnifyingglass.app.views.MagnifierOverlayView  Shader 2com.magnifyingglass.app.views.MagnifierOverlayView  apply 2com.magnifyingglass.app.views.MagnifierOverlayView  borderPaint 2com.magnifyingglass.app.views.MagnifierOverlayView  centerX 2com.magnifyingglass.app.views.MagnifierOverlayView  centerY 2com.magnifyingglass.app.views.MagnifierOverlayView  circleRadius 2com.magnifyingglass.app.views.MagnifierOverlayView  
clearPaint 2com.magnifyingglass.app.views.MagnifierOverlayView  drawFrostedGlassBackground 2com.magnifyingglass.app.views.MagnifierOverlayView  drawMagnifierHandle 2com.magnifyingglass.app.views.MagnifierOverlayView  floatArrayOf 2com.magnifyingglass.app.views.MagnifierOverlayView  getAPPLY 2com.magnifyingglass.app.views.MagnifierOverlayView  getApply 2com.magnifyingglass.app.views.MagnifierOverlayView  getFLOATArrayOf 2com.magnifyingglass.app.views.MagnifierOverlayView  getFloatArrayOf 2com.magnifyingglass.app.views.MagnifierOverlayView  	getHEIGHT 2com.magnifyingglass.app.views.MagnifierOverlayView  	getHeight 2com.magnifyingglass.app.views.MagnifierOverlayView  
getINTArrayOf 2com.magnifyingglass.app.views.MagnifierOverlayView  
getIntArrayOf 2com.magnifyingglass.app.views.MagnifierOverlayView  getJAVA 2com.magnifyingglass.app.views.MagnifierOverlayView  getJava 2com.magnifyingglass.app.views.MagnifierOverlayView  getUNTIL 2com.magnifyingglass.app.views.MagnifierOverlayView  getUntil 2com.magnifyingglass.app.views.MagnifierOverlayView  getWIDTH 2com.magnifyingglass.app.views.MagnifierOverlayView  getWidth 2com.magnifyingglass.app.views.MagnifierOverlayView  handlePaint 2com.magnifyingglass.app.views.MagnifierOverlayView  height 2com.magnifyingglass.app.views.MagnifierOverlayView  
intArrayOf 2com.magnifyingglass.app.views.MagnifierOverlayView  
invalidate 2com.magnifyingglass.app.views.MagnifierOverlayView  java 2com.magnifyingglass.app.views.MagnifierOverlayView  	maskPaint 2com.magnifyingglass.app.views.MagnifierOverlayView  	setHeight 2com.magnifyingglass.app.views.MagnifierOverlayView  setWidth 2com.magnifyingglass.app.views.MagnifierOverlayView  until 2com.magnifyingglass.app.views.MagnifierOverlayView  width 2com.magnifyingglass.app.views.MagnifierOverlayView  File java.io  FileOutputStream java.io  OutputStream java.io  absolutePath java.io.File  exists java.io.File  	freeSpace java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  getFREESpace java.io.File  getFreeSpace java.io.File  	getISFile java.io.File  	getIsFile java.io.File  getLET java.io.File  getLet java.io.File  getNAME java.io.File  getName java.io.File  isFile java.io.File  length java.io.File  let java.io.File  	listFiles java.io.File  mkdirs java.io.File  name java.io.File  setAbsolutePath java.io.File  setFile java.io.File  setFreeSpace java.io.File  setName java.io.File  getUSE java.io.FileOutputStream  getUse java.io.FileOutputStream  use java.io.FileOutputStream  getUSE java.io.OutputStream  getUse java.io.OutputStream  use java.io.OutputStream  ACCEL_THRESHOLD_HIGH 	java.lang  ACCEL_THRESHOLD_LOW 	java.lang  ALL_PERMISSIONS_REQUEST_CODE 	java.lang   AccelerateDecelerateInterpolator 	java.lang  ActivityCompat 	java.lang  ActivityMainBinding 	java.lang  ActivityManager 	java.lang  ActivityOnboardingBinding 	java.lang  ActivityResultContracts 	java.lang  ActivitySplashBinding 	java.lang  AlertDialog 	java.lang  AnimatorSet 	java.lang  Array 	java.lang  
AutoFocusMode 	java.lang  BOUNCE_DURATION 	java.lang  BRIGHTNESS_INDICATOR_TIMEOUT 	java.lang  BRIGHTNESS_SENSITIVITY 	java.lang  Bitmap 	java.lang  BlurMaskFilter 	java.lang  BounceInterpolator 	java.lang  Build 	java.lang  CAMERA_PERMISSION_REQUEST_CODE 	java.lang  Camera2CameraInfo 	java.lang  CameraCharacteristics 	java.lang  CameraController 	java.lang  
CameraMode 	java.lang  CameraSelector 	java.lang  CameraSettings 	java.lang  Canvas 	java.lang  Class 	java.lang  Color 	java.lang  
ColorDrawable 	java.lang  ColorMatrix 	java.lang  ColorMatrixColorFilter 	java.lang  
ContentValues 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  DATE_FORMAT 	java.lang  DEFAULT_BRIGHTNESS 	java.lang  DEFAULT_ZOOM_STEP 	java.lang  Date 	java.lang  Debug 	java.lang  DecelerateInterpolator 	java.lang  
DeviceInfo 	java.lang  Dispatchers 	java.lang  EDGE_THRESHOLD_DP 	java.lang  Environment 	java.lang  	Exception 	java.lang  ExperimentalCamera2Interop 	java.lang  
FADE_DURATION 	java.lang  FILTER_ALPHA_HIGH 	java.lang  FILTER_ALPHA_LOW 	java.lang  FOLDER_NAME 	java.lang  File 	java.lang  FileOutputStream 	java.lang  
FilterType 	java.lang  
FloatArray 	java.lang  FocusMeteringAction 	java.lang  GYRO_THRESHOLD_HIGH 	java.lang  GYRO_THRESHOLD_LOW 	java.lang  GestureDetector 	java.lang  GestureType 	java.lang  HIGH_ZOOM_THRESHOLD 	java.lang  Handler 	java.lang  
IMAGE_QUALITY 	java.lang  ImageFilterProcessor 	java.lang  	ImageView 	java.lang  IntArray 	java.lang  Intent 	java.lang  KEY_CURRENT_MODE 	java.lang  KEY_LAST_BRIGHTNESS 	java.lang  KEY_LAST_ZOOM_RATIO 	java.lang  LONG_PRESS_TIMEOUT 	java.lang  LayoutInflater 	java.lang  LinearLayout 	java.lang  Locale 	java.lang  Log 	java.lang  Looper 	java.lang  
LowPassFilter 	java.lang  MAX_BRIGHTNESS 	java.lang  MAX_ZOOM_RATIO 	java.lang  MEMORY_CHECK_INTERVAL 	java.lang  MEMORY_WARNING_THRESHOLD 	java.lang  MIN_BRIGHTNESS 	java.lang  MIN_SWIPE_DISTANCE 	java.lang  MIN_ZOOM_RATIO 	java.lang  MainActivity 	java.lang  Manifest 	java.lang  Math 	java.lang  
MediaStore 	java.lang  ModeConfiguration 	java.lang  	ModelType 	java.lang  MotionEvent 	java.lang  ObjectAnimator 	java.lang  Observer 	java.lang  OnboardingAdapter 	java.lang  OnboardingAnimator 	java.lang  OnboardingPage 	java.lang  OnboardingViewHolder 	java.lang  OvershootInterpolator 	java.lang  
PREFS_NAME 	java.lang  PackageManager 	java.lang  Paint 	java.lang  Pair 	java.lang  Path 	java.lang  PerformanceReport 	java.lang  
PorterDuff 	java.lang  PorterDuffXfermode 	java.lang  Preview 	java.lang  ProcessCameraProvider 	java.lang  R 	java.lang  RadialGradient 	java.lang  Result 	java.lang  Runnable 	java.lang  SCALE_DURATION 	java.lang  SCALE_FACTOR 	java.lang  SENSOR_DELAY 	java.lang  SLIDE_DURATION 	java.lang  SPLASH_DELAY 	java.lang  STORAGE_PERMISSION_REQUEST_CODE 	java.lang  SaveStatistics 	java.lang  ScaleGestureDetector 	java.lang  Sensor 	java.lang  
SensorManager 	java.lang  Settings 	java.lang  Shader 	java.lang  SimpleDateFormat 	java.lang  #SurfaceOrientedMeteringPointFactory 	java.lang  System 	java.lang  TAG 	java.lang  Toast 	java.lang  TransitionDirection 	java.lang  Triple 	java.lang  Uri 	java.lang  
ValueAnimator 	java.lang  View 	java.lang  	ViewGroup 	java.lang  Void 	java.lang  
WindowManager 	java.lang  ZOOM_ANIMATION_DURATION 	java.lang  abs 	java.lang  activity 	java.lang  addListener 	java.lang  aiStyleEdgeEnhancement 	java.lang  all 	java.lang  also 	java.lang  android 	java.lang  apply 	java.lang  applyNoiseReduction 	java.lang  applySharpeningFilter 	java.lang  arrayOf 	java.lang  average 	java.lang  bicubicUpscale 	java.lang  camera 	java.lang  cancelLongPress 	java.lang  checkMemoryUsage 	java.lang  
coerceAtLeast 	java.lang  coerceAtMost 	java.lang  coerceIn 	java.lang  !colorEnhancementAndDetailRecovery 	java.lang  contains 	java.lang  context 	java.lang  count 	java.lang  currentPage 	java.lang  deepSharpening 	java.lang  delay 	java.lang  endsWith 	java.lang  
enhanceColors 	java.lang  enhanceEdges 	java.lang  equals 	java.lang  floatArrayOf 	java.lang  forEach 	java.lang  format 	java.lang  generateFileName 	java.lang  gestureListener 	java.lang  handler 	java.lang  indexOf 	java.lang  indices 	java.lang  
intArrayOf 	java.lang  intelligentDenoising 	java.lang  isEdgeSwipeActive 	java.lang  
isInitialized 	java.lang  isLongPressTriggered 	java.lang  isMonitoring 	java.lang  
isNotEmpty 	java.lang  java 	java.lang  kotlin 	java.lang  kotlinx 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  mapOf 	java.lang  maxByOrNull 	java.lang  maxOf 	java.lang  minByOrNull 	java.lang  minOf 	java.lang  
mutableListOf 	java.lang  mutableMapOf 	java.lang  navigateToMain 	java.lang  
plusAssign 	java.lang  saveToExternalStorage 	java.lang  saveToMediaStore 	java.lang  set 	java.lang  smartUpscale 	java.lang  sqrt 	java.lang  to 	java.lang  toTypedArray 	java.lang  triggerAutoFocus 	java.lang  until 	java.lang  
updateButtons 	java.lang  updateIndicators 	java.lang  use 	java.lang  withContext 	java.lang  message java.lang.Exception  PI java.lang.Math  cos java.lang.Math  sin java.lang.Math  	toRadians java.lang.Math  <SAM-CONSTRUCTOR> java.lang.Runnable  getLET java.lang.Runnable  getLet java.lang.Runnable  let java.lang.Runnable  currentTimeMillis java.lang.System  gc java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Bitmap 	java.util  Build 	java.util  
ContentValues 	java.util  DATE_FORMAT 	java.util  Date 	java.util  Dispatchers 	java.util  Environment 	java.util  	Exception 	java.util  FOLDER_NAME 	java.util  File 	java.util  FileOutputStream 	java.util  
IMAGE_QUALITY 	java.util  Locale 	java.util  
MediaStore 	java.util  Pair 	java.util  Random 	java.util  Result 	java.util  SaveStatistics 	java.util  SimpleDateFormat 	java.util  apply 	java.util  context 	java.util  endsWith 	java.util  forEach 	java.util  generateFileName 	java.util  let 	java.util  minOf 	java.util  
plusAssign 	java.util  saveToExternalStorage 	java.util  saveToMediaStore 	java.util  use 	java.util  withContext 	java.util  
getDefault java.util.Locale  	nextFloat java.util.Random  Executor java.util.concurrent  ACCEL_THRESHOLD_HIGH kotlin  ACCEL_THRESHOLD_LOW kotlin  ALL_PERMISSIONS_REQUEST_CODE kotlin   AccelerateDecelerateInterpolator kotlin  ActivityCompat kotlin  ActivityMainBinding kotlin  ActivityManager kotlin  ActivityOnboardingBinding kotlin  ActivityResultContracts kotlin  ActivitySplashBinding kotlin  AlertDialog kotlin  AnimatorSet kotlin  Any kotlin  Array kotlin  
AutoFocusMode kotlin  BOUNCE_DURATION kotlin  BRIGHTNESS_INDICATOR_TIMEOUT kotlin  BRIGHTNESS_SENSITIVITY kotlin  Bitmap kotlin  BlurMaskFilter kotlin  Boolean kotlin  BounceInterpolator kotlin  Build kotlin  CAMERA_PERMISSION_REQUEST_CODE kotlin  Camera2CameraInfo kotlin  CameraCharacteristics kotlin  CameraController kotlin  
CameraMode kotlin  CameraSelector kotlin  CameraSettings kotlin  Canvas kotlin  CharSequence kotlin  Color kotlin  
ColorDrawable kotlin  ColorMatrix kotlin  ColorMatrixColorFilter kotlin  
ContentValues kotlin  Context kotlin  
ContextCompat kotlin  DATE_FORMAT kotlin  DEFAULT_BRIGHTNESS kotlin  DEFAULT_ZOOM_STEP kotlin  Date kotlin  Debug kotlin  DecelerateInterpolator kotlin  
DeviceInfo kotlin  Dispatchers kotlin  Double kotlin  EDGE_THRESHOLD_DP kotlin  Environment kotlin  	Exception kotlin  ExperimentalCamera2Interop kotlin  
FADE_DURATION kotlin  FILTER_ALPHA_HIGH kotlin  FILTER_ALPHA_LOW kotlin  FOLDER_NAME kotlin  File kotlin  FileOutputStream kotlin  
FilterType kotlin  Float kotlin  
FloatArray kotlin  FocusMeteringAction kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  GYRO_THRESHOLD_HIGH kotlin  GYRO_THRESHOLD_LOW kotlin  GestureDetector kotlin  GestureType kotlin  HIGH_ZOOM_THRESHOLD kotlin  Handler kotlin  
IMAGE_QUALITY kotlin  IllegalArgumentException kotlin  ImageFilterProcessor kotlin  	ImageView kotlin  Int kotlin  IntArray kotlin  Intent kotlin  JvmOverloads kotlin  KEY_CURRENT_MODE kotlin  KEY_LAST_BRIGHTNESS kotlin  KEY_LAST_ZOOM_RATIO kotlin  LONG_PRESS_TIMEOUT kotlin  LayoutInflater kotlin  LinearLayout kotlin  Locale kotlin  Log kotlin  Long kotlin  Looper kotlin  
LowPassFilter kotlin  MAX_BRIGHTNESS kotlin  MAX_ZOOM_RATIO kotlin  MEMORY_CHECK_INTERVAL kotlin  MEMORY_WARNING_THRESHOLD kotlin  MIN_BRIGHTNESS kotlin  MIN_SWIPE_DISTANCE kotlin  MIN_ZOOM_RATIO kotlin  MainActivity kotlin  Manifest kotlin  Math kotlin  
MediaStore kotlin  ModeConfiguration kotlin  	ModelType kotlin  MotionEvent kotlin  Nothing kotlin  ObjectAnimator kotlin  Observer kotlin  OnboardingAdapter kotlin  OnboardingAnimator kotlin  OnboardingPage kotlin  OnboardingViewHolder kotlin  OptIn kotlin  OvershootInterpolator kotlin  
PREFS_NAME kotlin  PackageManager kotlin  Paint kotlin  Pair kotlin  Path kotlin  PerformanceReport kotlin  
PorterDuff kotlin  PorterDuffXfermode kotlin  Preview kotlin  ProcessCameraProvider kotlin  R kotlin  RadialGradient kotlin  Result kotlin  Runnable kotlin  SCALE_DURATION kotlin  SCALE_FACTOR kotlin  SENSOR_DELAY kotlin  SLIDE_DURATION kotlin  SPLASH_DELAY kotlin  STORAGE_PERMISSION_REQUEST_CODE kotlin  SaveStatistics kotlin  ScaleGestureDetector kotlin  Sensor kotlin  
SensorManager kotlin  Settings kotlin  Shader kotlin  SimpleDateFormat kotlin  String kotlin  #SurfaceOrientedMeteringPointFactory kotlin  System kotlin  TAG kotlin  Toast kotlin  TransitionDirection kotlin  Triple kotlin  Unit kotlin  Uri kotlin  
ValueAnimator kotlin  View kotlin  	ViewGroup kotlin  
WindowManager kotlin  ZOOM_ANIMATION_DURATION kotlin  abs kotlin  activity kotlin  addListener kotlin  aiStyleEdgeEnhancement kotlin  all kotlin  also kotlin  android kotlin  apply kotlin  applyNoiseReduction kotlin  applySharpeningFilter kotlin  arrayOf kotlin  average kotlin  bicubicUpscale kotlin  camera kotlin  cancelLongPress kotlin  checkMemoryUsage kotlin  
coerceAtLeast kotlin  coerceAtMost kotlin  coerceIn kotlin  !colorEnhancementAndDetailRecovery kotlin  contains kotlin  context kotlin  count kotlin  currentPage kotlin  deepSharpening kotlin  delay kotlin  endsWith kotlin  
enhanceColors kotlin  enhanceEdges kotlin  equals kotlin  floatArrayOf kotlin  forEach kotlin  format kotlin  generateFileName kotlin  gestureListener kotlin  handler kotlin  indexOf kotlin  indices kotlin  
intArrayOf kotlin  intelligentDenoising kotlin  isEdgeSwipeActive kotlin  
isInitialized kotlin  isLongPressTriggered kotlin  isMonitoring kotlin  
isNotEmpty kotlin  java kotlin  kotlin kotlin  kotlinx kotlin  launch kotlin  let kotlin  listOf kotlin  mapOf kotlin  maxByOrNull kotlin  maxOf kotlin  minByOrNull kotlin  minOf kotlin  
mutableListOf kotlin  mutableMapOf kotlin  navigateToMain kotlin  
plusAssign kotlin  saveToExternalStorage kotlin  saveToMediaStore kotlin  set kotlin  smartUpscale kotlin  sqrt kotlin  to kotlin  toTypedArray kotlin  triggerAutoFocus kotlin  until kotlin  
updateButtons kotlin  updateIndicators kotlin  use kotlin  withContext kotlin  
getFOREach kotlin.Array  
getForEach kotlin.Array  
getINDEXOf kotlin.Array  
getINDICES kotlin.Array  
getISNotEmpty kotlin.Array  
getIndexOf kotlin.Array  
getIndices kotlin.Array  
getIsNotEmpty kotlin.Array  
isNotEmpty kotlin.Array  getCOERCEAtLeast kotlin.Float  getCOERCEAtMost kotlin.Float  getCOERCEIn kotlin.Float  getCoerceAtLeast kotlin.Float  getCoerceAtMost kotlin.Float  getCoerceIn kotlin.Float  getLET kotlin.Float  getLet kotlin.Float  
getPLUSAssign kotlin.Float  
getPlusAssign kotlin.Float  getALL kotlin.FloatArray  getAll kotlin.FloatArray  
getINDICES kotlin.FloatArray  
getIndices kotlin.FloatArray  getCOERCEIn 
kotlin.Int  getCoerceIn 
kotlin.Int  getLET 
kotlin.Int  getLet 
kotlin.Int  
getPLUSAssign 
kotlin.Int  
getPlusAssign 
kotlin.Int  getUNTIL 
kotlin.Int  getUntil 
kotlin.Int  getCONTAINS kotlin.IntArray  getContains kotlin.IntArray  
getINDICES kotlin.IntArray  
getISNotEmpty kotlin.IntArray  
getIndices kotlin.IntArray  
getIsNotEmpty kotlin.IntArray  
isNotEmpty kotlin.IntArray  
getPLUSAssign kotlin.Long  
getPlusAssign kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  failure 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  getENDSWith 
kotlin.String  	getEQUALS 
kotlin.String  getEndsWith 
kotlin.String  	getEquals 
kotlin.String  	getFORMAT 
kotlin.String  	getFormat 
kotlin.String  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  ACCEL_THRESHOLD_HIGH kotlin.annotation  ACCEL_THRESHOLD_LOW kotlin.annotation  ALL_PERMISSIONS_REQUEST_CODE kotlin.annotation   AccelerateDecelerateInterpolator kotlin.annotation  ActivityCompat kotlin.annotation  ActivityMainBinding kotlin.annotation  ActivityManager kotlin.annotation  ActivityOnboardingBinding kotlin.annotation  ActivityResultContracts kotlin.annotation  ActivitySplashBinding kotlin.annotation  AlertDialog kotlin.annotation  AnimatorSet kotlin.annotation  Array kotlin.annotation  
AutoFocusMode kotlin.annotation  BOUNCE_DURATION kotlin.annotation  BRIGHTNESS_INDICATOR_TIMEOUT kotlin.annotation  BRIGHTNESS_SENSITIVITY kotlin.annotation  Bitmap kotlin.annotation  BlurMaskFilter kotlin.annotation  BounceInterpolator kotlin.annotation  Build kotlin.annotation  CAMERA_PERMISSION_REQUEST_CODE kotlin.annotation  Camera2CameraInfo kotlin.annotation  CameraCharacteristics kotlin.annotation  CameraController kotlin.annotation  
CameraMode kotlin.annotation  CameraSelector kotlin.annotation  CameraSettings kotlin.annotation  Canvas kotlin.annotation  Color kotlin.annotation  
ColorDrawable kotlin.annotation  ColorMatrix kotlin.annotation  ColorMatrixColorFilter kotlin.annotation  
ContentValues kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  DATE_FORMAT kotlin.annotation  DEFAULT_BRIGHTNESS kotlin.annotation  DEFAULT_ZOOM_STEP kotlin.annotation  Date kotlin.annotation  Debug kotlin.annotation  DecelerateInterpolator kotlin.annotation  
DeviceInfo kotlin.annotation  Dispatchers kotlin.annotation  EDGE_THRESHOLD_DP kotlin.annotation  Environment kotlin.annotation  	Exception kotlin.annotation  ExperimentalCamera2Interop kotlin.annotation  
FADE_DURATION kotlin.annotation  FILTER_ALPHA_HIGH kotlin.annotation  FILTER_ALPHA_LOW kotlin.annotation  FOLDER_NAME kotlin.annotation  File kotlin.annotation  FileOutputStream kotlin.annotation  
FilterType kotlin.annotation  
FloatArray kotlin.annotation  FocusMeteringAction kotlin.annotation  GYRO_THRESHOLD_HIGH kotlin.annotation  GYRO_THRESHOLD_LOW kotlin.annotation  GestureDetector kotlin.annotation  GestureType kotlin.annotation  HIGH_ZOOM_THRESHOLD kotlin.annotation  Handler kotlin.annotation  
IMAGE_QUALITY kotlin.annotation  IllegalArgumentException kotlin.annotation  ImageFilterProcessor kotlin.annotation  	ImageView kotlin.annotation  IntArray kotlin.annotation  Intent kotlin.annotation  JvmOverloads kotlin.annotation  KEY_CURRENT_MODE kotlin.annotation  KEY_LAST_BRIGHTNESS kotlin.annotation  KEY_LAST_ZOOM_RATIO kotlin.annotation  LONG_PRESS_TIMEOUT kotlin.annotation  LayoutInflater kotlin.annotation  LinearLayout kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  
LowPassFilter kotlin.annotation  MAX_BRIGHTNESS kotlin.annotation  MAX_ZOOM_RATIO kotlin.annotation  MEMORY_CHECK_INTERVAL kotlin.annotation  MEMORY_WARNING_THRESHOLD kotlin.annotation  MIN_BRIGHTNESS kotlin.annotation  MIN_SWIPE_DISTANCE kotlin.annotation  MIN_ZOOM_RATIO kotlin.annotation  MainActivity kotlin.annotation  Manifest kotlin.annotation  Math kotlin.annotation  
MediaStore kotlin.annotation  ModeConfiguration kotlin.annotation  	ModelType kotlin.annotation  MotionEvent kotlin.annotation  ObjectAnimator kotlin.annotation  Observer kotlin.annotation  OnboardingAdapter kotlin.annotation  OnboardingAnimator kotlin.annotation  OnboardingPage kotlin.annotation  OnboardingViewHolder kotlin.annotation  OvershootInterpolator kotlin.annotation  
PREFS_NAME kotlin.annotation  PackageManager kotlin.annotation  Paint kotlin.annotation  Pair kotlin.annotation  Path kotlin.annotation  PerformanceReport kotlin.annotation  
PorterDuff kotlin.annotation  PorterDuffXfermode kotlin.annotation  Preview kotlin.annotation  ProcessCameraProvider kotlin.annotation  R kotlin.annotation  RadialGradient kotlin.annotation  Result kotlin.annotation  Runnable kotlin.annotation  SCALE_DURATION kotlin.annotation  SCALE_FACTOR kotlin.annotation  SENSOR_DELAY kotlin.annotation  SLIDE_DURATION kotlin.annotation  SPLASH_DELAY kotlin.annotation  STORAGE_PERMISSION_REQUEST_CODE kotlin.annotation  SaveStatistics kotlin.annotation  ScaleGestureDetector kotlin.annotation  Sensor kotlin.annotation  
SensorManager kotlin.annotation  Settings kotlin.annotation  Shader kotlin.annotation  SimpleDateFormat kotlin.annotation  #SurfaceOrientedMeteringPointFactory kotlin.annotation  System kotlin.annotation  TAG kotlin.annotation  Toast kotlin.annotation  TransitionDirection kotlin.annotation  Triple kotlin.annotation  Uri kotlin.annotation  
ValueAnimator kotlin.annotation  View kotlin.annotation  	ViewGroup kotlin.annotation  
WindowManager kotlin.annotation  ZOOM_ANIMATION_DURATION kotlin.annotation  abs kotlin.annotation  activity kotlin.annotation  addListener kotlin.annotation  aiStyleEdgeEnhancement kotlin.annotation  all kotlin.annotation  also kotlin.annotation  android kotlin.annotation  apply kotlin.annotation  applyNoiseReduction kotlin.annotation  applySharpeningFilter kotlin.annotation  arrayOf kotlin.annotation  average kotlin.annotation  bicubicUpscale kotlin.annotation  camera kotlin.annotation  cancelLongPress kotlin.annotation  checkMemoryUsage kotlin.annotation  
coerceAtLeast kotlin.annotation  coerceAtMost kotlin.annotation  coerceIn kotlin.annotation  !colorEnhancementAndDetailRecovery kotlin.annotation  contains kotlin.annotation  context kotlin.annotation  count kotlin.annotation  currentPage kotlin.annotation  deepSharpening kotlin.annotation  delay kotlin.annotation  endsWith kotlin.annotation  
enhanceColors kotlin.annotation  enhanceEdges kotlin.annotation  equals kotlin.annotation  floatArrayOf kotlin.annotation  forEach kotlin.annotation  format kotlin.annotation  generateFileName kotlin.annotation  gestureListener kotlin.annotation  handler kotlin.annotation  indexOf kotlin.annotation  indices kotlin.annotation  
intArrayOf kotlin.annotation  intelligentDenoising kotlin.annotation  isEdgeSwipeActive kotlin.annotation  
isInitialized kotlin.annotation  isLongPressTriggered kotlin.annotation  isMonitoring kotlin.annotation  
isNotEmpty kotlin.annotation  java kotlin.annotation  kotlin kotlin.annotation  kotlinx kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  mapOf kotlin.annotation  maxByOrNull kotlin.annotation  maxOf kotlin.annotation  minByOrNull kotlin.annotation  minOf kotlin.annotation  
mutableListOf kotlin.annotation  mutableMapOf kotlin.annotation  navigateToMain kotlin.annotation  
plusAssign kotlin.annotation  saveToExternalStorage kotlin.annotation  saveToMediaStore kotlin.annotation  set kotlin.annotation  smartUpscale kotlin.annotation  sqrt kotlin.annotation  to kotlin.annotation  toTypedArray kotlin.annotation  triggerAutoFocus kotlin.annotation  until kotlin.annotation  
updateButtons kotlin.annotation  updateIndicators kotlin.annotation  use kotlin.annotation  withContext kotlin.annotation  ACCEL_THRESHOLD_HIGH kotlin.collections  ACCEL_THRESHOLD_LOW kotlin.collections  ALL_PERMISSIONS_REQUEST_CODE kotlin.collections   AccelerateDecelerateInterpolator kotlin.collections  ActivityCompat kotlin.collections  ActivityMainBinding kotlin.collections  ActivityManager kotlin.collections  ActivityOnboardingBinding kotlin.collections  ActivityResultContracts kotlin.collections  ActivitySplashBinding kotlin.collections  AlertDialog kotlin.collections  AnimatorSet kotlin.collections  Array kotlin.collections  
AutoFocusMode kotlin.collections  BOUNCE_DURATION kotlin.collections  BRIGHTNESS_INDICATOR_TIMEOUT kotlin.collections  BRIGHTNESS_SENSITIVITY kotlin.collections  Bitmap kotlin.collections  BlurMaskFilter kotlin.collections  BounceInterpolator kotlin.collections  Build kotlin.collections  CAMERA_PERMISSION_REQUEST_CODE kotlin.collections  Camera2CameraInfo kotlin.collections  CameraCharacteristics kotlin.collections  CameraController kotlin.collections  
CameraMode kotlin.collections  CameraSelector kotlin.collections  CameraSettings kotlin.collections  Canvas kotlin.collections  Color kotlin.collections  
ColorDrawable kotlin.collections  ColorMatrix kotlin.collections  ColorMatrixColorFilter kotlin.collections  
ContentValues kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  DATE_FORMAT kotlin.collections  DEFAULT_BRIGHTNESS kotlin.collections  DEFAULT_ZOOM_STEP kotlin.collections  Date kotlin.collections  Debug kotlin.collections  DecelerateInterpolator kotlin.collections  
DeviceInfo kotlin.collections  Dispatchers kotlin.collections  EDGE_THRESHOLD_DP kotlin.collections  Environment kotlin.collections  	Exception kotlin.collections  ExperimentalCamera2Interop kotlin.collections  
FADE_DURATION kotlin.collections  FILTER_ALPHA_HIGH kotlin.collections  FILTER_ALPHA_LOW kotlin.collections  FOLDER_NAME kotlin.collections  File kotlin.collections  FileOutputStream kotlin.collections  
FilterType kotlin.collections  
FloatArray kotlin.collections  FocusMeteringAction kotlin.collections  GYRO_THRESHOLD_HIGH kotlin.collections  GYRO_THRESHOLD_LOW kotlin.collections  GestureDetector kotlin.collections  GestureType kotlin.collections  HIGH_ZOOM_THRESHOLD kotlin.collections  Handler kotlin.collections  
IMAGE_QUALITY kotlin.collections  IllegalArgumentException kotlin.collections  ImageFilterProcessor kotlin.collections  	ImageView kotlin.collections  IntArray kotlin.collections  Intent kotlin.collections  JvmOverloads kotlin.collections  KEY_CURRENT_MODE kotlin.collections  KEY_LAST_BRIGHTNESS kotlin.collections  KEY_LAST_ZOOM_RATIO kotlin.collections  LONG_PRESS_TIMEOUT kotlin.collections  LayoutInflater kotlin.collections  LinearLayout kotlin.collections  List kotlin.collections  Locale kotlin.collections  Log kotlin.collections  Looper kotlin.collections  
LowPassFilter kotlin.collections  MAX_BRIGHTNESS kotlin.collections  MAX_ZOOM_RATIO kotlin.collections  MEMORY_CHECK_INTERVAL kotlin.collections  MEMORY_WARNING_THRESHOLD kotlin.collections  MIN_BRIGHTNESS kotlin.collections  MIN_SWIPE_DISTANCE kotlin.collections  MIN_ZOOM_RATIO kotlin.collections  MainActivity kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  Math kotlin.collections  
MediaStore kotlin.collections  ModeConfiguration kotlin.collections  	ModelType kotlin.collections  MotionEvent kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  ObjectAnimator kotlin.collections  Observer kotlin.collections  OnboardingAdapter kotlin.collections  OnboardingAnimator kotlin.collections  OnboardingPage kotlin.collections  OnboardingViewHolder kotlin.collections  OvershootInterpolator kotlin.collections  
PREFS_NAME kotlin.collections  PackageManager kotlin.collections  Paint kotlin.collections  Pair kotlin.collections  Path kotlin.collections  PerformanceReport kotlin.collections  
PorterDuff kotlin.collections  PorterDuffXfermode kotlin.collections  Preview kotlin.collections  ProcessCameraProvider kotlin.collections  R kotlin.collections  RadialGradient kotlin.collections  Result kotlin.collections  Runnable kotlin.collections  SCALE_DURATION kotlin.collections  SCALE_FACTOR kotlin.collections  SENSOR_DELAY kotlin.collections  SLIDE_DURATION kotlin.collections  SPLASH_DELAY kotlin.collections  STORAGE_PERMISSION_REQUEST_CODE kotlin.collections  SaveStatistics kotlin.collections  ScaleGestureDetector kotlin.collections  Sensor kotlin.collections  
SensorManager kotlin.collections  Settings kotlin.collections  Shader kotlin.collections  SimpleDateFormat kotlin.collections  #SurfaceOrientedMeteringPointFactory kotlin.collections  System kotlin.collections  TAG kotlin.collections  Toast kotlin.collections  TransitionDirection kotlin.collections  Triple kotlin.collections  Uri kotlin.collections  
ValueAnimator kotlin.collections  View kotlin.collections  	ViewGroup kotlin.collections  
WindowManager kotlin.collections  ZOOM_ANIMATION_DURATION kotlin.collections  abs kotlin.collections  activity kotlin.collections  addListener kotlin.collections  aiStyleEdgeEnhancement kotlin.collections  all kotlin.collections  also kotlin.collections  android kotlin.collections  apply kotlin.collections  applyNoiseReduction kotlin.collections  applySharpeningFilter kotlin.collections  arrayOf kotlin.collections  average kotlin.collections  bicubicUpscale kotlin.collections  camera kotlin.collections  cancelLongPress kotlin.collections  checkMemoryUsage kotlin.collections  
coerceAtLeast kotlin.collections  coerceAtMost kotlin.collections  coerceIn kotlin.collections  !colorEnhancementAndDetailRecovery kotlin.collections  contains kotlin.collections  context kotlin.collections  count kotlin.collections  currentPage kotlin.collections  deepSharpening kotlin.collections  delay kotlin.collections  endsWith kotlin.collections  
enhanceColors kotlin.collections  enhanceEdges kotlin.collections  equals kotlin.collections  floatArrayOf kotlin.collections  forEach kotlin.collections  format kotlin.collections  generateFileName kotlin.collections  gestureListener kotlin.collections  handler kotlin.collections  indexOf kotlin.collections  indices kotlin.collections  
intArrayOf kotlin.collections  intelligentDenoising kotlin.collections  isEdgeSwipeActive kotlin.collections  
isInitialized kotlin.collections  isLongPressTriggered kotlin.collections  isMonitoring kotlin.collections  
isNotEmpty kotlin.collections  java kotlin.collections  kotlin kotlin.collections  kotlinx kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  maxByOrNull kotlin.collections  maxOf kotlin.collections  minByOrNull kotlin.collections  minOf kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  navigateToMain kotlin.collections  
plusAssign kotlin.collections  saveToExternalStorage kotlin.collections  saveToMediaStore kotlin.collections  set kotlin.collections  smartUpscale kotlin.collections  sqrt kotlin.collections  to kotlin.collections  toTypedArray kotlin.collections  triggerAutoFocus kotlin.collections  until kotlin.collections  
updateButtons kotlin.collections  updateIndicators kotlin.collections  use kotlin.collections  withContext kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  getMINByOrNull kotlin.collections.List  getMinByOrNull kotlin.collections.List  Entry kotlin.collections.Map  getMAXByOrNull kotlin.collections.Map  getMaxByOrNull kotlin.collections.Map  
getAVERAGE kotlin.collections.MutableList  
getAverage kotlin.collections.MutableList  getCOUNT kotlin.collections.MutableList  getCount kotlin.collections.MutableList  
getISNotEmpty kotlin.collections.MutableList  
getIsNotEmpty kotlin.collections.MutableList  getTOTypedArray kotlin.collections.MutableList  getToTypedArray kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  ACCEL_THRESHOLD_HIGH kotlin.comparisons  ACCEL_THRESHOLD_LOW kotlin.comparisons  ALL_PERMISSIONS_REQUEST_CODE kotlin.comparisons   AccelerateDecelerateInterpolator kotlin.comparisons  ActivityCompat kotlin.comparisons  ActivityMainBinding kotlin.comparisons  ActivityManager kotlin.comparisons  ActivityOnboardingBinding kotlin.comparisons  ActivityResultContracts kotlin.comparisons  ActivitySplashBinding kotlin.comparisons  AlertDialog kotlin.comparisons  AnimatorSet kotlin.comparisons  Array kotlin.comparisons  
AutoFocusMode kotlin.comparisons  BOUNCE_DURATION kotlin.comparisons  BRIGHTNESS_INDICATOR_TIMEOUT kotlin.comparisons  BRIGHTNESS_SENSITIVITY kotlin.comparisons  Bitmap kotlin.comparisons  BlurMaskFilter kotlin.comparisons  BounceInterpolator kotlin.comparisons  Build kotlin.comparisons  CAMERA_PERMISSION_REQUEST_CODE kotlin.comparisons  Camera2CameraInfo kotlin.comparisons  CameraCharacteristics kotlin.comparisons  CameraController kotlin.comparisons  
CameraMode kotlin.comparisons  CameraSelector kotlin.comparisons  CameraSettings kotlin.comparisons  Canvas kotlin.comparisons  Color kotlin.comparisons  
ColorDrawable kotlin.comparisons  ColorMatrix kotlin.comparisons  ColorMatrixColorFilter kotlin.comparisons  
ContentValues kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  DATE_FORMAT kotlin.comparisons  DEFAULT_BRIGHTNESS kotlin.comparisons  DEFAULT_ZOOM_STEP kotlin.comparisons  Date kotlin.comparisons  Debug kotlin.comparisons  DecelerateInterpolator kotlin.comparisons  
DeviceInfo kotlin.comparisons  Dispatchers kotlin.comparisons  EDGE_THRESHOLD_DP kotlin.comparisons  Environment kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalCamera2Interop kotlin.comparisons  
FADE_DURATION kotlin.comparisons  FILTER_ALPHA_HIGH kotlin.comparisons  FILTER_ALPHA_LOW kotlin.comparisons  FOLDER_NAME kotlin.comparisons  File kotlin.comparisons  FileOutputStream kotlin.comparisons  
FilterType kotlin.comparisons  
FloatArray kotlin.comparisons  FocusMeteringAction kotlin.comparisons  GYRO_THRESHOLD_HIGH kotlin.comparisons  GYRO_THRESHOLD_LOW kotlin.comparisons  GestureDetector kotlin.comparisons  GestureType kotlin.comparisons  HIGH_ZOOM_THRESHOLD kotlin.comparisons  Handler kotlin.comparisons  
IMAGE_QUALITY kotlin.comparisons  IllegalArgumentException kotlin.comparisons  ImageFilterProcessor kotlin.comparisons  	ImageView kotlin.comparisons  IntArray kotlin.comparisons  Intent kotlin.comparisons  JvmOverloads kotlin.comparisons  KEY_CURRENT_MODE kotlin.comparisons  KEY_LAST_BRIGHTNESS kotlin.comparisons  KEY_LAST_ZOOM_RATIO kotlin.comparisons  LONG_PRESS_TIMEOUT kotlin.comparisons  LayoutInflater kotlin.comparisons  LinearLayout kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  
LowPassFilter kotlin.comparisons  MAX_BRIGHTNESS kotlin.comparisons  MAX_ZOOM_RATIO kotlin.comparisons  MEMORY_CHECK_INTERVAL kotlin.comparisons  MEMORY_WARNING_THRESHOLD kotlin.comparisons  MIN_BRIGHTNESS kotlin.comparisons  MIN_SWIPE_DISTANCE kotlin.comparisons  MIN_ZOOM_RATIO kotlin.comparisons  MainActivity kotlin.comparisons  Manifest kotlin.comparisons  Math kotlin.comparisons  
MediaStore kotlin.comparisons  ModeConfiguration kotlin.comparisons  	ModelType kotlin.comparisons  MotionEvent kotlin.comparisons  ObjectAnimator kotlin.comparisons  Observer kotlin.comparisons  OnboardingAdapter kotlin.comparisons  OnboardingAnimator kotlin.comparisons  OnboardingPage kotlin.comparisons  OnboardingViewHolder kotlin.comparisons  OvershootInterpolator kotlin.comparisons  
PREFS_NAME kotlin.comparisons  PackageManager kotlin.comparisons  Paint kotlin.comparisons  Pair kotlin.comparisons  Path kotlin.comparisons  PerformanceReport kotlin.comparisons  
PorterDuff kotlin.comparisons  PorterDuffXfermode kotlin.comparisons  Preview kotlin.comparisons  ProcessCameraProvider kotlin.comparisons  R kotlin.comparisons  RadialGradient kotlin.comparisons  Result kotlin.comparisons  Runnable kotlin.comparisons  SCALE_DURATION kotlin.comparisons  SCALE_FACTOR kotlin.comparisons  SENSOR_DELAY kotlin.comparisons  SLIDE_DURATION kotlin.comparisons  SPLASH_DELAY kotlin.comparisons  STORAGE_PERMISSION_REQUEST_CODE kotlin.comparisons  SaveStatistics kotlin.comparisons  ScaleGestureDetector kotlin.comparisons  Sensor kotlin.comparisons  
SensorManager kotlin.comparisons  Settings kotlin.comparisons  Shader kotlin.comparisons  SimpleDateFormat kotlin.comparisons  #SurfaceOrientedMeteringPointFactory kotlin.comparisons  System kotlin.comparisons  TAG kotlin.comparisons  Toast kotlin.comparisons  TransitionDirection kotlin.comparisons  Triple kotlin.comparisons  Uri kotlin.comparisons  
ValueAnimator kotlin.comparisons  View kotlin.comparisons  	ViewGroup kotlin.comparisons  
WindowManager kotlin.comparisons  ZOOM_ANIMATION_DURATION kotlin.comparisons  abs kotlin.comparisons  activity kotlin.comparisons  addListener kotlin.comparisons  aiStyleEdgeEnhancement kotlin.comparisons  all kotlin.comparisons  also kotlin.comparisons  android kotlin.comparisons  apply kotlin.comparisons  applyNoiseReduction kotlin.comparisons  applySharpeningFilter kotlin.comparisons  arrayOf kotlin.comparisons  average kotlin.comparisons  bicubicUpscale kotlin.comparisons  camera kotlin.comparisons  cancelLongPress kotlin.comparisons  checkMemoryUsage kotlin.comparisons  
coerceAtLeast kotlin.comparisons  coerceAtMost kotlin.comparisons  coerceIn kotlin.comparisons  !colorEnhancementAndDetailRecovery kotlin.comparisons  contains kotlin.comparisons  context kotlin.comparisons  count kotlin.comparisons  currentPage kotlin.comparisons  deepSharpening kotlin.comparisons  delay kotlin.comparisons  endsWith kotlin.comparisons  
enhanceColors kotlin.comparisons  enhanceEdges kotlin.comparisons  equals kotlin.comparisons  floatArrayOf kotlin.comparisons  forEach kotlin.comparisons  format kotlin.comparisons  generateFileName kotlin.comparisons  gestureListener kotlin.comparisons  handler kotlin.comparisons  indexOf kotlin.comparisons  indices kotlin.comparisons  
intArrayOf kotlin.comparisons  intelligentDenoising kotlin.comparisons  isEdgeSwipeActive kotlin.comparisons  
isInitialized kotlin.comparisons  isLongPressTriggered kotlin.comparisons  isMonitoring kotlin.comparisons  
isNotEmpty kotlin.comparisons  java kotlin.comparisons  kotlin kotlin.comparisons  kotlinx kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  mapOf kotlin.comparisons  maxByOrNull kotlin.comparisons  maxOf kotlin.comparisons  minByOrNull kotlin.comparisons  minOf kotlin.comparisons  
mutableListOf kotlin.comparisons  mutableMapOf kotlin.comparisons  navigateToMain kotlin.comparisons  
plusAssign kotlin.comparisons  saveToExternalStorage kotlin.comparisons  saveToMediaStore kotlin.comparisons  set kotlin.comparisons  smartUpscale kotlin.comparisons  sqrt kotlin.comparisons  to kotlin.comparisons  toTypedArray kotlin.comparisons  triggerAutoFocus kotlin.comparisons  until kotlin.comparisons  
updateButtons kotlin.comparisons  updateIndicators kotlin.comparisons  use kotlin.comparisons  withContext kotlin.comparisons  SuspendFunction1 kotlin.coroutines  ACCEL_THRESHOLD_HIGH 	kotlin.io  ACCEL_THRESHOLD_LOW 	kotlin.io  ALL_PERMISSIONS_REQUEST_CODE 	kotlin.io   AccelerateDecelerateInterpolator 	kotlin.io  ActivityCompat 	kotlin.io  ActivityMainBinding 	kotlin.io  ActivityManager 	kotlin.io  ActivityOnboardingBinding 	kotlin.io  ActivityResultContracts 	kotlin.io  ActivitySplashBinding 	kotlin.io  AlertDialog 	kotlin.io  AnimatorSet 	kotlin.io  Array 	kotlin.io  
AutoFocusMode 	kotlin.io  BOUNCE_DURATION 	kotlin.io  BRIGHTNESS_INDICATOR_TIMEOUT 	kotlin.io  BRIGHTNESS_SENSITIVITY 	kotlin.io  Bitmap 	kotlin.io  BlurMaskFilter 	kotlin.io  BounceInterpolator 	kotlin.io  Build 	kotlin.io  CAMERA_PERMISSION_REQUEST_CODE 	kotlin.io  Camera2CameraInfo 	kotlin.io  CameraCharacteristics 	kotlin.io  CameraController 	kotlin.io  
CameraMode 	kotlin.io  CameraSelector 	kotlin.io  CameraSettings 	kotlin.io  Canvas 	kotlin.io  Color 	kotlin.io  
ColorDrawable 	kotlin.io  ColorMatrix 	kotlin.io  ColorMatrixColorFilter 	kotlin.io  
ContentValues 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  DATE_FORMAT 	kotlin.io  DEFAULT_BRIGHTNESS 	kotlin.io  DEFAULT_ZOOM_STEP 	kotlin.io  Date 	kotlin.io  Debug 	kotlin.io  DecelerateInterpolator 	kotlin.io  
DeviceInfo 	kotlin.io  Dispatchers 	kotlin.io  EDGE_THRESHOLD_DP 	kotlin.io  Environment 	kotlin.io  	Exception 	kotlin.io  ExperimentalCamera2Interop 	kotlin.io  
FADE_DURATION 	kotlin.io  FILTER_ALPHA_HIGH 	kotlin.io  FILTER_ALPHA_LOW 	kotlin.io  FOLDER_NAME 	kotlin.io  File 	kotlin.io  FileOutputStream 	kotlin.io  
FilterType 	kotlin.io  
FloatArray 	kotlin.io  FocusMeteringAction 	kotlin.io  GYRO_THRESHOLD_HIGH 	kotlin.io  GYRO_THRESHOLD_LOW 	kotlin.io  GestureDetector 	kotlin.io  GestureType 	kotlin.io  HIGH_ZOOM_THRESHOLD 	kotlin.io  Handler 	kotlin.io  
IMAGE_QUALITY 	kotlin.io  IllegalArgumentException 	kotlin.io  ImageFilterProcessor 	kotlin.io  	ImageView 	kotlin.io  IntArray 	kotlin.io  Intent 	kotlin.io  JvmOverloads 	kotlin.io  KEY_CURRENT_MODE 	kotlin.io  KEY_LAST_BRIGHTNESS 	kotlin.io  KEY_LAST_ZOOM_RATIO 	kotlin.io  LONG_PRESS_TIMEOUT 	kotlin.io  LayoutInflater 	kotlin.io  LinearLayout 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  
LowPassFilter 	kotlin.io  MAX_BRIGHTNESS 	kotlin.io  MAX_ZOOM_RATIO 	kotlin.io  MEMORY_CHECK_INTERVAL 	kotlin.io  MEMORY_WARNING_THRESHOLD 	kotlin.io  MIN_BRIGHTNESS 	kotlin.io  MIN_SWIPE_DISTANCE 	kotlin.io  MIN_ZOOM_RATIO 	kotlin.io  MainActivity 	kotlin.io  Manifest 	kotlin.io  Math 	kotlin.io  
MediaStore 	kotlin.io  ModeConfiguration 	kotlin.io  	ModelType 	kotlin.io  MotionEvent 	kotlin.io  ObjectAnimator 	kotlin.io  Observer 	kotlin.io  OnboardingAdapter 	kotlin.io  OnboardingAnimator 	kotlin.io  OnboardingPage 	kotlin.io  OnboardingViewHolder 	kotlin.io  OvershootInterpolator 	kotlin.io  
PREFS_NAME 	kotlin.io  PackageManager 	kotlin.io  Paint 	kotlin.io  Pair 	kotlin.io  Path 	kotlin.io  PerformanceReport 	kotlin.io  
PorterDuff 	kotlin.io  PorterDuffXfermode 	kotlin.io  Preview 	kotlin.io  ProcessCameraProvider 	kotlin.io  R 	kotlin.io  RadialGradient 	kotlin.io  Result 	kotlin.io  Runnable 	kotlin.io  SCALE_DURATION 	kotlin.io  SCALE_FACTOR 	kotlin.io  SENSOR_DELAY 	kotlin.io  SLIDE_DURATION 	kotlin.io  SPLASH_DELAY 	kotlin.io  STORAGE_PERMISSION_REQUEST_CODE 	kotlin.io  SaveStatistics 	kotlin.io  ScaleGestureDetector 	kotlin.io  Sensor 	kotlin.io  
SensorManager 	kotlin.io  Settings 	kotlin.io  Shader 	kotlin.io  SimpleDateFormat 	kotlin.io  #SurfaceOrientedMeteringPointFactory 	kotlin.io  System 	kotlin.io  TAG 	kotlin.io  Toast 	kotlin.io  TransitionDirection 	kotlin.io  Triple 	kotlin.io  Uri 	kotlin.io  
ValueAnimator 	kotlin.io  View 	kotlin.io  	ViewGroup 	kotlin.io  
WindowManager 	kotlin.io  ZOOM_ANIMATION_DURATION 	kotlin.io  abs 	kotlin.io  activity 	kotlin.io  addListener 	kotlin.io  aiStyleEdgeEnhancement 	kotlin.io  all 	kotlin.io  also 	kotlin.io  android 	kotlin.io  apply 	kotlin.io  applyNoiseReduction 	kotlin.io  applySharpeningFilter 	kotlin.io  arrayOf 	kotlin.io  average 	kotlin.io  bicubicUpscale 	kotlin.io  camera 	kotlin.io  cancelLongPress 	kotlin.io  checkMemoryUsage 	kotlin.io  
coerceAtLeast 	kotlin.io  coerceAtMost 	kotlin.io  coerceIn 	kotlin.io  !colorEnhancementAndDetailRecovery 	kotlin.io  contains 	kotlin.io  context 	kotlin.io  count 	kotlin.io  currentPage 	kotlin.io  deepSharpening 	kotlin.io  delay 	kotlin.io  endsWith 	kotlin.io  
enhanceColors 	kotlin.io  enhanceEdges 	kotlin.io  equals 	kotlin.io  floatArrayOf 	kotlin.io  forEach 	kotlin.io  format 	kotlin.io  generateFileName 	kotlin.io  gestureListener 	kotlin.io  handler 	kotlin.io  indexOf 	kotlin.io  indices 	kotlin.io  
intArrayOf 	kotlin.io  intelligentDenoising 	kotlin.io  isEdgeSwipeActive 	kotlin.io  
isInitialized 	kotlin.io  isLongPressTriggered 	kotlin.io  isMonitoring 	kotlin.io  
isNotEmpty 	kotlin.io  java 	kotlin.io  kotlin 	kotlin.io  kotlinx 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  mapOf 	kotlin.io  maxByOrNull 	kotlin.io  maxOf 	kotlin.io  minByOrNull 	kotlin.io  minOf 	kotlin.io  
mutableListOf 	kotlin.io  mutableMapOf 	kotlin.io  navigateToMain 	kotlin.io  
plusAssign 	kotlin.io  saveToExternalStorage 	kotlin.io  saveToMediaStore 	kotlin.io  set 	kotlin.io  smartUpscale 	kotlin.io  sqrt 	kotlin.io  to 	kotlin.io  toTypedArray 	kotlin.io  triggerAutoFocus 	kotlin.io  until 	kotlin.io  
updateButtons 	kotlin.io  updateIndicators 	kotlin.io  use 	kotlin.io  withContext 	kotlin.io  ACCEL_THRESHOLD_HIGH 
kotlin.jvm  ACCEL_THRESHOLD_LOW 
kotlin.jvm  ALL_PERMISSIONS_REQUEST_CODE 
kotlin.jvm   AccelerateDecelerateInterpolator 
kotlin.jvm  ActivityCompat 
kotlin.jvm  ActivityMainBinding 
kotlin.jvm  ActivityManager 
kotlin.jvm  ActivityOnboardingBinding 
kotlin.jvm  ActivityResultContracts 
kotlin.jvm  ActivitySplashBinding 
kotlin.jvm  AlertDialog 
kotlin.jvm  AnimatorSet 
kotlin.jvm  Array 
kotlin.jvm  
AutoFocusMode 
kotlin.jvm  BOUNCE_DURATION 
kotlin.jvm  BRIGHTNESS_INDICATOR_TIMEOUT 
kotlin.jvm  BRIGHTNESS_SENSITIVITY 
kotlin.jvm  Bitmap 
kotlin.jvm  BlurMaskFilter 
kotlin.jvm  BounceInterpolator 
kotlin.jvm  Build 
kotlin.jvm  CAMERA_PERMISSION_REQUEST_CODE 
kotlin.jvm  Camera2CameraInfo 
kotlin.jvm  CameraCharacteristics 
kotlin.jvm  CameraController 
kotlin.jvm  
CameraMode 
kotlin.jvm  CameraSelector 
kotlin.jvm  CameraSettings 
kotlin.jvm  Canvas 
kotlin.jvm  Color 
kotlin.jvm  
ColorDrawable 
kotlin.jvm  ColorMatrix 
kotlin.jvm  ColorMatrixColorFilter 
kotlin.jvm  
ContentValues 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  DATE_FORMAT 
kotlin.jvm  DEFAULT_BRIGHTNESS 
kotlin.jvm  DEFAULT_ZOOM_STEP 
kotlin.jvm  Date 
kotlin.jvm  Debug 
kotlin.jvm  DecelerateInterpolator 
kotlin.jvm  
DeviceInfo 
kotlin.jvm  Dispatchers 
kotlin.jvm  EDGE_THRESHOLD_DP 
kotlin.jvm  Environment 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalCamera2Interop 
kotlin.jvm  
FADE_DURATION 
kotlin.jvm  FILTER_ALPHA_HIGH 
kotlin.jvm  FILTER_ALPHA_LOW 
kotlin.jvm  FOLDER_NAME 
kotlin.jvm  File 
kotlin.jvm  FileOutputStream 
kotlin.jvm  
FilterType 
kotlin.jvm  
FloatArray 
kotlin.jvm  FocusMeteringAction 
kotlin.jvm  GYRO_THRESHOLD_HIGH 
kotlin.jvm  GYRO_THRESHOLD_LOW 
kotlin.jvm  GestureDetector 
kotlin.jvm  GestureType 
kotlin.jvm  HIGH_ZOOM_THRESHOLD 
kotlin.jvm  Handler 
kotlin.jvm  
IMAGE_QUALITY 
kotlin.jvm  IllegalArgumentException 
kotlin.jvm  ImageFilterProcessor 
kotlin.jvm  	ImageView 
kotlin.jvm  IntArray 
kotlin.jvm  Intent 
kotlin.jvm  JvmOverloads 
kotlin.jvm  KEY_CURRENT_MODE 
kotlin.jvm  KEY_LAST_BRIGHTNESS 
kotlin.jvm  KEY_LAST_ZOOM_RATIO 
kotlin.jvm  LONG_PRESS_TIMEOUT 
kotlin.jvm  LayoutInflater 
kotlin.jvm  LinearLayout 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  
LowPassFilter 
kotlin.jvm  MAX_BRIGHTNESS 
kotlin.jvm  MAX_ZOOM_RATIO 
kotlin.jvm  MEMORY_CHECK_INTERVAL 
kotlin.jvm  MEMORY_WARNING_THRESHOLD 
kotlin.jvm  MIN_BRIGHTNESS 
kotlin.jvm  MIN_SWIPE_DISTANCE 
kotlin.jvm  MIN_ZOOM_RATIO 
kotlin.jvm  MainActivity 
kotlin.jvm  Manifest 
kotlin.jvm  Math 
kotlin.jvm  
MediaStore 
kotlin.jvm  ModeConfiguration 
kotlin.jvm  	ModelType 
kotlin.jvm  MotionEvent 
kotlin.jvm  ObjectAnimator 
kotlin.jvm  Observer 
kotlin.jvm  OnboardingAdapter 
kotlin.jvm  OnboardingAnimator 
kotlin.jvm  OnboardingPage 
kotlin.jvm  OnboardingViewHolder 
kotlin.jvm  OvershootInterpolator 
kotlin.jvm  
PREFS_NAME 
kotlin.jvm  PackageManager 
kotlin.jvm  Paint 
kotlin.jvm  Pair 
kotlin.jvm  Path 
kotlin.jvm  PerformanceReport 
kotlin.jvm  
PorterDuff 
kotlin.jvm  PorterDuffXfermode 
kotlin.jvm  Preview 
kotlin.jvm  ProcessCameraProvider 
kotlin.jvm  R 
kotlin.jvm  RadialGradient 
kotlin.jvm  Result 
kotlin.jvm  Runnable 
kotlin.jvm  SCALE_DURATION 
kotlin.jvm  SCALE_FACTOR 
kotlin.jvm  SENSOR_DELAY 
kotlin.jvm  SLIDE_DURATION 
kotlin.jvm  SPLASH_DELAY 
kotlin.jvm  STORAGE_PERMISSION_REQUEST_CODE 
kotlin.jvm  SaveStatistics 
kotlin.jvm  ScaleGestureDetector 
kotlin.jvm  Sensor 
kotlin.jvm  
SensorManager 
kotlin.jvm  Settings 
kotlin.jvm  Shader 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  #SurfaceOrientedMeteringPointFactory 
kotlin.jvm  System 
kotlin.jvm  TAG 
kotlin.jvm  Toast 
kotlin.jvm  TransitionDirection 
kotlin.jvm  Triple 
kotlin.jvm  Uri 
kotlin.jvm  
ValueAnimator 
kotlin.jvm  View 
kotlin.jvm  	ViewGroup 
kotlin.jvm  
WindowManager 
kotlin.jvm  ZOOM_ANIMATION_DURATION 
kotlin.jvm  abs 
kotlin.jvm  activity 
kotlin.jvm  addListener 
kotlin.jvm  aiStyleEdgeEnhancement 
kotlin.jvm  all 
kotlin.jvm  also 
kotlin.jvm  android 
kotlin.jvm  apply 
kotlin.jvm  applyNoiseReduction 
kotlin.jvm  applySharpeningFilter 
kotlin.jvm  arrayOf 
kotlin.jvm  average 
kotlin.jvm  bicubicUpscale 
kotlin.jvm  camera 
kotlin.jvm  cancelLongPress 
kotlin.jvm  checkMemoryUsage 
kotlin.jvm  
coerceAtLeast 
kotlin.jvm  coerceAtMost 
kotlin.jvm  coerceIn 
kotlin.jvm  !colorEnhancementAndDetailRecovery 
kotlin.jvm  contains 
kotlin.jvm  context 
kotlin.jvm  count 
kotlin.jvm  currentPage 
kotlin.jvm  deepSharpening 
kotlin.jvm  delay 
kotlin.jvm  endsWith 
kotlin.jvm  
enhanceColors 
kotlin.jvm  enhanceEdges 
kotlin.jvm  equals 
kotlin.jvm  floatArrayOf 
kotlin.jvm  forEach 
kotlin.jvm  format 
kotlin.jvm  generateFileName 
kotlin.jvm  gestureListener 
kotlin.jvm  handler 
kotlin.jvm  indexOf 
kotlin.jvm  indices 
kotlin.jvm  
intArrayOf 
kotlin.jvm  intelligentDenoising 
kotlin.jvm  isEdgeSwipeActive 
kotlin.jvm  
isInitialized 
kotlin.jvm  isLongPressTriggered 
kotlin.jvm  isMonitoring 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  java 
kotlin.jvm  kotlin 
kotlin.jvm  kotlinx 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  mapOf 
kotlin.jvm  maxByOrNull 
kotlin.jvm  maxOf 
kotlin.jvm  minByOrNull 
kotlin.jvm  minOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  mutableMapOf 
kotlin.jvm  navigateToMain 
kotlin.jvm  
plusAssign 
kotlin.jvm  saveToExternalStorage 
kotlin.jvm  saveToMediaStore 
kotlin.jvm  set 
kotlin.jvm  smartUpscale 
kotlin.jvm  sqrt 
kotlin.jvm  to 
kotlin.jvm  toTypedArray 
kotlin.jvm  triggerAutoFocus 
kotlin.jvm  until 
kotlin.jvm  
updateButtons 
kotlin.jvm  updateIndicators 
kotlin.jvm  use 
kotlin.jvm  withContext 
kotlin.jvm  Bitmap kotlin.math  Canvas kotlin.math  ColorMatrix kotlin.math  ColorMatrixColorFilter kotlin.math  Dispatchers kotlin.math  	Exception kotlin.math  IntArray kotlin.math  Log kotlin.math  Paint kotlin.math  SCALE_FACTOR kotlin.math  TAG kotlin.math  abs kotlin.math  android kotlin.math  applyNoiseReduction kotlin.math  applySharpeningFilter kotlin.math  arrayOf kotlin.math  bicubicUpscale kotlin.math  
enhanceColors kotlin.math  enhanceEdges kotlin.math  exp kotlin.math  floatArrayOf kotlin.math  
intArrayOf kotlin.math  maxOf kotlin.math  min kotlin.math  minOf kotlin.math  
plusAssign kotlin.math  sqrt kotlin.math  until kotlin.math  withContext kotlin.math  ACCEL_THRESHOLD_HIGH 
kotlin.ranges  ACCEL_THRESHOLD_LOW 
kotlin.ranges  ALL_PERMISSIONS_REQUEST_CODE 
kotlin.ranges   AccelerateDecelerateInterpolator 
kotlin.ranges  ActivityCompat 
kotlin.ranges  ActivityMainBinding 
kotlin.ranges  ActivityManager 
kotlin.ranges  ActivityOnboardingBinding 
kotlin.ranges  ActivityResultContracts 
kotlin.ranges  ActivitySplashBinding 
kotlin.ranges  AlertDialog 
kotlin.ranges  AnimatorSet 
kotlin.ranges  Array 
kotlin.ranges  
AutoFocusMode 
kotlin.ranges  BOUNCE_DURATION 
kotlin.ranges  BRIGHTNESS_INDICATOR_TIMEOUT 
kotlin.ranges  BRIGHTNESS_SENSITIVITY 
kotlin.ranges  Bitmap 
kotlin.ranges  BlurMaskFilter 
kotlin.ranges  BounceInterpolator 
kotlin.ranges  Build 
kotlin.ranges  CAMERA_PERMISSION_REQUEST_CODE 
kotlin.ranges  Camera2CameraInfo 
kotlin.ranges  CameraCharacteristics 
kotlin.ranges  CameraController 
kotlin.ranges  
CameraMode 
kotlin.ranges  CameraSelector 
kotlin.ranges  CameraSettings 
kotlin.ranges  Canvas 
kotlin.ranges  Color 
kotlin.ranges  
ColorDrawable 
kotlin.ranges  ColorMatrix 
kotlin.ranges  ColorMatrixColorFilter 
kotlin.ranges  
ContentValues 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  DATE_FORMAT 
kotlin.ranges  DEFAULT_BRIGHTNESS 
kotlin.ranges  DEFAULT_ZOOM_STEP 
kotlin.ranges  Date 
kotlin.ranges  Debug 
kotlin.ranges  DecelerateInterpolator 
kotlin.ranges  
DeviceInfo 
kotlin.ranges  Dispatchers 
kotlin.ranges  EDGE_THRESHOLD_DP 
kotlin.ranges  Environment 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalCamera2Interop 
kotlin.ranges  
FADE_DURATION 
kotlin.ranges  FILTER_ALPHA_HIGH 
kotlin.ranges  FILTER_ALPHA_LOW 
kotlin.ranges  FOLDER_NAME 
kotlin.ranges  File 
kotlin.ranges  FileOutputStream 
kotlin.ranges  
FilterType 
kotlin.ranges  
FloatArray 
kotlin.ranges  FocusMeteringAction 
kotlin.ranges  GYRO_THRESHOLD_HIGH 
kotlin.ranges  GYRO_THRESHOLD_LOW 
kotlin.ranges  GestureDetector 
kotlin.ranges  GestureType 
kotlin.ranges  HIGH_ZOOM_THRESHOLD 
kotlin.ranges  Handler 
kotlin.ranges  
IMAGE_QUALITY 
kotlin.ranges  IllegalArgumentException 
kotlin.ranges  ImageFilterProcessor 
kotlin.ranges  	ImageView 
kotlin.ranges  IntArray 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  JvmOverloads 
kotlin.ranges  KEY_CURRENT_MODE 
kotlin.ranges  KEY_LAST_BRIGHTNESS 
kotlin.ranges  KEY_LAST_ZOOM_RATIO 
kotlin.ranges  LONG_PRESS_TIMEOUT 
kotlin.ranges  LayoutInflater 
kotlin.ranges  LinearLayout 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  
LowPassFilter 
kotlin.ranges  MAX_BRIGHTNESS 
kotlin.ranges  MAX_ZOOM_RATIO 
kotlin.ranges  MEMORY_CHECK_INTERVAL 
kotlin.ranges  MEMORY_WARNING_THRESHOLD 
kotlin.ranges  MIN_BRIGHTNESS 
kotlin.ranges  MIN_SWIPE_DISTANCE 
kotlin.ranges  MIN_ZOOM_RATIO 
kotlin.ranges  MainActivity 
kotlin.ranges  Manifest 
kotlin.ranges  Math 
kotlin.ranges  
MediaStore 
kotlin.ranges  ModeConfiguration 
kotlin.ranges  	ModelType 
kotlin.ranges  MotionEvent 
kotlin.ranges  ObjectAnimator 
kotlin.ranges  Observer 
kotlin.ranges  OnboardingAdapter 
kotlin.ranges  OnboardingAnimator 
kotlin.ranges  OnboardingPage 
kotlin.ranges  OnboardingViewHolder 
kotlin.ranges  OvershootInterpolator 
kotlin.ranges  
PREFS_NAME 
kotlin.ranges  PackageManager 
kotlin.ranges  Paint 
kotlin.ranges  Pair 
kotlin.ranges  Path 
kotlin.ranges  PerformanceReport 
kotlin.ranges  
PorterDuff 
kotlin.ranges  PorterDuffXfermode 
kotlin.ranges  Preview 
kotlin.ranges  ProcessCameraProvider 
kotlin.ranges  R 
kotlin.ranges  RadialGradient 
kotlin.ranges  Result 
kotlin.ranges  Runnable 
kotlin.ranges  SCALE_DURATION 
kotlin.ranges  SCALE_FACTOR 
kotlin.ranges  SENSOR_DELAY 
kotlin.ranges  SLIDE_DURATION 
kotlin.ranges  SPLASH_DELAY 
kotlin.ranges  STORAGE_PERMISSION_REQUEST_CODE 
kotlin.ranges  SaveStatistics 
kotlin.ranges  ScaleGestureDetector 
kotlin.ranges  Sensor 
kotlin.ranges  
SensorManager 
kotlin.ranges  Settings 
kotlin.ranges  Shader 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  #SurfaceOrientedMeteringPointFactory 
kotlin.ranges  System 
kotlin.ranges  TAG 
kotlin.ranges  Toast 
kotlin.ranges  TransitionDirection 
kotlin.ranges  Triple 
kotlin.ranges  Uri 
kotlin.ranges  
ValueAnimator 
kotlin.ranges  View 
kotlin.ranges  	ViewGroup 
kotlin.ranges  
WindowManager 
kotlin.ranges  ZOOM_ANIMATION_DURATION 
kotlin.ranges  abs 
kotlin.ranges  activity 
kotlin.ranges  addListener 
kotlin.ranges  aiStyleEdgeEnhancement 
kotlin.ranges  all 
kotlin.ranges  also 
kotlin.ranges  android 
kotlin.ranges  apply 
kotlin.ranges  applyNoiseReduction 
kotlin.ranges  applySharpeningFilter 
kotlin.ranges  arrayOf 
kotlin.ranges  average 
kotlin.ranges  bicubicUpscale 
kotlin.ranges  camera 
kotlin.ranges  cancelLongPress 
kotlin.ranges  checkMemoryUsage 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  coerceIn 
kotlin.ranges  !colorEnhancementAndDetailRecovery 
kotlin.ranges  contains 
kotlin.ranges  context 
kotlin.ranges  count 
kotlin.ranges  currentPage 
kotlin.ranges  deepSharpening 
kotlin.ranges  delay 
kotlin.ranges  endsWith 
kotlin.ranges  
enhanceColors 
kotlin.ranges  enhanceEdges 
kotlin.ranges  equals 
kotlin.ranges  floatArrayOf 
kotlin.ranges  forEach 
kotlin.ranges  format 
kotlin.ranges  generateFileName 
kotlin.ranges  gestureListener 
kotlin.ranges  handler 
kotlin.ranges  indexOf 
kotlin.ranges  indices 
kotlin.ranges  
intArrayOf 
kotlin.ranges  intelligentDenoising 
kotlin.ranges  isEdgeSwipeActive 
kotlin.ranges  
isInitialized 
kotlin.ranges  isLongPressTriggered 
kotlin.ranges  isMonitoring 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  java 
kotlin.ranges  kotlin 
kotlin.ranges  kotlinx 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  mapOf 
kotlin.ranges  maxByOrNull 
kotlin.ranges  maxOf 
kotlin.ranges  minByOrNull 
kotlin.ranges  minOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  mutableMapOf 
kotlin.ranges  navigateToMain 
kotlin.ranges  
plusAssign 
kotlin.ranges  saveToExternalStorage 
kotlin.ranges  saveToMediaStore 
kotlin.ranges  set 
kotlin.ranges  smartUpscale 
kotlin.ranges  sqrt 
kotlin.ranges  to 
kotlin.ranges  toTypedArray 
kotlin.ranges  triggerAutoFocus 
kotlin.ranges  until 
kotlin.ranges  
updateButtons 
kotlin.ranges  updateIndicators 
kotlin.ranges  use 
kotlin.ranges  withContext 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  getISInitialized  kotlin.reflect.KMutableProperty0  getIsInitialized  kotlin.reflect.KMutableProperty0  
isInitialized  kotlin.reflect.KMutableProperty0  ACCEL_THRESHOLD_HIGH kotlin.sequences  ACCEL_THRESHOLD_LOW kotlin.sequences  ALL_PERMISSIONS_REQUEST_CODE kotlin.sequences   AccelerateDecelerateInterpolator kotlin.sequences  ActivityCompat kotlin.sequences  ActivityMainBinding kotlin.sequences  ActivityManager kotlin.sequences  ActivityOnboardingBinding kotlin.sequences  ActivityResultContracts kotlin.sequences  ActivitySplashBinding kotlin.sequences  AlertDialog kotlin.sequences  AnimatorSet kotlin.sequences  Array kotlin.sequences  
AutoFocusMode kotlin.sequences  BOUNCE_DURATION kotlin.sequences  BRIGHTNESS_INDICATOR_TIMEOUT kotlin.sequences  BRIGHTNESS_SENSITIVITY kotlin.sequences  Bitmap kotlin.sequences  BlurMaskFilter kotlin.sequences  BounceInterpolator kotlin.sequences  Build kotlin.sequences  CAMERA_PERMISSION_REQUEST_CODE kotlin.sequences  Camera2CameraInfo kotlin.sequences  CameraCharacteristics kotlin.sequences  CameraController kotlin.sequences  
CameraMode kotlin.sequences  CameraSelector kotlin.sequences  CameraSettings kotlin.sequences  Canvas kotlin.sequences  Color kotlin.sequences  
ColorDrawable kotlin.sequences  ColorMatrix kotlin.sequences  ColorMatrixColorFilter kotlin.sequences  
ContentValues kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  DATE_FORMAT kotlin.sequences  DEFAULT_BRIGHTNESS kotlin.sequences  DEFAULT_ZOOM_STEP kotlin.sequences  Date kotlin.sequences  Debug kotlin.sequences  DecelerateInterpolator kotlin.sequences  
DeviceInfo kotlin.sequences  Dispatchers kotlin.sequences  EDGE_THRESHOLD_DP kotlin.sequences  Environment kotlin.sequences  	Exception kotlin.sequences  ExperimentalCamera2Interop kotlin.sequences  
FADE_DURATION kotlin.sequences  FILTER_ALPHA_HIGH kotlin.sequences  FILTER_ALPHA_LOW kotlin.sequences  FOLDER_NAME kotlin.sequences  File kotlin.sequences  FileOutputStream kotlin.sequences  
FilterType kotlin.sequences  
FloatArray kotlin.sequences  FocusMeteringAction kotlin.sequences  GYRO_THRESHOLD_HIGH kotlin.sequences  GYRO_THRESHOLD_LOW kotlin.sequences  GestureDetector kotlin.sequences  GestureType kotlin.sequences  HIGH_ZOOM_THRESHOLD kotlin.sequences  Handler kotlin.sequences  
IMAGE_QUALITY kotlin.sequences  IllegalArgumentException kotlin.sequences  ImageFilterProcessor kotlin.sequences  	ImageView kotlin.sequences  IntArray kotlin.sequences  Intent kotlin.sequences  JvmOverloads kotlin.sequences  KEY_CURRENT_MODE kotlin.sequences  KEY_LAST_BRIGHTNESS kotlin.sequences  KEY_LAST_ZOOM_RATIO kotlin.sequences  LONG_PRESS_TIMEOUT kotlin.sequences  LayoutInflater kotlin.sequences  LinearLayout kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  
LowPassFilter kotlin.sequences  MAX_BRIGHTNESS kotlin.sequences  MAX_ZOOM_RATIO kotlin.sequences  MEMORY_CHECK_INTERVAL kotlin.sequences  MEMORY_WARNING_THRESHOLD kotlin.sequences  MIN_BRIGHTNESS kotlin.sequences  MIN_SWIPE_DISTANCE kotlin.sequences  MIN_ZOOM_RATIO kotlin.sequences  MainActivity kotlin.sequences  Manifest kotlin.sequences  Math kotlin.sequences  
MediaStore kotlin.sequences  ModeConfiguration kotlin.sequences  	ModelType kotlin.sequences  MotionEvent kotlin.sequences  ObjectAnimator kotlin.sequences  Observer kotlin.sequences  OnboardingAdapter kotlin.sequences  OnboardingAnimator kotlin.sequences  OnboardingPage kotlin.sequences  OnboardingViewHolder kotlin.sequences  OvershootInterpolator kotlin.sequences  
PREFS_NAME kotlin.sequences  PackageManager kotlin.sequences  Paint kotlin.sequences  Pair kotlin.sequences  Path kotlin.sequences  PerformanceReport kotlin.sequences  
PorterDuff kotlin.sequences  PorterDuffXfermode kotlin.sequences  Preview kotlin.sequences  ProcessCameraProvider kotlin.sequences  R kotlin.sequences  RadialGradient kotlin.sequences  Result kotlin.sequences  Runnable kotlin.sequences  SCALE_DURATION kotlin.sequences  SCALE_FACTOR kotlin.sequences  SENSOR_DELAY kotlin.sequences  SLIDE_DURATION kotlin.sequences  SPLASH_DELAY kotlin.sequences  STORAGE_PERMISSION_REQUEST_CODE kotlin.sequences  SaveStatistics kotlin.sequences  ScaleGestureDetector kotlin.sequences  Sensor kotlin.sequences  
SensorManager kotlin.sequences  Settings kotlin.sequences  Shader kotlin.sequences  SimpleDateFormat kotlin.sequences  #SurfaceOrientedMeteringPointFactory kotlin.sequences  System kotlin.sequences  TAG kotlin.sequences  Toast kotlin.sequences  TransitionDirection kotlin.sequences  Triple kotlin.sequences  Uri kotlin.sequences  
ValueAnimator kotlin.sequences  View kotlin.sequences  	ViewGroup kotlin.sequences  
WindowManager kotlin.sequences  ZOOM_ANIMATION_DURATION kotlin.sequences  abs kotlin.sequences  activity kotlin.sequences  addListener kotlin.sequences  aiStyleEdgeEnhancement kotlin.sequences  all kotlin.sequences  also kotlin.sequences  android kotlin.sequences  apply kotlin.sequences  applyNoiseReduction kotlin.sequences  applySharpeningFilter kotlin.sequences  arrayOf kotlin.sequences  average kotlin.sequences  bicubicUpscale kotlin.sequences  camera kotlin.sequences  cancelLongPress kotlin.sequences  checkMemoryUsage kotlin.sequences  
coerceAtLeast kotlin.sequences  coerceAtMost kotlin.sequences  coerceIn kotlin.sequences  !colorEnhancementAndDetailRecovery kotlin.sequences  contains kotlin.sequences  context kotlin.sequences  count kotlin.sequences  currentPage kotlin.sequences  deepSharpening kotlin.sequences  delay kotlin.sequences  endsWith kotlin.sequences  
enhanceColors kotlin.sequences  enhanceEdges kotlin.sequences  equals kotlin.sequences  floatArrayOf kotlin.sequences  forEach kotlin.sequences  format kotlin.sequences  generateFileName kotlin.sequences  gestureListener kotlin.sequences  handler kotlin.sequences  indexOf kotlin.sequences  indices kotlin.sequences  
intArrayOf kotlin.sequences  intelligentDenoising kotlin.sequences  isEdgeSwipeActive kotlin.sequences  
isInitialized kotlin.sequences  isLongPressTriggered kotlin.sequences  isMonitoring kotlin.sequences  
isNotEmpty kotlin.sequences  java kotlin.sequences  kotlin kotlin.sequences  kotlinx kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  mapOf kotlin.sequences  maxByOrNull kotlin.sequences  maxOf kotlin.sequences  minByOrNull kotlin.sequences  minOf kotlin.sequences  
mutableListOf kotlin.sequences  mutableMapOf kotlin.sequences  navigateToMain kotlin.sequences  
plusAssign kotlin.sequences  saveToExternalStorage kotlin.sequences  saveToMediaStore kotlin.sequences  set kotlin.sequences  smartUpscale kotlin.sequences  sqrt kotlin.sequences  to kotlin.sequences  toTypedArray kotlin.sequences  triggerAutoFocus kotlin.sequences  until kotlin.sequences  
updateButtons kotlin.sequences  updateIndicators kotlin.sequences  use kotlin.sequences  withContext kotlin.sequences  ACCEL_THRESHOLD_HIGH kotlin.text  ACCEL_THRESHOLD_LOW kotlin.text  ALL_PERMISSIONS_REQUEST_CODE kotlin.text   AccelerateDecelerateInterpolator kotlin.text  ActivityCompat kotlin.text  ActivityMainBinding kotlin.text  ActivityManager kotlin.text  ActivityOnboardingBinding kotlin.text  ActivityResultContracts kotlin.text  ActivitySplashBinding kotlin.text  AlertDialog kotlin.text  AnimatorSet kotlin.text  Array kotlin.text  
AutoFocusMode kotlin.text  BOUNCE_DURATION kotlin.text  BRIGHTNESS_INDICATOR_TIMEOUT kotlin.text  BRIGHTNESS_SENSITIVITY kotlin.text  Bitmap kotlin.text  BlurMaskFilter kotlin.text  BounceInterpolator kotlin.text  Build kotlin.text  CAMERA_PERMISSION_REQUEST_CODE kotlin.text  Camera2CameraInfo kotlin.text  CameraCharacteristics kotlin.text  CameraController kotlin.text  
CameraMode kotlin.text  CameraSelector kotlin.text  CameraSettings kotlin.text  Canvas kotlin.text  Color kotlin.text  
ColorDrawable kotlin.text  ColorMatrix kotlin.text  ColorMatrixColorFilter kotlin.text  
ContentValues kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  DATE_FORMAT kotlin.text  DEFAULT_BRIGHTNESS kotlin.text  DEFAULT_ZOOM_STEP kotlin.text  Date kotlin.text  Debug kotlin.text  DecelerateInterpolator kotlin.text  
DeviceInfo kotlin.text  Dispatchers kotlin.text  EDGE_THRESHOLD_DP kotlin.text  Environment kotlin.text  	Exception kotlin.text  ExperimentalCamera2Interop kotlin.text  
FADE_DURATION kotlin.text  FILTER_ALPHA_HIGH kotlin.text  FILTER_ALPHA_LOW kotlin.text  FOLDER_NAME kotlin.text  File kotlin.text  FileOutputStream kotlin.text  
FilterType kotlin.text  
FloatArray kotlin.text  FocusMeteringAction kotlin.text  GYRO_THRESHOLD_HIGH kotlin.text  GYRO_THRESHOLD_LOW kotlin.text  GestureDetector kotlin.text  GestureType kotlin.text  HIGH_ZOOM_THRESHOLD kotlin.text  Handler kotlin.text  
IMAGE_QUALITY kotlin.text  IllegalArgumentException kotlin.text  ImageFilterProcessor kotlin.text  	ImageView kotlin.text  IntArray kotlin.text  Intent kotlin.text  JvmOverloads kotlin.text  KEY_CURRENT_MODE kotlin.text  KEY_LAST_BRIGHTNESS kotlin.text  KEY_LAST_ZOOM_RATIO kotlin.text  LONG_PRESS_TIMEOUT kotlin.text  LayoutInflater kotlin.text  LinearLayout kotlin.text  Locale kotlin.text  Log kotlin.text  Looper kotlin.text  
LowPassFilter kotlin.text  MAX_BRIGHTNESS kotlin.text  MAX_ZOOM_RATIO kotlin.text  MEMORY_CHECK_INTERVAL kotlin.text  MEMORY_WARNING_THRESHOLD kotlin.text  MIN_BRIGHTNESS kotlin.text  MIN_SWIPE_DISTANCE kotlin.text  MIN_ZOOM_RATIO kotlin.text  MainActivity kotlin.text  Manifest kotlin.text  Math kotlin.text  
MediaStore kotlin.text  ModeConfiguration kotlin.text  	ModelType kotlin.text  MotionEvent kotlin.text  ObjectAnimator kotlin.text  Observer kotlin.text  OnboardingAdapter kotlin.text  OnboardingAnimator kotlin.text  OnboardingPage kotlin.text  OnboardingViewHolder kotlin.text  OvershootInterpolator kotlin.text  
PREFS_NAME kotlin.text  PackageManager kotlin.text  Paint kotlin.text  Pair kotlin.text  Path kotlin.text  PerformanceReport kotlin.text  
PorterDuff kotlin.text  PorterDuffXfermode kotlin.text  Preview kotlin.text  ProcessCameraProvider kotlin.text  R kotlin.text  RadialGradient kotlin.text  Result kotlin.text  Runnable kotlin.text  SCALE_DURATION kotlin.text  SCALE_FACTOR kotlin.text  SENSOR_DELAY kotlin.text  SLIDE_DURATION kotlin.text  SPLASH_DELAY kotlin.text  STORAGE_PERMISSION_REQUEST_CODE kotlin.text  SaveStatistics kotlin.text  ScaleGestureDetector kotlin.text  Sensor kotlin.text  
SensorManager kotlin.text  Settings kotlin.text  Shader kotlin.text  SimpleDateFormat kotlin.text  #SurfaceOrientedMeteringPointFactory kotlin.text  System kotlin.text  TAG kotlin.text  Toast kotlin.text  TransitionDirection kotlin.text  Triple kotlin.text  Uri kotlin.text  
ValueAnimator kotlin.text  View kotlin.text  	ViewGroup kotlin.text  
WindowManager kotlin.text  ZOOM_ANIMATION_DURATION kotlin.text  abs kotlin.text  activity kotlin.text  addListener kotlin.text  aiStyleEdgeEnhancement kotlin.text  all kotlin.text  also kotlin.text  android kotlin.text  apply kotlin.text  applyNoiseReduction kotlin.text  applySharpeningFilter kotlin.text  arrayOf kotlin.text  average kotlin.text  bicubicUpscale kotlin.text  camera kotlin.text  cancelLongPress kotlin.text  checkMemoryUsage kotlin.text  
coerceAtLeast kotlin.text  coerceAtMost kotlin.text  coerceIn kotlin.text  !colorEnhancementAndDetailRecovery kotlin.text  contains kotlin.text  context kotlin.text  count kotlin.text  currentPage kotlin.text  deepSharpening kotlin.text  delay kotlin.text  endsWith kotlin.text  
enhanceColors kotlin.text  enhanceEdges kotlin.text  equals kotlin.text  floatArrayOf kotlin.text  forEach kotlin.text  format kotlin.text  generateFileName kotlin.text  gestureListener kotlin.text  handler kotlin.text  indexOf kotlin.text  indices kotlin.text  
intArrayOf kotlin.text  intelligentDenoising kotlin.text  isEdgeSwipeActive kotlin.text  
isInitialized kotlin.text  isLongPressTriggered kotlin.text  isMonitoring kotlin.text  
isNotEmpty kotlin.text  java kotlin.text  kotlin kotlin.text  kotlinx kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  mapOf kotlin.text  maxByOrNull kotlin.text  maxOf kotlin.text  minByOrNull kotlin.text  minOf kotlin.text  
mutableListOf kotlin.text  mutableMapOf kotlin.text  navigateToMain kotlin.text  
plusAssign kotlin.text  saveToExternalStorage kotlin.text  saveToMediaStore kotlin.text  set kotlin.text  smartUpscale kotlin.text  sqrt kotlin.text  to kotlin.text  toTypedArray kotlin.text  triggerAutoFocus kotlin.text  until kotlin.text  
updateButtons kotlin.text  updateIndicators kotlin.text  use kotlin.text  withContext kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Bitmap !kotlinx.coroutines.CoroutineScope  Build !kotlinx.coroutines.CoroutineScope  
ContentValues !kotlinx.coroutines.CoroutineScope  Environment !kotlinx.coroutines.CoroutineScope  	Exception !kotlinx.coroutines.CoroutineScope  FOLDER_NAME !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  FileOutputStream !kotlinx.coroutines.CoroutineScope  
IMAGE_QUALITY !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  
MediaStore !kotlinx.coroutines.CoroutineScope  Pair !kotlinx.coroutines.CoroutineScope  Result !kotlinx.coroutines.CoroutineScope  SCALE_FACTOR !kotlinx.coroutines.CoroutineScope  SPLASH_DELAY !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  aiStyleEdgeEnhancement !kotlinx.coroutines.CoroutineScope  apply !kotlinx.coroutines.CoroutineScope  applyNoiseReduction !kotlinx.coroutines.CoroutineScope  applySharpeningFilter !kotlinx.coroutines.CoroutineScope  bicubicUpscale !kotlinx.coroutines.CoroutineScope  !colorEnhancementAndDetailRecovery !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  deepSharpening !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  
enhanceColors !kotlinx.coroutines.CoroutineScope  enhanceEdges !kotlinx.coroutines.CoroutineScope  generateFileName !kotlinx.coroutines.CoroutineScope  getAIStyleEdgeEnhancement !kotlinx.coroutines.CoroutineScope  getAPPLY !kotlinx.coroutines.CoroutineScope  getAPPLYNoiseReduction !kotlinx.coroutines.CoroutineScope  getAPPLYSharpeningFilter !kotlinx.coroutines.CoroutineScope  getAiStyleEdgeEnhancement !kotlinx.coroutines.CoroutineScope  getApply !kotlinx.coroutines.CoroutineScope  getApplyNoiseReduction !kotlinx.coroutines.CoroutineScope  getApplySharpeningFilter !kotlinx.coroutines.CoroutineScope  getBICUBICUpscale !kotlinx.coroutines.CoroutineScope  getBicubicUpscale !kotlinx.coroutines.CoroutineScope  $getCOLOREnhancementAndDetailRecovery !kotlinx.coroutines.CoroutineScope  
getCONTEXT !kotlinx.coroutines.CoroutineScope  $getColorEnhancementAndDetailRecovery !kotlinx.coroutines.CoroutineScope  
getContext !kotlinx.coroutines.CoroutineScope  getDEEPSharpening !kotlinx.coroutines.CoroutineScope  getDELAY !kotlinx.coroutines.CoroutineScope  getDeepSharpening !kotlinx.coroutines.CoroutineScope  getDelay !kotlinx.coroutines.CoroutineScope  getENHANCEColors !kotlinx.coroutines.CoroutineScope  getENHANCEEdges !kotlinx.coroutines.CoroutineScope  getEnhanceColors !kotlinx.coroutines.CoroutineScope  getEnhanceEdges !kotlinx.coroutines.CoroutineScope  getGENERATEFileName !kotlinx.coroutines.CoroutineScope  getGenerateFileName !kotlinx.coroutines.CoroutineScope  getINTELLIGENTDenoising !kotlinx.coroutines.CoroutineScope  getISInitialized !kotlinx.coroutines.CoroutineScope  getIntelligentDenoising !kotlinx.coroutines.CoroutineScope  getIsInitialized !kotlinx.coroutines.CoroutineScope  
getKOTLINX !kotlinx.coroutines.CoroutineScope  
getKotlinx !kotlinx.coroutines.CoroutineScope  getNAVIGATEToMain !kotlinx.coroutines.CoroutineScope  getNavigateToMain !kotlinx.coroutines.CoroutineScope  getSAVEToExternalStorage !kotlinx.coroutines.CoroutineScope  getSAVEToMediaStore !kotlinx.coroutines.CoroutineScope  getSMARTUpscale !kotlinx.coroutines.CoroutineScope  getSaveToExternalStorage !kotlinx.coroutines.CoroutineScope  getSaveToMediaStore !kotlinx.coroutines.CoroutineScope  getSmartUpscale !kotlinx.coroutines.CoroutineScope  getUSE !kotlinx.coroutines.CoroutineScope  getUse !kotlinx.coroutines.CoroutineScope  intelligentDenoising !kotlinx.coroutines.CoroutineScope  
isInitialized !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  navigateToMain !kotlinx.coroutines.CoroutineScope  saveToExternalStorage !kotlinx.coroutines.CoroutineScope  saveToMediaStore !kotlinx.coroutines.CoroutineScope  smartUpscale !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  Default kotlinx.coroutines.Dispatchers  IO kotlinx.coroutines.Dispatchers                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               