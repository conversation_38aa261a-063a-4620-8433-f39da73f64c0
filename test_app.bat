@echo off
echo ========================================
echo 放大镜APP测试报告
echo ========================================
echo.

set ADB=C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe

echo 1. 检查设备连接...
%ADB% devices
echo.

echo 2. 检查应用安装状态...
%ADB% shell pm list packages | findstr magnifying
if %ERRORLEVEL% EQU 0 (
    echo ✓ 应用已成功安装
) else (
    echo ✗ 应用未安装
    goto :end
)
echo.

echo 3. 检查APK信息...
for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do (
    set size=%%~zA
    set /a sizeMB=!size!/1024/1024
    echo APK大小: !sizeMB!MB
)
echo.

echo 4. 检查应用权限...
%ADB% shell dumpsys package com.magnifyingglass.app | findstr "android.permission.CAMERA"
echo.

echo 5. 启动应用...
%ADB% shell am start -n com.magnifyingglass.app/.MainActivity
if %ERRORLEVEL% EQU 0 (
    echo ✓ 应用启动成功
) else (
    echo ✗ 应用启动失败
)
echo.

echo 6. 等待3秒检查应用状态...
timeout /t 3 /nobreak > nul

echo 7. 检查应用进程...
%ADB% shell "ps -A | grep magnifying"
echo.

echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 手动测试项目：
echo □ 1. 应用是否正常启动并显示相机预览
echo □ 2. 双指捏合是否可以缩放
echo □ 3. 双击是否可以快速切换倍数
echo □ 4. 长按是否可以冻结画面
echo □ 5. 屏幕边缘滑动是否可以调节亮度
echo □ 6. 底部按钮是否响应正常
echo □ 7. 模式切换是否工作
echo □ 8. 权限请求是否正常显示
echo.

:end
pause
