package com.magnifyingglass.app.utils

import android.content.Context
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraManager
import android.util.Log
import androidx.camera.camera2.interop.Camera2CameraInfo
import androidx.camera.core.Camera
import androidx.camera.core.CameraInfo
import androidx.camera.core.CameraSelector
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat

class CameraController(private val context: Context) {
    
    private val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
    
    companion object {
        private const val TAG = "CameraController"
        const val MIN_ZOOM_RATIO = 1.0f
        const val MAX_ZOOM_RATIO = 16.0f
    }
    
    /**
     * 检查设备是否支持手动对焦
     */
    fun supportsManualFocus(camera: Camera): Boolean {
        return try {
            val camera2Info = Camera2CameraInfo.from(camera.cameraInfo)
            val cameraId = camera2Info.cameraId
            val characteristics = cameraManager.getCameraCharacteristics(cameraId)
            
            val focusDistanceRange = characteristics.get(CameraCharacteristics.LENS_INFO_MINIMUM_FOCUS_DISTANCE)
            focusDistanceRange != null && focusDistanceRange > 0
        } catch (e: Exception) {
            Log.w(TAG, "Failed to check manual focus support", e)
            false
        }
    }
    
    /**
     * 检查设备是否支持硬件防抖
     */
    fun supportsOpticalStabilization(camera: Camera): Boolean {
        return try {
            val camera2Info = Camera2CameraInfo.from(camera.cameraInfo)
            val cameraId = camera2Info.cameraId
            val characteristics = cameraManager.getCameraCharacteristics(cameraId)
            
            val availableStabilization = characteristics.get(
                CameraCharacteristics.LENS_INFO_AVAILABLE_OPTICAL_STABILIZATION
            )
            
            availableStabilization?.contains(CameraCharacteristics.LENS_OPTICAL_STABILIZATION_MODE_ON) == true
        } catch (e: Exception) {
            Log.w(TAG, "Failed to check optical stabilization support", e)
            false
        }
    }
    
    /**
     * 检查设备是否支持视频防抖
     */
    fun supportsVideoStabilization(camera: Camera): Boolean {
        return try {
            val camera2Info = Camera2CameraInfo.from(camera.cameraInfo)
            val cameraId = camera2Info.cameraId
            val characteristics = cameraManager.getCameraCharacteristics(cameraId)
            
            val availableStabilization = characteristics.get(
                CameraCharacteristics.CONTROL_AVAILABLE_VIDEO_STABILIZATION_MODES
            )
            
            availableStabilization?.contains(CameraCharacteristics.CONTROL_VIDEO_STABILIZATION_MODE_ON) == true
        } catch (e: Exception) {
            Log.w(TAG, "Failed to check video stabilization support", e)
            false
        }
    }
    
    /**
     * 获取相机支持的最大变焦比例
     */
    fun getMaxZoomRatio(camera: Camera): Float {
        return try {
            val zoomState = camera.cameraInfo.zoomState.value
            zoomState?.maxZoomRatio ?: MAX_ZOOM_RATIO
        } catch (e: Exception) {
            Log.w(TAG, "Failed to get max zoom ratio", e)
            MAX_ZOOM_RATIO
        }
    }
    
    /**
     * 获取相机支持的最小变焦比例
     */
    fun getMinZoomRatio(camera: Camera): Float {
        return try {
            val zoomState = camera.cameraInfo.zoomState.value
            zoomState?.minZoomRatio ?: MIN_ZOOM_RATIO
        } catch (e: Exception) {
            Log.w(TAG, "Failed to get min zoom ratio", e)
            MIN_ZOOM_RATIO
        }
    }
    
    /**
     * 检查设备是否支持闪光灯
     */
    fun hasFlashlight(camera: Camera): Boolean {
        return camera.cameraInfo.hasFlashUnit()
    }
    
    /**
     * 获取相机的曝光补偿范围
     */
    fun getExposureCompensationRange(camera: Camera): Pair<Int, Int> {
        return try {
            val exposureState = camera.cameraInfo.exposureState
            Pair(exposureState.exposureCompensationRange.lower, exposureState.exposureCompensationRange.upper)
        } catch (e: Exception) {
            Log.w(TAG, "Failed to get exposure compensation range", e)
            Pair(-2, 2) // 默认范围
        }
    }
    
    /**
     * 检查设备是否支持自动对焦
     */
    fun supportsAutoFocus(camera: Camera): Boolean {
        return try {
            val camera2Info = Camera2CameraInfo.from(camera.cameraInfo)
            val cameraId = camera2Info.cameraId
            val characteristics = cameraManager.getCameraCharacteristics(cameraId)
            
            val afModes = characteristics.get(CameraCharacteristics.CONTROL_AF_AVAILABLE_MODES)
            afModes?.contains(CameraCharacteristics.CONTROL_AF_MODE_CONTINUOUS_VIDEO) == true ||
            afModes?.contains(CameraCharacteristics.CONTROL_AF_MODE_AUTO) == true
        } catch (e: Exception) {
            Log.w(TAG, "Failed to check auto focus support", e)
            true // 大多数设备都支持自动对焦
        }
    }
    
    /**
     * 获取设备品牌和型号信息
     */
    fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            manufacturer = android.os.Build.MANUFACTURER,
            model = android.os.Build.MODEL,
            brand = android.os.Build.BRAND
        )
    }
    
    /**
     * 根据设备信息获取优化参数
     */
    fun getOptimizedSettings(deviceInfo: DeviceInfo): CameraSettings {
        return when {
            deviceInfo.manufacturer.equals("huawei", true) -> {
                // 华为设备优化
                CameraSettings(
                    focusTimeout = 1000L,
                    preferredZoomStep = 0.3f,
                    enableMacroMode = true
                )
            }
            deviceInfo.manufacturer.equals("xiaomi", true) -> {
                // 小米设备优化
                CameraSettings(
                    focusTimeout = 800L,
                    preferredZoomStep = 0.5f,
                    flashlightIntensity = 0.8f
                )
            }
            deviceInfo.manufacturer.equals("samsung", true) -> {
                // 三星设备优化
                CameraSettings(
                    focusTimeout = 600L,
                    preferredZoomStep = 0.4f,
                    enableAdvancedStabilization = true
                )
            }
            else -> {
                // 默认设置
                CameraSettings()
            }
        }
    }
    
    data class DeviceInfo(
        val manufacturer: String,
        val model: String,
        val brand: String
    )
    
    data class CameraSettings(
        val focusTimeout: Long = 800L,
        val preferredZoomStep: Float = 0.5f,
        val enableMacroMode: Boolean = false,
        val flashlightIntensity: Float = 1.0f,
        val enableAdvancedStabilization: Boolean = false
    )
}
