package com.magnifyingglass.app.utils

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import kotlin.math.abs

class GestureHandler(
    private val context: Context,
    private val gestureListener: GestureListener
) {
    private var isEnabled = true
    
    companion object {
        private const val EDGE_THRESHOLD_DP = 50 // 边缘检测阈值（dp）
        private const val LONG_PRESS_TIMEOUT = 500L // 长按超时时间
        private const val BRIGHTNESS_SENSITIVITY = 3.0f // 亮度调节灵敏度
        private const val MIN_SWIPE_DISTANCE = 20 // 最小滑动距离
    }
    
    interface GestureListener {
        fun onZoomGesture(scaleFactor: Float)
        fun onDoubleTap()
        fun onLongPress()
        fun onSingleTap(x: Float, y: Float)
        fun onBrightnessAdjust(delta: Int)
        fun onEdgeSwipeStart()
        fun onEdgeSwipeEnd()
    }
    
    private val scaleGestureDetector: ScaleGestureDetector
    private val gestureDetector: GestureDetector
    private val edgeThresholdPx: Float
    private val handler = Handler(Looper.getMainLooper())
    
    // 边缘滑动状态
    private var isEdgeSwipeActive = false
    private var edgeSwipeStartY = 0f
    private var lastBrightnessY = 0f
    
    // 长按状态
    private var longPressRunnable: Runnable? = null
    private var isLongPressTriggered = false
    private var touchStartTime = 0L
    private var hasMovedDuringTouch = false
    private var touchStartX = 0f
    private var touchStartY = 0f
    
    init {
        val density = context.resources.displayMetrics.density
        edgeThresholdPx = EDGE_THRESHOLD_DP * density
        
        scaleGestureDetector = ScaleGestureDetector(context, ScaleListener())
        gestureDetector = GestureDetector(context, SimpleGestureListener())
    }
    
    fun setEnabled(enabled: Boolean) {
        isEnabled = enabled
        if (!enabled) {
            // 禁用时取消所有正在进行的手势
            cancelLongPress()
            isEdgeSwipeActive = false
        }
    }

    fun onTouchEvent(event: MotionEvent, view: View): Boolean {
        if (!isEnabled) {
            return false
        }

        var handled = false

        // 处理缩放手势
        handled = scaleGestureDetector.onTouchEvent(event) || handled

        // 如果不是缩放手势，处理其他手势
        if (!scaleGestureDetector.isInProgress) {
            handled = gestureDetector.onTouchEvent(event) || handled
            handled = handleEdgeSwipe(event, view) || handled
            handled = handleLongPress(event) || handled
        }

        return handled
    }
    
    private fun handleEdgeSwipe(event: MotionEvent, view: View): Boolean {
        try {
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    val isAtEdge = isAtScreenEdge(event.x, view.width.toFloat())
                    if (isAtEdge) {
                        isEdgeSwipeActive = true
                        edgeSwipeStartY = event.y
                        lastBrightnessY = event.y
                        gestureListener.onEdgeSwipeStart()
                        // 边缘滑动时取消长按检测
                        cancelLongPress()
                        return true
                    }
                }
            
                MotionEvent.ACTION_MOVE -> {
                    if (isEdgeSwipeActive) {
                        val deltaY = lastBrightnessY - event.y
                        if (abs(deltaY) > MIN_SWIPE_DISTANCE) {
                            val brightnessDelta = (deltaY / BRIGHTNESS_SENSITIVITY).toInt()
                            gestureListener.onBrightnessAdjust(brightnessDelta)
                            lastBrightnessY = event.y
                        }
                        return true
                    }
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (isEdgeSwipeActive) {
                        isEdgeSwipeActive = false
                        gestureListener.onEdgeSwipeEnd()
                        return true
                    }
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("GestureHandler", "Error in handleEdgeSwipe", e)
            isEdgeSwipeActive = false
        }

        return false
    }
    
    private fun handleLongPress(event: MotionEvent): Boolean {
        try {
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    if (!isEdgeSwipeActive && !scaleGestureDetector.isInProgress) {
                        // 检查是否在屏幕边缘，如果是则不触发长按
                        val isAtEdge = isAtScreenEdge(event.x, 0f) // 使用0作为宽度参数来检查边缘
                        if (!isAtEdge) {
                            touchStartTime = System.currentTimeMillis()
                            touchStartX = event.x
                            touchStartY = event.y
                            isLongPressTriggered = false
                            hasMovedDuringTouch = false

                            longPressRunnable = Runnable {
                                if (!isLongPressTriggered && !isEdgeSwipeActive && !hasMovedDuringTouch) {
                                    isLongPressTriggered = true
                                    gestureListener.onLongPress()
                                }
                            }

                            handler.postDelayed(longPressRunnable!!, LONG_PRESS_TIMEOUT)
                            return true
                        }
                    }
                }

                MotionEvent.ACTION_MOVE -> {
                    // 检查是否有明显的移动
                    val deltaX = abs(event.x - touchStartX)
                    val deltaY = abs(event.y - touchStartY)
                    if (deltaX > 20 || deltaY > 20) {
                        hasMovedDuringTouch = true
                        cancelLongPress()
                    }
                }

                MotionEvent.ACTION_UP -> {
                    val pressDuration = System.currentTimeMillis() - touchStartTime
                    if (pressDuration < LONG_PRESS_TIMEOUT && !isLongPressTriggered) {
                        cancelLongPress()
                        // 这是一个短按，让其他手势处理器处理
                        return false
                    } else if (isLongPressTriggered) {
                        cancelLongPress()
                        return true
                    }
                }

                MotionEvent.ACTION_CANCEL -> {
                    cancelLongPress()
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("GestureHandler", "Error in handleLongPress", e)
            cancelLongPress()
        }

        return false
    }
    
    private fun cancelLongPress() {
        longPressRunnable?.let { handler.removeCallbacks(it) }
        longPressRunnable = null
        isLongPressTriggered = false
        hasMovedDuringTouch = false
    }
    
    private fun isAtScreenEdge(x: Float, screenWidth: Float): Boolean {
        val actualWidth = if (screenWidth <= 0) {
            context.resources.displayMetrics.widthPixels.toFloat()
        } else {
            screenWidth
        }
        return x < edgeThresholdPx || x > actualWidth - edgeThresholdPx
    }
    
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            gestureListener.onZoomGesture(detector.scaleFactor)
            return true
        }
        
        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            // 开始缩放时取消长按
            cancelLongPress()
            return true
        }
    }
    
    private inner class SimpleGestureListener : GestureDetector.SimpleOnGestureListener() {
        override fun onDoubleTap(e: MotionEvent): Boolean {
            gestureListener.onDoubleTap()
            return true
        }
        
        override fun onSingleTapUp(e: MotionEvent): Boolean {
            // 只有在不是边缘滑动和长按的情况下才处理单击
            if (!isEdgeSwipeActive && !isLongPressTriggered) {
                gestureListener.onSingleTap(e.x, e.y)
                return true
            }
            return false
        }
        
        override fun onDown(e: MotionEvent): Boolean {
            // 总是返回true以接收后续事件
            return true
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        cancelLongPress()
        handler.removeCallbacksAndMessages(null)
    }
}
