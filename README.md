# 放大镜 APP

一款简洁易用的离线放大镜应用，专为老年人和低视力用户设计。

## 功能特点

### 🔍 高清放大
- 2x-16x 连续变焦
- 硬件加速的数字变焦
- 自动对焦和手动对焦支持
- 点击对焦功能

### 🎨 智能滤镜
- **普通模式**: 标准放大显示
- **阅读模式**: 降低饱和度，提高对比度，适合长时间阅读
- **高对比度模式**: 黑底白字显示，适合低视力用户
- **低光模式**: 自动开启闪光灯和最大亮度

### 🤏 直观手势
- 双指捏合缩放
- 双击快速切换1x和当前倍数
- 长按冻结画面
- 屏幕边缘滑动调节亮度

### 📸 截图保存
- 一键冻结当前画面
- 保存放大后的截图到相册
- 支持当前视图区域保存

### ⚡ 性能优化
- 启动时间 < 2秒
- APK大小 < 5MB
- 智能防抖技术
- 内存优化和电池友好

### 🎯 极简设计
- 无广告，无打扰
- 最小权限要求
- 一键模式切换
- 老年人友好的大按钮设计

## 技术架构

### 核心技术
- **相机框架**: CameraX
- **图像处理**: 自定义滤镜引擎
- **手势识别**: 多点触控和传感器融合
- **防抖技术**: 陀螺仪 + 加速度计
- **性能监控**: 实时内存和帧率监控

### 项目结构
```
app/src/main/java/com/magnifyingglass/app/
├── MainActivity.kt              # 主活动
├── OnboardingActivity.kt        # 引导页面
└── utils/
    ├── CameraController.kt      # 相机控制
    ├── ZoomController.kt        # 变焦控制
    ├── ImageFilterProcessor.kt  # 图像滤镜
    ├── GestureHandler.kt        # 手势处理
    ├── BrightnessController.kt  # 亮度控制
    ├── StabilizationController.kt # 防抖控制
    ├── ModeManager.kt           # 模式管理
    ├── ImageSaver.kt            # 图片保存
    ├── PermissionHelper.kt      # 权限管理
    └── PerformanceMonitor.kt    # 性能监控
```

## 使用说明

### 基本操作
1. **放大缩小**: 双指捏合或点击底部 +/- 按钮
2. **快速复位**: 双击屏幕在1x和当前倍数间切换
3. **调节亮度**: 在屏幕左右边缘上下滑动
4. **冻结画面**: 长按屏幕任意位置
5. **保存截图**: 冻结后点击保存按钮
6. **切换模式**: 点击底部模式按钮

### 模式说明
- **普通模式** 📱: 日常使用，平衡的显示效果
- **阅读模式** 📖: 适合阅读文字，降低眼疲劳
- **高对比度** ⚫: 黑底白字，适合低视力用户
- **低光模式** 🔦: 暗环境使用，自动开启闪光灯

### 场景应用
- 📦 查看快递单号和小字
- 💊 阅读药品说明书
- 🕵️‍♂️ 检查商品细节和瑕疵
- 📰 阅读报纸和书籍
- 🔍 观察植物和昆虫细节

## 系统要求

- **Android版本**: 5.0 (API 21) 及以上
- **相机**: 支持自动对焦的后置摄像头
- **存储**: 至少 10MB 可用空间
- **内存**: 建议 1GB 以上 RAM

## 权限说明

应用仅请求必要权限：
- **相机权限**: 用于显示放大画面
- **存储权限**: 用于保存截图（可选）

## 隐私保护

- ✅ 完全离线运行，无需网络连接
- ✅ 不收集任何个人信息
- ✅ 不上传任何图像数据
- ✅ 截图仅保存在本地设备

## 开发构建

### 环境要求
- Android Studio Arctic Fox 或更高版本
- Kotlin 1.9.10
- Gradle 8.1.2
- compileSdk 34

### 构建步骤
```bash
# 克隆项目
git clone https://github.com/your-repo/magnifying-glass.git

# 打开 Android Studio 并导入项目

# 构建 Debug 版本
./gradlew assembleDebug

# 构建 Release 版本
./gradlew assembleRelease

# 运行测试
./gradlew test
```

### 性能优化
- 启用 ProGuard/R8 代码混淆
- 使用 Android App Bundle
- 优化图片资源
- 移除未使用的依赖

## 测试

### 单元测试
```bash
./gradlew test
```

### 设备测试
建议在以下设备上测试：
- 低端设备 (1GB RAM)
- 中端设备 (3GB RAM)
- 高端设备 (6GB+ RAM)
- 不同品牌 (华为、小米、三星等)

## 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发规范
- 遵循 Kotlin 编码规范
- 添加适当的注释和文档
- 编写单元测试
- 保持代码简洁和可读性

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 更新日志

### v1.0.0 (2024-01-01)
- 🎉 首次发布
- ✨ 支持 2x-16x 变焦
- ✨ 四种显示模式
- ✨ 智能手势操作
- ✨ 截图保存功能
- ✨ 性能优化

---

**让放大变得简单，让细节清晰可见** 🔍✨
