<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#FFFF5722</color>
    <color name="active">#FF4CAF50</color>
    <color name="black">#FF000000</color>
    <color name="control_background">#80000000</color>
    <color name="control_background_pressed">#B0000000</color>
    <color name="control_icon">#FFFFFFFF</color>
    <color name="control_text">#FFFFFFFF</color>
    <color name="error">#FFF44336</color>
    <color name="high_contrast_bg">#FF000000</color>
    <color name="high_contrast_fg">#FFFFFFFF</color>
    <color name="inactive">#FF9E9E9E</color>
    <color name="overlay_dark">#80000000</color>
    <color name="overlay_light">#80FFFFFF</color>
    <color name="primary">#FF2196F3</color>
    <color name="primary_dark">#FF1976D2</color>
    <color name="transparent">#00000000</color>
    <color name="warning">#FFFF9800</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="control_button_size">64dp</dimen>
    <dimen name="focus_indicator_size">60dp</dimen>
    <dimen name="small_control_button_size">48dp</dimen>
    <string name="app_name">放大镜</string>
    <string name="back">返回</string>
    <string name="brightness_level">亮度: %d%%</string>
    <string name="camera_error">相机初始化失败</string>
    <string name="camera_not_available">相机不可用</string>
    <string name="dismiss">忽略</string>
    <string name="enable_max_brightness">开启</string>
    <string name="enhance">增强</string>
    <string name="enhance_image">画面增强</string>
    <string name="flashlight">手电筒</string>
    <string name="flashlight_off">手电筒已关闭</string>
    <string name="flashlight_on">手电筒已开启</string>
    <string name="focus_failed">对焦失败</string>
    <string name="frame_frozen">画面已冻结</string>
    <string name="frame_unfrozen">恢复实时画面</string>
    <string name="freeze_frame">冻结</string>
    <string name="high_contrast_mode">高对比度</string>
    <string name="image_save_failed">保存失败</string>
    <string name="image_saved">图片已保存到相册</string>
    <string name="low_light_detected">检测到弱光环境，是否开启最大亮度？</string>
    <string name="low_light_mode">低光模式</string>
    <string name="mode_switch">模式</string>
    <string name="normal_mode">普通模式</string>
    <string name="onboarding_desc_1">双指捏合或双击屏幕可以调节放大倍数</string>
    <string name="onboarding_desc_2">长按屏幕可以冻结当前画面，方便仔细观看</string>
    <string name="onboarding_desc_3">在屏幕边缘上下滑动可以调节画面亮度</string>
    <string name="onboarding_next">下一步</string>
    <string name="onboarding_skip">跳过</string>
    <string name="onboarding_start">开始使用</string>
    <string name="onboarding_title_1">欢迎使用放大镜</string>
    <string name="onboarding_title_2">冻结画面</string>
    <string name="onboarding_title_3">调节亮度</string>
    <string name="permission_camera_message">放大镜需要使用相机来显示放大画面，请授予相机权限。</string>
    <string name="permission_camera_title">需要相机权限</string>
    <string name="permission_cancel">取消</string>
    <string name="permission_denied">权限被拒绝</string>
    <string name="permission_grant">授予权限</string>
    <string name="permission_storage_message">保存截图需要存储权限，请授予存储权限。</string>
    <string name="permission_storage_title">需要存储权限</string>
    <string name="reading_mode">阅读模式</string>
    <string name="save_image">保存</string>
    <string name="scenario_inspection">🕵️‍♂️ 检查商品细节瑕疵</string>
    <string name="scenario_medicine">💊 阅读药品说明书</string>
    <string name="scenario_package">📦 看清快递单上的小字</string>
    <string name="settings">设置</string>
    <string name="zoom_in">放大</string>
    <string name="zoom_level">%dx</string>
    <string name="zoom_out">缩小</string>
    <style name="ControlButton">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:background">@drawable/control_button_background</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:padding">12dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:tint">@color/control_icon</item>
    </style>
    <style name="InfoText">
        <item name="android:textColor">@color/control_text</item>
        <item name="android:textSize">16sp</item>
        <item name="android:background">@color/control_background</item>
        <item name="android:padding">8dp</item>
        <item name="android:layout_margin">4dp</item>
    </style>
    <style name="OnboardingDescription">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:gravity">center</item>
        <item name="android:lineSpacingExtra">4dp</item>
    </style>
    <style name="OnboardingTitle">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/primary</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style>
    <style name="SmallControlButton" parent="ControlButton">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:padding">10dp</item>
        <item name="android:layout_margin">6dp</item>
    </style>
    <style name="SplashTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsTranslucent">false</item>
    </style>
    <style name="Theme.MagnifyingGlass" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">@color/black</item>
        <item name="android:navigationBarColor">@color/black</item>
    </style>
    <style name="Theme.MagnifyingGlass.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>