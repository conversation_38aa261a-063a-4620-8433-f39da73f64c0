// Generated by view binder compiler. Do not edit!
package com.magnifyingglass.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.magnifyingglass.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySplashBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView splashIcon;

  @NonNull
  public final TextView splashSubtitle;

  @NonNull
  public final TextView splashTitle;

  private ActivitySplashBinding(@NonNull LinearLayout rootView, @NonNull ImageView splashIcon,
      @NonNull TextView splashSubtitle, @NonNull TextView splashTitle) {
    this.rootView = rootView;
    this.splashIcon = splashIcon;
    this.splashSubtitle = splashSubtitle;
    this.splashTitle = splashTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_splash, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySplashBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.splash_icon;
      ImageView splashIcon = ViewBindings.findChildViewById(rootView, id);
      if (splashIcon == null) {
        break missingId;
      }

      id = R.id.splash_subtitle;
      TextView splashSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (splashSubtitle == null) {
        break missingId;
      }

      id = R.id.splash_title;
      TextView splashTitle = ViewBindings.findChildViewById(rootView, id);
      if (splashTitle == null) {
        break missingId;
      }

      return new ActivitySplashBinding((LinearLayout) rootView, splashIcon, splashSubtitle,
          splashTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
