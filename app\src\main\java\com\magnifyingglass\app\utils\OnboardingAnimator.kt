package com.magnifyingglass.app.utils

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.BounceInterpolator
import android.view.animation.OvershootInterpolator

class OnboardingAnimator {
    
    companion object {
        private const val FADE_DURATION = 300L
        private const val SCALE_DURATION = 400L
        private const val SLIDE_DURATION = 500L
        private const val BOUNCE_DURATION = 600L
    }
    
    /**
     * 淡入动画
     */
    fun fadeIn(view: View, delay: Long = 0L): ObjectAnimator {
        view.alpha = 0f
        return ObjectAnimator.ofFloat(view, "alpha", 0f, 1f).apply {
            duration = FADE_DURATION
            startDelay = delay
            interpolator = AccelerateDecelerateInterpolator()
        }
    }
    
    /**
     * 缩放进入动画
     */
    fun scaleIn(view: View, delay: Long = 0L): AnimatorSet {
        view.scaleX = 0f
        view.scaleY = 0f
        view.alpha = 0f
        
        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 0f, 1f)
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 0f, 1f)
        val alpha = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        
        return AnimatorSet().apply {
            playTogether(scaleX, scaleY, alpha)
            duration = SCALE_DURATION
            startDelay = delay
            interpolator = OvershootInterpolator()
        }
    }
    
    /**
     * 从下方滑入动画
     */
    fun slideInFromBottom(view: View, delay: Long = 0L): AnimatorSet {
        val originalY = view.translationY
        view.translationY = view.height.toFloat()
        view.alpha = 0f
        
        val translateY = ObjectAnimator.ofFloat(view, "translationY", view.height.toFloat(), originalY)
        val alpha = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        
        return AnimatorSet().apply {
            playTogether(translateY, alpha)
            duration = SLIDE_DURATION
            startDelay = delay
            interpolator = AccelerateDecelerateInterpolator()
        }
    }
    
    /**
     * 弹跳动画
     */
    fun bounce(view: View, delay: Long = 0L): ObjectAnimator {
        return ObjectAnimator.ofFloat(view, "translationY", 0f, -30f, 0f).apply {
            duration = BOUNCE_DURATION
            startDelay = delay
            interpolator = BounceInterpolator()
            repeatCount = 2
        }
    }
    
    /**
     * 脉冲动画（放大缩小）
     */
    fun pulse(view: View, delay: Long = 0L): AnimatorSet {
        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1f, 1.1f, 1f)
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1f, 1.1f, 1f)
        
        return AnimatorSet().apply {
            playTogether(scaleX, scaleY)
            duration = 800L
            startDelay = delay
            interpolator = AccelerateDecelerateInterpolator()
            repeatCount = ValueAnimator.INFINITE
        }
    }
    
    /**
     * 摇摆动画
     */
    fun shake(view: View, delay: Long = 0L): ObjectAnimator {
        return ObjectAnimator.ofFloat(view, "rotation", 0f, -5f, 5f, -5f, 5f, 0f).apply {
            duration = 500L
            startDelay = delay
            interpolator = AccelerateDecelerateInterpolator()
        }
    }
    
    /**
     * 组合动画：图标进入
     */
    fun animateIconEntry(iconView: View): AnimatorSet {
        val scaleIn = scaleIn(iconView, 0L)
        val bounce = bounce(iconView, SCALE_DURATION)
        
        return AnimatorSet().apply {
            playSequentially(scaleIn, bounce)
        }
    }
    
    /**
     * 组合动画：文本进入
     */
    fun animateTextEntry(titleView: View, descView: View): AnimatorSet {
        val titleSlide = slideInFromBottom(titleView, 200L)
        val descSlide = slideInFromBottom(descView, 400L)
        
        return AnimatorSet().apply {
            playTogether(titleSlide, descSlide)
        }
    }
    
    /**
     * 页面切换动画
     */
    fun animatePageTransition(outView: View, inView: View, direction: TransitionDirection) {
        val outAnimator = when (direction) {
            TransitionDirection.NEXT -> {
                ObjectAnimator.ofFloat(outView, "translationX", 0f, -outView.width.toFloat())
            }
            TransitionDirection.PREVIOUS -> {
                ObjectAnimator.ofFloat(outView, "translationX", 0f, outView.width.toFloat())
            }
        }
        
        val inAnimator = when (direction) {
            TransitionDirection.NEXT -> {
                inView.translationX = inView.width.toFloat()
                ObjectAnimator.ofFloat(inView, "translationX", inView.width.toFloat(), 0f)
            }
            TransitionDirection.PREVIOUS -> {
                inView.translationX = -inView.width.toFloat()
                ObjectAnimator.ofFloat(inView, "translationX", -inView.width.toFloat(), 0f)
            }
        }
        
        val fadeOut = ObjectAnimator.ofFloat(outView, "alpha", 1f, 0f)
        val fadeIn = ObjectAnimator.ofFloat(inView, "alpha", 0f, 1f)
        
        AnimatorSet().apply {
            playTogether(outAnimator, inAnimator, fadeOut, fadeIn)
            duration = 300L
            interpolator = AccelerateDecelerateInterpolator()
            start()
        }
    }
    
    /**
     * 手势演示动画
     */
    fun animateGestureDemo(view: View, gestureType: GestureType): AnimatorSet {
        return when (gestureType) {
            GestureType.PINCH_ZOOM -> {
                // 模拟双指缩放手势
                val scale1 = ObjectAnimator.ofFloat(view, "scaleX", 1f, 1.5f, 1f)
                val scale2 = ObjectAnimator.ofFloat(view, "scaleY", 1f, 1.5f, 1f)
                AnimatorSet().apply {
                    playTogether(scale1, scale2)
                    duration = 1000L
                    repeatCount = 2
                }
            }
            GestureType.DOUBLE_TAP -> {
                // 模拟双击手势
                val scale1 = ObjectAnimator.ofFloat(view, "scaleX", 1f, 0.9f, 1f)
                val scale2 = ObjectAnimator.ofFloat(view, "scaleY", 1f, 0.9f, 1f)
                AnimatorSet().apply {
                    playTogether(scale1, scale2)
                    duration = 200L
                    repeatCount = 3
                }
            }
            GestureType.LONG_PRESS -> {
                // 模拟长按手势
                pulse(view, 0L)
            }
            GestureType.EDGE_SWIPE -> {
                // 模拟边缘滑动手势
                ObjectAnimator.ofFloat(view, "translationY", 0f, -50f, 50f, 0f).apply {
                    duration = 1500L
                    repeatCount = 2
                    interpolator = AccelerateDecelerateInterpolator()
                } as AnimatorSet
            }
        }
    }
    
    enum class TransitionDirection {
        NEXT, PREVIOUS
    }
    
    enum class GestureType {
        PINCH_ZOOM, DOUBLE_TAP, LONG_PRESS, EDGE_SWIPE
    }
}
