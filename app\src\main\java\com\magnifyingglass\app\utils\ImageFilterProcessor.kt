package com.magnifyingglass.app.utils

import android.graphics.*
import android.graphics.drawable.ColorDrawable
import android.widget.ImageView
import androidx.camera.view.PreviewView

class ImageFilterProcessor {
    
    companion object {
        private const val TAG = "ImageFilterProcessor"
    }
    
    enum class FilterType {
        NONE,
        HIGH_CONTRAST,
        READING_MODE,
        GRAYSCALE,
        NEGATIVE
    }
    
    /**
     * 应用滤镜到PreviewView
     */
    fun applyFilterToPreview(previewView: PreviewView, filterType: FilterType) {
        val colorMatrix = when (filterType) {
            FilterType.NONE -> null
            FilterType.HIGH_CONTRAST -> createHighContrastMatrix()
            FilterType.READING_MODE -> createReadingModeMatrix()
            FilterType.GRAYSCALE -> createGrayscaleMatrix()
            FilterType.NEGATIVE -> createNegativeMatrix()
        }
        
        if (colorMatrix != null) {
            val colorFilter = ColorMatrixColorFilter(colorMatrix)
            previewView.foreground = ColorDrawable(Color.TRANSPARENT).apply {
                this.colorFilter = colorFilter
            }
        } else {
            previewView.foreground = null
        }
    }
    
    /**
     * 应用滤镜到Bitmap
     */
    fun applyFilterToBitmap(bitmap: Bitmap, filterType: FilterType): Bitmap {
        if (filterType == FilterType.NONE) return bitmap
        
        val colorMatrix = when (filterType) {
            FilterType.HIGH_CONTRAST -> createHighContrastMatrix()
            FilterType.READING_MODE -> createReadingModeMatrix()
            FilterType.GRAYSCALE -> createGrayscaleMatrix()
            FilterType.NEGATIVE -> createNegativeMatrix()
            else -> return bitmap
        }
        
        val paint = Paint().apply {
            colorFilter = ColorMatrixColorFilter(colorMatrix)
        }
        
        val filteredBitmap = Bitmap.createBitmap(
            bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888
        )
        
        val canvas = Canvas(filteredBitmap)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        
        return filteredBitmap
    }
    
    /**
     * 创建高对比度矩阵
     */
    private fun createHighContrastMatrix(): ColorMatrix {
        return ColorMatrix().apply {
            // 增加对比度并转换为黑白
            val contrast = 2.0f
            val brightness = -0.5f
            
            // 先转换为灰度
            setSaturation(0f)
            
            // 然后增加对比度
            val contrastMatrix = ColorMatrix(floatArrayOf(
                contrast, 0f, 0f, 0f, brightness * 255,
                0f, contrast, 0f, 0f, brightness * 255,
                0f, 0f, contrast, 0f, brightness * 255,
                0f, 0f, 0f, 1f, 0f
            ))
            
            postConcat(contrastMatrix)
            
            // 最后反转颜色（黑底白字）
            val negativeMatrix = ColorMatrix(floatArrayOf(
                -1f, 0f, 0f, 0f, 255f,
                0f, -1f, 0f, 0f, 255f,
                0f, 0f, -1f, 0f, 255f,
                0f, 0f, 0f, 1f, 0f
            ))
            
            postConcat(negativeMatrix)
        }
    }
    
    /**
     * 创建阅读模式矩阵
     */
    private fun createReadingModeMatrix(): ColorMatrix {
        return ColorMatrix().apply {
            // 降低饱和度，轻微增加对比度
            setSaturation(0.3f)
            
            val contrastMatrix = ColorMatrix(floatArrayOf(
                1.2f, 0f, 0f, 0f, 0f,
                0f, 1.2f, 0f, 0f, 0f,
                0f, 0f, 1.2f, 0f, 0f,
                0f, 0f, 0f, 1f, 0f
            ))
            
            postConcat(contrastMatrix)
        }
    }
    
    /**
     * 创建灰度矩阵
     */
    private fun createGrayscaleMatrix(): ColorMatrix {
        return ColorMatrix().apply {
            setSaturation(0f)
        }
    }
    
    /**
     * 创建负片矩阵
     */
    private fun createNegativeMatrix(): ColorMatrix {
        return ColorMatrix(floatArrayOf(
            -1f, 0f, 0f, 0f, 255f,
            0f, -1f, 0f, 0f, 255f,
            0f, 0f, -1f, 0f, 255f,
            0f, 0f, 0f, 1f, 0f
        ))
    }
    
    /**
     * 调整亮度
     */
    fun adjustBrightness(view: PreviewView, brightness: Float) {
        // brightness: 0.0 - 2.0 (0% - 200%)
        view.alpha = brightness.coerceIn(0.1f, 2.0f)
    }
    
    /**
     * 调整对比度
     */
    fun adjustContrast(bitmap: Bitmap, contrast: Float): Bitmap {
        val contrastMatrix = ColorMatrix(floatArrayOf(
            contrast, 0f, 0f, 0f, 0f,
            0f, contrast, 0f, 0f, 0f,
            0f, 0f, contrast, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        ))
        
        val paint = Paint().apply {
            colorFilter = ColorMatrixColorFilter(contrastMatrix)
        }
        
        val adjustedBitmap = Bitmap.createBitmap(
            bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888
        )
        
        val canvas = Canvas(adjustedBitmap)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        
        return adjustedBitmap
    }
    
    /**
     * 检测图像亮度
     */
    fun detectImageBrightness(bitmap: Bitmap): Float {
        var totalBrightness = 0L
        val pixels = IntArray(bitmap.width * bitmap.height)
        bitmap.getPixels(pixels, 0, bitmap.width, 0, 0, bitmap.width, bitmap.height)
        
        for (pixel in pixels) {
            val r = Color.red(pixel)
            val g = Color.green(pixel)
            val b = Color.blue(pixel)
            
            // 使用加权平均计算亮度
            val brightness = (0.299 * r + 0.587 * g + 0.114 * b).toInt()
            totalBrightness += brightness
        }
        
        return (totalBrightness / pixels.size.toFloat()) / 255f
    }
    
    /**
     * 检测是否为低光环境
     */
    fun isLowLightEnvironment(bitmap: Bitmap): Boolean {
        val brightness = detectImageBrightness(bitmap)
        return brightness < 0.3f // 亮度低于30%认为是低光环境
    }
    
    /**
     * 创建自定义滤镜
     */
    fun createCustomFilter(
        saturation: Float = 1.0f,
        contrast: Float = 1.0f,
        brightness: Float = 0.0f,
        invert: Boolean = false
    ): ColorMatrix {
        val matrix = ColorMatrix()
        
        // 设置饱和度
        matrix.setSaturation(saturation)
        
        // 设置对比度和亮度
        val contrastMatrix = ColorMatrix(floatArrayOf(
            contrast, 0f, 0f, 0f, brightness * 255,
            0f, contrast, 0f, 0f, brightness * 255,
            0f, 0f, contrast, 0f, brightness * 255,
            0f, 0f, 0f, 1f, 0f
        ))
        
        matrix.postConcat(contrastMatrix)
        
        // 如果需要反转颜色
        if (invert) {
            val invertMatrix = ColorMatrix(floatArrayOf(
                -1f, 0f, 0f, 0f, 255f,
                0f, -1f, 0f, 0f, 255f,
                0f, 0f, -1f, 0f, 255f,
                0f, 0f, 0f, 1f, 0f
            ))
            matrix.postConcat(invertMatrix)
        }
        
        return matrix
    }

    /**
     * 应用锐化效果
     */
    fun applySharpen(bitmap: Bitmap): Bitmap {
        return applySharpen(bitmap, 1.0f)
    }

    /**
     * 应用锐化效果（带强度参数）
     */
    fun applySharpen(bitmap: Bitmap, intensity: Float): Bitmap {
        val result = bitmap.copy(Bitmap.Config.ARGB_8888, true)
        val canvas = Canvas(result)
        val paint = Paint()

        // 锐化效果通过对比度增强实现
        val sharpenFactor = 1.0f + intensity * 0.5f
        val sharpenMatrix = ColorMatrix(floatArrayOf(
            sharpenFactor, 0f, 0f, 0f, 0f,
            0f, sharpenFactor, 0f, 0f, 0f,
            0f, 0f, sharpenFactor, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        ))

        paint.colorFilter = ColorMatrixColorFilter(sharpenMatrix)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        return result
    }

    /**
     * 应用对比度调节
     */
    fun applyContrast(bitmap: Bitmap, contrast: Float): Bitmap {
        return adjustContrast(bitmap, contrast)
    }

    /**
     * 应用亮度调节
     */
    fun applyBrightness(bitmap: Bitmap, brightness: Float): Bitmap {
        val result = bitmap.copy(Bitmap.Config.ARGB_8888, true)
        val canvas = Canvas(result)
        val paint = Paint()

        val offset = (brightness - 1f) * 255f
        val colorMatrix = ColorMatrix(floatArrayOf(
            1f, 0f, 0f, 0f, offset,
            0f, 1f, 0f, 0f, offset,
            0f, 0f, 1f, 0f, offset,
            0f, 0f, 0f, 1f, 0f
        ))
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)

        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        return result
    }

    /**
     * 应用模糊/降噪效果
     */
    fun applyBlur(bitmap: Bitmap, radius: Float): Bitmap {
        val result = bitmap.copy(Bitmap.Config.ARGB_8888, true)
        val canvas = Canvas(result)
        val paint = Paint()

        paint.maskFilter = BlurMaskFilter(radius, BlurMaskFilter.Blur.NORMAL)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        return result
    }
}
