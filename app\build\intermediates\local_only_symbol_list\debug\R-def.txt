R_DEF: Internal format may change without notice
local
color accent
color active
color black
color control_background
color control_background_pressed
color control_icon
color control_text
color error
color high_contrast_bg
color high_contrast_fg
color inactive
color overlay_dark
color overlay_light
color primary
color primary_dark
color transparent
color warning
color white
dimen control_button_size
dimen focus_indicator_size
dimen small_control_button_size
drawable brightness_indicator_background
drawable control_button_background
drawable enhance_dialog_background
drawable focus_indicator
drawable ic_app_icon
drawable ic_app_icon_with_border
drawable ic_back
drawable ic_camera
drawable ic_enhance
drawable ic_flashlight
drawable ic_freeze
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_magnifying_glass
drawable ic_mode
drawable ic_save
drawable ic_settings
drawable ic_zoom_in
drawable ic_zoom_out
drawable splash_background
id applyButton
id backButton
id bottomControlLayout
id brightnessSeekBar
id brightnessText
id brightnessValue
id buttonLayout
id cancelButton
id contrastSeekBar
id contrastValue
id descriptionTextView
id enhanceButton
id flashlightButton
id freezeButton
id frozenButtonsContainer
id frozenImageView
id grantButton
id iconImageView
id indicatorLayout
id modeButton
id nextButton
id noiseReductionSeekBar
id noiseReductionValue
id permissionIcon
id permissionMessage
id permissionTitle
id previewView
id resetButton
id saveButton
id sharpenSeekBar
id sharpenValue
id skipButton
id splash_icon
id splash_subtitle
id splash_title
id statusText
id titleTextView
id topInfoLayout
id viewPager
id zoomInButton
id zoomLevelText
id zoomOutButton
layout activity_main
layout activity_onboarding
layout activity_splash
layout dialog_enhance
layout dialog_permission
layout onboarding_page
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string back
string brightness_level
string camera_error
string camera_not_available
string dismiss
string enable_max_brightness
string enhance
string enhance_image
string flashlight
string flashlight_off
string flashlight_on
string focus_failed
string frame_frozen
string frame_unfrozen
string freeze_frame
string high_contrast_mode
string image_save_failed
string image_saved
string low_light_detected
string low_light_mode
string mode_switch
string normal_mode
string onboarding_desc_1
string onboarding_desc_2
string onboarding_desc_3
string onboarding_next
string onboarding_skip
string onboarding_start
string onboarding_title_1
string onboarding_title_2
string onboarding_title_3
string permission_camera_message
string permission_camera_title
string permission_cancel
string permission_denied
string permission_grant
string permission_storage_message
string permission_storage_title
string reading_mode
string save_image
string scenario_inspection
string scenario_medicine
string scenario_package
string settings
string zoom_in
string zoom_level
string zoom_out
style ControlButton
style InfoText
style OnboardingDescription
style OnboardingTitle
style SmallControlButton
style SplashTheme
style Theme.MagnifyingGlass
style Theme.MagnifyingGlass.NoActionBar
xml backup_rules
xml data_extraction_rules
