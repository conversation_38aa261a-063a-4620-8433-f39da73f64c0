package com.magnifyingglass.app

import android.Manifest
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.Bundle
import android.provider.MediaStore
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.magnifyingglass.app.databinding.ActivityMainBinding
import com.magnifyingglass.app.utils.CameraController
import com.magnifyingglass.app.utils.ImageFilterProcessor
import com.magnifyingglass.app.utils.PermissionHelper
import com.magnifyingglass.app.utils.ZoomController
import com.magnifyingglass.app.utils.GestureHandler
import com.magnifyingglass.app.utils.BrightnessController
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

class MainActivity : AppCompatActivity(), SensorEventListener {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var cameraController: CameraController
    private lateinit var imageFilterProcessor: ImageFilterProcessor
    private lateinit var permissionHelper: PermissionHelper
    private lateinit var zoomController: ZoomController
    private lateinit var gestureHandler: GestureHandler
    private lateinit var brightnessController: BrightnessController
    
    // 相机相关
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var preview: Preview? = null
    private var imageCapture: ImageCapture? = null
    private lateinit var cameraExecutor: ExecutorService
    
    // 手势检测 (现在由GestureHandler处理)
    
    // 传感器相关
    private lateinit var sensorManager: SensorManager
    private var gyroscope: Sensor? = null
    private var accelerometer: Sensor? = null
    
    // 状态变量
    private var currentZoomRatio = 1.0f
    private var previousZoomRatio = 1.0f
    private var currentBrightness = 100
    private var isFlashlightOn = false
    private var isFrameFrozen = false
    private var currentMode = CameraMode.NORMAL
    private var isStabilizationEnabled = true
    
    // 常量
    companion object {
        private const val MIN_ZOOM_RATIO = 1.0f
        private const val MAX_ZOOM_RATIO = 16.0f
        private const val ZOOM_STEP = 0.5f
        private const val BRIGHTNESS_STEP = 10
        private const val EDGE_THRESHOLD = 100 // 边缘滑动检测阈值
        private const val LONG_PRESS_TIMEOUT = 500L
    }
    
    enum class CameraMode {
        NORMAL, READING, LOW_LIGHT, HIGH_CONTRAST
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 检查是否需要显示引导页面
        if (shouldShowOnboarding()) {
            startActivity(Intent(this, OnboardingActivity::class.java))
            finish()
            return
        }
        
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        initializeComponents()
        setupUI()
        checkPermissionsAndStartCamera()
    }
    
    private fun shouldShowOnboarding(): Boolean {
        val prefs = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        return !prefs.getBoolean("onboarding_completed", false)
    }
    
    private fun initializeComponents() {
        cameraExecutor = Executors.newSingleThreadExecutor()
        cameraController = CameraController(this)
        imageFilterProcessor = ImageFilterProcessor()
        permissionHelper = PermissionHelper(this)

        // 初始化亮度控制器
        brightnessController = BrightnessController(this, binding.previewView)
        brightnessController.setBrightnessListener(object : BrightnessController.BrightnessListener {
            override fun onBrightnessChanged(brightness: Int) {
                currentBrightness = brightness
                binding.brightnessText.text = getString(R.string.brightness_level, brightness)
            }

            override fun onLowLightDetected() {
                // 显示低光环境提示
                showLowLightDialog()
            }

            override fun onBrightnessIndicatorVisibilityChanged(visible: Boolean) {
                binding.brightnessText.visibility = if (visible) View.VISIBLE else View.GONE
            }
        })

        // 初始化手势处理器
        gestureHandler = GestureHandler(this, object : GestureHandler.GestureListener {
            override fun onZoomGesture(scaleFactor: Float) {
                if (::zoomController.isInitialized) {
                    zoomController.handlePinchZoom(scaleFactor)
                }
            }

            override fun onDoubleTap() {
                if (::zoomController.isInitialized) {
                    zoomController.toggleZoom()
                }
            }

            override fun onLongPress() {
                if (!isFrameFrozen) {
                    freezeFrame()
                }
            }

            override fun onSingleTap(x: Float, y: Float) {
                focusOnPoint(x, y)
            }

            override fun onBrightnessAdjust(delta: Int) {
                brightnessController.adjustBrightness(delta)
            }

            override fun onEdgeSwipeStart() {
                // 边缘滑动开始，可以显示亮度指示器
            }

            override fun onEdgeSwipeEnd() {
                // 边缘滑动结束
            }
        })

        // 初始化传感器
        sensorManager = getSystemService(Context.SENSOR_SERVICE) as SensorManager
        gyroscope = sensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE)
        accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
    }
    
    private fun setupUI() {
        // 设置触摸监听器
        binding.previewView.setOnTouchListener { view, event ->
            gestureHandler.onTouchEvent(event, view)
        }
        
        // 设置按钮点击监听器
        binding.zoomInButton.setOnClickListener { zoomIn() }
        binding.zoomOutButton.setOnClickListener { zoomOut() }
        binding.freezeButton.setOnClickListener { toggleFreeze() }
        binding.saveButton.setOnClickListener { saveImage() }
        binding.flashlightButton.setOnClickListener { toggleFlashlight() }
        binding.modeButton.setOnClickListener { switchMode() }
        
        updateUI()
    }
    
    private fun checkPermissionsAndStartCamera() {
        if (permissionHelper.hasCameraPermission()) {
            startCamera()
        } else {
            requestCameraPermission()
        }
    }
    
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            startCamera()
        } else {
            showPermissionDeniedDialog()
        }
    }
    
    private fun requestCameraPermission() {
        requestPermissionLauncher.launch(Manifest.permission.CAMERA)
    }
    
    private fun showPermissionDeniedDialog() {
        Toast.makeText(this, getString(R.string.permission_denied), Toast.LENGTH_LONG).show()
        finish()
    }
    
    private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)
        
        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                bindCameraUseCases()
                
                // 启用传感器监听
                if (isStabilizationEnabled) {
                    enableSensorListening()
                }
                
            } catch (exc: Exception) {
                Toast.makeText(this, getString(R.string.camera_error), Toast.LENGTH_SHORT).show()
            }
        }, ContextCompat.getMainExecutor(this))
    }
    
    private fun bindCameraUseCases() {
        val cameraProvider = cameraProvider ?: return
        
        // 预览用例
        preview = Preview.Builder().build().also {
            it.setSurfaceProvider(binding.previewView.surfaceProvider)
        }
        
        // 图像捕获用例
        imageCapture = ImageCapture.Builder()
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
            .build()
        
        // 选择后置摄像头
        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
        
        try {
            // 解绑所有用例
            cameraProvider.unbindAll()
            
            // 绑定用例到生命周期
            camera = cameraProvider.bindToLifecycle(
                this, cameraSelector, preview, imageCapture
            )

            // 初始化变焦控制器
            camera?.let { cam ->
                zoomController = ZoomController(cam, this)
                zoomController.setZoomListener(object : ZoomController.ZoomListener {
                    override fun onZoomChanged(zoomRatio: Float) {
                        currentZoomRatio = zoomRatio
                        updateZoomDisplay()
                    }

                    override fun onZoomLimitsChanged(minZoom: Float, maxZoom: Float) {
                        // 更新UI中的变焦限制显示
                    }
                })
            }
            
        } catch (exc: Exception) {
            Toast.makeText(this, getString(R.string.camera_not_available), Toast.LENGTH_SHORT).show()
        }
    }
    
    // 手势监听器
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            if (::zoomController.isInitialized) {
                zoomController.handlePinchZoom(detector.scaleFactor)
            }
            return true
        }
    }
    
    private inner class GestureListener : GestureDetector.SimpleOnGestureListener() {
        override fun onDoubleTap(e: MotionEvent): Boolean {
            if (::zoomController.isInitialized) {
                zoomController.toggleZoom()
            }
            return true
        }
        
        override fun onLongPress(e: MotionEvent) {
            // 长按冻结画面
            if (!isFrameFrozen) {
                freezeFrame()
            }
        }
        
        override fun onSingleTapUp(e: MotionEvent): Boolean {
            // 点击对焦
            focusOnPoint(e.x, e.y)
            return true
        }
    }
    

    private fun handleEdgeSwipe(event: MotionEvent) {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 检查是否在屏幕边缘
                val screenWidth = binding.root.width
                if (event.x < EDGE_THRESHOLD || event.x > screenWidth - EDGE_THRESHOLD) {
                    // 记录初始Y位置用于亮度调节
                }
            }
            MotionEvent.ACTION_MOVE -> {
                val screenWidth = binding.root.width
                if (event.x < EDGE_THRESHOLD || event.x > screenWidth - EDGE_THRESHOLD) {
                    // 根据Y轴移动调节亮度
                    adjustBrightnessFromSwipe(event)
                }
            }
        }
    }

    private fun adjustBrightnessFromSwipe(event: MotionEvent) {
        // 简化的亮度调节逻辑
        val deltaY = event.historySize.let { size ->
            if (size > 0) event.getHistoricalY(size - 1) - event.y else 0f
        }

        if (kotlin.math.abs(deltaY) > 10) {
            val brightnessChange = (deltaY / 20).toInt()
            adjustBrightness(brightnessChange)
        }
    }

    private fun adjustBrightness(change: Int) {
        currentBrightness = (currentBrightness + change).coerceIn(0, 200)

        // 应用亮度变化到预览
        val alpha = currentBrightness / 100f
        binding.previewView.alpha = alpha.coerceIn(0.1f, 2.0f)

        // 显示亮度指示
        binding.brightnessText.text = getString(R.string.brightness_level, currentBrightness)
        binding.brightnessText.visibility = View.VISIBLE

        // 3秒后隐藏亮度指示
        binding.brightnessText.postDelayed({
            binding.brightnessText.visibility = View.GONE
        }, 3000)
    }

    private fun zoomIn() {
        if (::zoomController.isInitialized) {
            zoomController.zoomIn()
        }
    }

    private fun zoomOut() {
        if (::zoomController.isInitialized) {
            zoomController.zoomOut()
        }
    }

    private fun focusOnPoint(x: Float, y: Float) {
        val factory = binding.previewView.meteringPointFactory
        val point = factory.createPoint(x, y)
        val action = FocusMeteringAction.Builder(point).build()

        camera?.cameraControl?.startFocusAndMetering(action)?.addListener({
            // 对焦完成
        }, ContextCompat.getMainExecutor(this))
    }

    private fun toggleFreeze() {
        if (isFrameFrozen) {
            unfreezeFrame()
        } else {
            freezeFrame()
        }
    }

    private fun freezeFrame() {
        // 捕获当前预览帧
        val bitmap = binding.previewView.getBitmap()
        if (bitmap != null) {
            binding.frozenImageView.setImageBitmap(bitmap)
            binding.frozenImageView.visibility = View.VISIBLE
            binding.saveButton.visibility = View.VISIBLE
            binding.statusText.visibility = View.VISIBLE
            binding.statusText.text = getString(R.string.frame_frozen)

            isFrameFrozen = true
            updateUI()

            Toast.makeText(this, getString(R.string.frame_frozen), Toast.LENGTH_SHORT).show()
        }
    }

    private fun unfreezeFrame() {
        binding.frozenImageView.visibility = View.GONE
        binding.saveButton.visibility = View.GONE
        binding.statusText.visibility = View.GONE

        isFrameFrozen = false
        updateUI()

        Toast.makeText(this, getString(R.string.frame_unfrozen), Toast.LENGTH_SHORT).show()
    }

    private fun saveImage() {
        val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (bitmap != null) {
            lifecycleScope.launch {
                try {
                    saveImageToGallery(bitmap)
                    Toast.makeText(this@MainActivity, getString(R.string.image_saved), Toast.LENGTH_SHORT).show()
                } catch (e: Exception) {
                    Toast.makeText(this@MainActivity, getString(R.string.image_save_failed), Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private suspend fun saveImageToGallery(bitmap: Bitmap) {
        val filename = "magnifier_${SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())}.jpg"

        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, filename)
            put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
            put(MediaStore.MediaColumns.RELATIVE_PATH, "Pictures/MagnifyingGlass")
        }

        val uri = contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
        uri?.let {
            contentResolver.openOutputStream(it)?.use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
            }
        }
    }

    private fun toggleFlashlight() {
        camera?.let { cam ->
            isFlashlightOn = !isFlashlightOn
            cam.cameraControl.enableTorch(isFlashlightOn)

            val message = if (isFlashlightOn) {
                getString(R.string.flashlight_on)
            } else {
                getString(R.string.flashlight_off)
            }
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
            updateUI()
        }
    }

    private fun switchMode() {
        currentMode = when (currentMode) {
            CameraMode.NORMAL -> CameraMode.READING
            CameraMode.READING -> CameraMode.HIGH_CONTRAST
            CameraMode.HIGH_CONTRAST -> CameraMode.LOW_LIGHT
            CameraMode.LOW_LIGHT -> CameraMode.NORMAL
        }

        applyCurrentMode()
        updateUI()
    }

    private fun applyCurrentMode() {
        when (currentMode) {
            CameraMode.NORMAL -> {
                // 恢复正常模式
                binding.previewView.alpha = 1.0f
                isStabilizationEnabled = true
            }
            CameraMode.READING -> {
                // 阅读模式：降低饱和度，提高对比度，关闭防抖
                isStabilizationEnabled = false
                // 这里可以添加图像滤镜处理
            }
            CameraMode.HIGH_CONTRAST -> {
                // 高对比度模式
                // 这里可以添加高对比度滤镜
            }
            CameraMode.LOW_LIGHT -> {
                // 低光模式：开启闪光灯，提高亮度
                if (!isFlashlightOn) {
                    toggleFlashlight()
                }
                adjustBrightness(50) // 提高亮度
            }
        }
    }

    private fun updateUI() {
        updateZoomDisplay()
        updateModeDisplay()
        updateControlsVisibility()
    }

    private fun updateZoomDisplay() {
        binding.zoomLevelText.text = getString(R.string.zoom_level, currentZoomRatio.toInt())
    }

    private fun updateModeDisplay() {
        val modeText = when (currentMode) {
            CameraMode.NORMAL -> getString(R.string.normal_mode)
            CameraMode.READING -> getString(R.string.reading_mode)
            CameraMode.HIGH_CONTRAST -> getString(R.string.high_contrast_mode)
            CameraMode.LOW_LIGHT -> getString(R.string.low_light_mode)
        }

        // 可以在这里更新模式指示器
    }

    private fun updateControlsVisibility() {
        // 根据当前状态更新控件可见性
        binding.bottomControlLayout.visibility = if (isFrameFrozen) View.GONE else View.VISIBLE
    }

    // 传感器监听
    private fun enableSensorListening() {
        gyroscope?.let {
            sensorManager.registerListener(this, it, SensorManager.SENSOR_DELAY_GAME)
        }
        accelerometer?.let {
            sensorManager.registerListener(this, it, SensorManager.SENSOR_DELAY_GAME)
        }
    }

    private fun disableSensorListening() {
        sensorManager.unregisterListener(this)
    }

    override fun onSensorChanged(event: SensorEvent?) {
        if (!isStabilizationEnabled) return

        event?.let {
            when (it.sensor.type) {
                Sensor.TYPE_GYROSCOPE -> {
                    // 处理陀螺仪数据进行防抖
                    handleGyroscopeData(it.values)
                }
                Sensor.TYPE_ACCELEROMETER -> {
                    // 处理加速度计数据
                    handleAccelerometerData(it.values)
                }
            }
        }
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // 传感器精度变化处理
    }

    private fun handleGyroscopeData(values: FloatArray) {
        // 简化的防抖处理
        val rotationX = values[0]
        val rotationY = values[1]
        val rotationZ = values[2]

        // 这里可以实现基于陀螺仪的图像稳定算法
        // 当前为简化实现
    }

    private fun handleAccelerometerData(values: FloatArray) {
        // 检测设备移动，用于防抖
        val x = values[0]
        val y = values[1]
        val z = values[2]

        // 计算总加速度
        val totalAcceleration = kotlin.math.sqrt((x * x + y * y + z * z).toDouble())

        // 如果检测到剧烈移动，可以暂时降低对焦频率
    }

    override fun onResume() {
        super.onResume()
        if (isStabilizationEnabled) {
            enableSensorListening()
        }
    }

    override fun onPause() {
        super.onPause()
        disableSensorListening()
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraExecutor.shutdown()
        disableSensorListening()
        if (::zoomController.isInitialized) {
            zoomController.cleanup()
        }
    }
}
