package com.magnifyingglass.app

import android.Manifest
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import com.magnifyingglass.app.databinding.ActivityMainBinding

/**
 * 简化版MainActivity - 只保留核心功能
 */
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var camera: Camera
    private var isFrameFrozen = false
    private var isFlashlightOn = false

    // 权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            startCamera()
        } else {
            Toast.makeText(this, "需要相机权限才能使用应用", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 检查相机权限
        if (allPermissionsGranted()) {
            startCamera()
        } else {
            requestPermissionLauncher.launch(Manifest.permission.CAMERA)
        }

        setupButtonListeners()
    }

    private fun setupButtonListeners() {
        binding.flashlightButton.setOnClickListener { toggleFlashlight() }
        binding.saveButton.setOnClickListener { saveCurrentFrame() }
        
        // 长按冻结画面
        binding.previewView.setOnLongClickListener {
            if (!isFrameFrozen) {
                freezeFrame()
            } else {
                unfreezeFrame()
            }
            true
        }
    }

    private fun allPermissionsGranted() = ContextCompat.checkSelfPermission(
        this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED

    private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            val cameraProvider: ProcessCameraProvider = cameraProviderFuture.get()

            val preview = Preview.Builder().build().also {
                it.setSurfaceProvider(binding.previewView.surfaceProvider)
            }

            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

            try {
                cameraProvider.unbindAll()
                camera = cameraProvider.bindToLifecycle(
                    this, cameraSelector, preview)

            } catch(exc: Exception) {
                Toast.makeText(this, "相机启动失败", Toast.LENGTH_SHORT).show()
            }

        }, ContextCompat.getMainExecutor(this))
    }

    private fun toggleFlashlight() {
        if (::camera.isInitialized) {
            isFlashlightOn = !isFlashlightOn
            camera.cameraControl.enableTorch(isFlashlightOn)
            
            val message = if (isFlashlightOn) {
                "闪光灯已开启"
            } else {
                "闪光灯已关闭"
            }
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        }
    }

    private fun freezeFrame() {
        isFrameFrozen = true
        
        // 捕获当前画面
        val bitmap = captureCurrentFrame()
        if (bitmap != null) {
            binding.frozenImageView.setImageBitmap(bitmap)
            binding.frozenImageView.visibility = View.VISIBLE
            Toast.makeText(this, "画面已冻结，长按解冻", Toast.LENGTH_SHORT).show()
        }
    }

    private fun unfreezeFrame() {
        isFrameFrozen = false
        binding.frozenImageView.visibility = View.GONE
        Toast.makeText(this, "画面已解冻", Toast.LENGTH_SHORT).show()
    }

    private fun captureCurrentFrame(): Bitmap? {
        return try {
            val bitmap = Bitmap.createBitmap(
                binding.previewView.width,
                binding.previewView.height,
                Bitmap.Config.ARGB_8888
            )
            val canvas = Canvas(bitmap)
            binding.previewView.draw(canvas)
            bitmap
        } catch (e: Exception) {
            null
        }
    }

    private fun saveCurrentFrame() {
        if (isFrameFrozen) {
            val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
            if (bitmap != null) {
                Toast.makeText(this, "图像已保存", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(this, "请先冻结画面再保存", Toast.LENGTH_SHORT).show()
        }
    }
}
