package com.magnifyingglass.app

import android.Manifest
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.Bundle
import android.provider.MediaStore
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.magnifyingglass.app.databinding.ActivityMainBinding
import com.magnifyingglass.app.utils.CameraController
import com.magnifyingglass.app.utils.ImageFilterProcessor
import com.magnifyingglass.app.utils.PermissionHelper
import com.magnifyingglass.app.utils.ZoomController
import com.magnifyingglass.app.utils.GestureHandler
import com.magnifyingglass.app.utils.BrightnessController
import com.magnifyingglass.app.utils.StabilizationController
import com.magnifyingglass.app.utils.ImageSaver
import com.magnifyingglass.app.utils.ModeManager
import com.magnifyingglass.app.utils.PermissionDialogManager
import com.magnifyingglass.app.utils.PerformanceMonitor
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

class MainActivity : AppCompatActivity(), SensorEventListener {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var cameraController: CameraController
    private lateinit var imageFilterProcessor: ImageFilterProcessor
    private lateinit var permissionHelper: PermissionHelper
    private lateinit var zoomController: ZoomController
    private lateinit var gestureHandler: GestureHandler
    private lateinit var brightnessController: BrightnessController
    private lateinit var stabilizationController: StabilizationController
    private lateinit var imageSaver: ImageSaver
    private lateinit var modeManager: ModeManager
    private lateinit var permissionDialogManager: PermissionDialogManager
    private lateinit var performanceMonitor: PerformanceMonitor
    
    // 相机相关
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var preview: Preview? = null
    private var imageCapture: ImageCapture? = null
    private lateinit var cameraExecutor: ExecutorService
    
    // 手势检测 (现在由GestureHandler处理)
    
    // 传感器相关
    private lateinit var sensorManager: SensorManager
    private var gyroscope: Sensor? = null
    private var accelerometer: Sensor? = null
    
    // 状态变量
    private var currentZoomRatio = 1.0f
    private var previousZoomRatio = 1.0f
    private var currentBrightness = 100
    private var isFlashlightOn = false
    private var isFrameFrozen = false
    private var isStabilizationEnabled = true
    
    // 常量
    companion object {
        private const val MIN_ZOOM_RATIO = 1.0f
        private const val MAX_ZOOM_RATIO = 16.0f
        private const val ZOOM_STEP = 0.5f
        private const val BRIGHTNESS_STEP = 10
        private const val EDGE_THRESHOLD = 100 // 边缘滑动检测阈值
        private const val LONG_PRESS_TIMEOUT = 500L
    }
    
    // CameraMode现在由ModeManager管理
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 检查是否需要显示引导页面
        if (shouldShowOnboarding()) {
            startActivity(Intent(this, OnboardingActivity::class.java))
            finish()
            return
        }
        
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        initializeComponents()
        setupUI()

        // 开始性能监控
        performanceMonitor.startMonitoring()

        checkPermissionsAndStartCamera()
    }
    
    private fun shouldShowOnboarding(): Boolean {
        val prefs = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        return !prefs.getBoolean("onboarding_completed", false)
    }
    
    private fun initializeComponents() {
        cameraExecutor = Executors.newSingleThreadExecutor()
        cameraController = CameraController(this)
        imageFilterProcessor = ImageFilterProcessor()
        permissionHelper = PermissionHelper(this)
        imageSaver = ImageSaver(this)
        permissionDialogManager = PermissionDialogManager(this)

        // 初始化性能监控
        performanceMonitor = PerformanceMonitor(this)
        performanceMonitor.setPerformanceListener(object : PerformanceMonitor.PerformanceListener {
            override fun onMemoryWarning(usedMemoryMB: Long, totalMemoryMB: Long) {
                // 内存警告，可以清理一些缓存
                performanceMonitor.forceGarbageCollection()
            }

            override fun onLowMemory() {
                // 系统低内存，释放非必要资源
                if (::imageFilterProcessor.isInitialized) {
                    // 可以清理滤镜缓存等
                }
            }

            override fun onPerformanceReport(report: PerformanceMonitor.PerformanceReport) {
                // 性能报告，可以用于调试
                android.util.Log.d("Performance", "APK Size: ${report.apkSizeMB}MB")
            }
        })

        // 初始化模式管理器
        modeManager = ModeManager(this)
        modeManager.setModeChangeListener(object : ModeManager.ModeChangeListener {
            override fun onModeChanged(newMode: ModeManager.CameraMode, oldMode: ModeManager.CameraMode) {
                updateModeDisplay()
            }

            override fun onModeConfigurationChanged(mode: ModeManager.CameraMode, config: ModeManager.ModeConfiguration) {
                applyModeConfiguration(config)
            }
        })

        // 初始化亮度控制器
        brightnessController = BrightnessController(this, binding.previewView)
        brightnessController.setBrightnessListener(object : BrightnessController.BrightnessListener {
            override fun onBrightnessChanged(brightness: Int) {
                currentBrightness = brightness
                binding.brightnessText.text = getString(R.string.brightness_level, brightness)
            }

            override fun onLowLightDetected() {
                // 显示低光环境提示
                showLowLightDialog()
            }

            override fun onBrightnessIndicatorVisibilityChanged(visible: Boolean) {
                binding.brightnessText.visibility = if (visible) View.VISIBLE else View.GONE
            }
        })

        // 初始化手势处理器
        gestureHandler = GestureHandler(this, object : GestureHandler.GestureListener {
            override fun onZoomGesture(scaleFactor: Float) {
                if (::zoomController.isInitialized) {
                    zoomController.handlePinchZoom(scaleFactor)
                }
            }

            override fun onDoubleTap() {
                if (::zoomController.isInitialized) {
                    zoomController.toggleZoom()
                }
            }

            override fun onLongPress() {
                if (!isFrameFrozen) {
                    freezeFrame()
                }
            }

            override fun onSingleTap(x: Float, y: Float) {
                focusOnPoint(x, y)
            }

            override fun onBrightnessAdjust(delta: Int) {
                brightnessController.adjustBrightness(delta)
            }

            override fun onEdgeSwipeStart() {
                // 边缘滑动开始，可以显示亮度指示器
            }

            override fun onEdgeSwipeEnd() {
                // 边缘滑动结束
            }
        })

        // 初始化防抖控制器
        stabilizationController = StabilizationController(this)
        stabilizationController.setStabilizationListener(object : StabilizationController.StabilizationListener {
            override fun onStabilizationStateChanged(isStable: Boolean) {
                // 可以在这里更新UI显示稳定性状态
                runOnUiThread {
                    // 例如：显示稳定性指示器
                }
            }

            override fun onMovementDetected(intensity: Float) {
                // 可以根据运动强度调整对焦频率等
            }
        })

        // 初始化传感器
        sensorManager = getSystemService(Context.SENSOR_SERVICE) as SensorManager
        gyroscope = sensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE)
        accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
    }
    
    private fun setupUI() {
        // 设置触摸监听器
        binding.previewView.setOnTouchListener { view, event ->
            gestureHandler.onTouchEvent(event, view)
        }
        
        // 设置按钮点击监听器
        binding.zoomInButton.setOnClickListener { zoomIn() }
        binding.zoomOutButton.setOnClickListener { zoomOut() }

        // 设置长按监听器实现连续放大缩小
        setupLongPressZoom()
        binding.freezeButton.setOnClickListener { toggleFreeze() }
        binding.saveButton.setOnClickListener { saveImage() }
        binding.backButton.setOnClickListener { unfreezeFrame() }
        binding.enhanceButton.setOnClickListener { showEnhanceOptions() }
        binding.flashlightButton.setOnClickListener { toggleFlashlight() }
        binding.modeButton.setOnClickListener { switchMode() }
        
        updateUI()
    }
    
    private fun checkPermissionsAndStartCamera() {
        if (permissionHelper.hasCameraPermission()) {
            startCamera()
        } else {
            if (permissionHelper.shouldShowCameraPermissionRationale(this)) {
                // 显示权限说明对话框
                permissionDialogManager.showPermissionRationaleDialog("camera",
                    object : PermissionDialogManager.PermissionDialogListener {
                        override fun onPermissionGranted() {
                            requestCameraPermission()
                        }

                        override fun onPermissionDenied() {
                            showPermissionDeniedDialog()
                        }

                        override fun onSettingsRequested() {
                            // 用户前往设置页面
                        }
                    })
            } else {
                requestCameraPermission()
            }
        }
    }
    
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            startCamera()
        } else {
            showPermissionDeniedDialog()
        }
    }
    
    private fun requestCameraPermission() {
        requestPermissionLauncher.launch(android.Manifest.permission.CAMERA)
    }
    
    private fun showPermissionDeniedDialog() {
        permissionDialogManager.showFeatureLimitationDialog("camera")
        finish()
    }

    private fun showLowLightDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(getString(R.string.low_light_detected))
            .setMessage(getString(R.string.low_light_detected))
            .setPositiveButton(getString(R.string.enable_max_brightness)) { _, _ ->
                brightnessController.setMaxBrightness()
                if (!isFlashlightOn) {
                    toggleFlashlight()
                }
            }
            .setNegativeButton(getString(R.string.dismiss), null)
            .show()
    }
    
    private fun startCamera() {
        performanceMonitor.recordCameraInitStart()

        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                bindCameraUseCases()

                performanceMonitor.recordCameraInitComplete()
                
                // 启用传感器监听
                if (isStabilizationEnabled) {
                    enableSensorListening()
                }
                
            } catch (exc: Exception) {
                Toast.makeText(this, getString(R.string.camera_error), Toast.LENGTH_SHORT).show()
            }
        }, ContextCompat.getMainExecutor(this))
    }
    
    private fun bindCameraUseCases() {
        val cameraProvider = cameraProvider ?: return

        // 预览用例 - 优化配置
        preview = Preview.Builder()
            .setTargetAspectRatio(androidx.camera.core.AspectRatio.RATIO_4_3)
            .build().also {
                it.setSurfaceProvider(binding.previewView.surfaceProvider)
            }

        // 图像捕获用例 - 优化配置
        imageCapture = ImageCapture.Builder()
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY) // 改为最大质量
            .setTargetAspectRatio(androidx.camera.core.AspectRatio.RATIO_4_3)
            .setJpegQuality(95) // 设置高质量JPEG
            .build()

        // 选择后置摄像头
        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

        try {
            // 解绑所有用例
            cameraProvider.unbindAll()

            // 绑定用例到生命周期
            camera = cameraProvider.bindToLifecycle(
                this, cameraSelector, preview, imageCapture
            )

            // 初始化变焦控制器
            camera?.let { cam ->
                zoomController = ZoomController(cam, this)
                zoomController.setZoomListener(object : ZoomController.ZoomListener {
                    override fun onZoomChanged(zoomRatio: Float) {
                        currentZoomRatio = zoomRatio
                        updateZoomDisplay()
                    }

                    override fun onZoomLimitsChanged(minZoom: Float, maxZoom: Float) {
                        // 更新UI中的变焦限制显示
                    }
                })

                // 设置相机到亮度控制器
                brightnessController.setCamera(cam)

                // 设置相机到防抖控制器
                stabilizationController.setCamera(cam)

                // 启用连续自动对焦
                enableContinuousAutoFocus()
            }
            
        } catch (exc: Exception) {
            Toast.makeText(this, getString(R.string.camera_not_available), Toast.LENGTH_SHORT).show()
        }
    }
    
    // 手势处理现在由GestureHandler类处理
    
    // 亮度调节现在由BrightnessController处理

    private fun zoomIn() {
        if (::zoomController.isInitialized) {
            zoomController.zoomIn()
        }
    }

    private fun zoomOut() {
        if (::zoomController.isInitialized) {
            zoomController.zoomOut()
        }
    }

    private fun focusOnPoint(x: Float, y: Float) {
        val factory = binding.previewView.meteringPointFactory
        val point = factory.createPoint(x, y)

        // 创建对焦和测光动作，包括自动对焦、自动曝光和自动白平衡
        val action = FocusMeteringAction.Builder(point)
            .addPoint(point, FocusMeteringAction.FLAG_AF) // 自动对焦
            .addPoint(point, FocusMeteringAction.FLAG_AE) // 自动曝光
            .addPoint(point, FocusMeteringAction.FLAG_AWB) // 自动白平衡
            .setAutoCancelDuration(5, java.util.concurrent.TimeUnit.SECONDS) // 5秒后自动取消
            .build()

        camera?.cameraControl?.startFocusAndMetering(action)?.addListener({
            // 对焦完成，显示对焦成功指示
            showFocusIndicator(x, y)
        }, ContextCompat.getMainExecutor(this))
    }

    /**
     * 显示对焦指示器
     */
    private fun showFocusIndicator(x: Float, y: Float) {
        // 创建一个简单的对焦指示器动画
        val focusIndicator = View(this)
        focusIndicator.setBackgroundResource(R.drawable.focus_indicator)
        focusIndicator.alpha = 0.7f

        // 设置指示器位置和大小
        val size = resources.getDimensionPixelSize(R.dimen.focus_indicator_size)
        val params = android.widget.FrameLayout.LayoutParams(size, size)
        params.leftMargin = x.toInt() - size / 2
        params.topMargin = y.toInt() - size / 2

        // 添加到布局
        binding.root.addView(focusIndicator, params)

        // 创建动画
        focusIndicator.animate()
            .scaleX(1.2f).scaleY(1.2f)
            .setDuration(300)
            .withEndAction {
                focusIndicator.animate()
                    .alpha(0f).scaleX(0.8f).scaleY(0.8f)
                    .setDuration(300)
                    .withEndAction {
                        binding.root.removeView(focusIndicator)
                    }
                    .start()
            }
            .start()
    }

    /**
     * 启用连续自动对焦
     */
    private fun enableContinuousAutoFocus() {
        camera?.let { cam ->
            try {
                // 创建屏幕中心点
                val factory = SurfaceOrientedMeteringPointFactory(1f, 1f)
                val centerPoint = factory.createPoint(0.5f, 0.5f)

                // 创建连续自动对焦动作
                val action = FocusMeteringAction.Builder(centerPoint)
                    .disableAutoCancel() // 不自动取消
                    .build()

                cam.cameraControl.startFocusAndMetering(action)

                android.util.Log.d("MainActivity", "Continuous auto focus enabled")
            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "Failed to enable continuous auto focus", e)
            }
        }
    }

    private fun toggleFreeze() {
        if (isFrameFrozen) {
            unfreezeFrame()
        } else {
            freezeFrame()
        }
    }

    private fun freezeFrame() {
        // 捕获当前预览帧
        val bitmap = binding.previewView.getBitmap()
        if (bitmap != null) {
            // 应用当前滤镜到捕获的图像
            val currentConfig = modeManager.getCurrentModeConfiguration()
            val filteredBitmap = if (currentConfig.filterType != ImageFilterProcessor.FilterType.NONE) {
                imageFilterProcessor.applyFilterToBitmap(bitmap, currentConfig.filterType)
            } else {
                bitmap
            }

            binding.frozenImageView.setImageBitmap(filteredBitmap)
            binding.frozenImageView.visibility = View.VISIBLE
            binding.frozenButtonsContainer.visibility = View.VISIBLE
            binding.statusText.visibility = View.VISIBLE
            binding.statusText.text = getString(R.string.frame_frozen)

            isFrameFrozen = true
            updateUI()

            // 冻结时禁用相机预览但保留手势用于解冻
            binding.previewView.visibility = View.INVISIBLE
            // 不完全禁用手势，保留长按解冻功能

            // 为冻结图像添加长按解冻功能
            binding.frozenImageView.setOnLongClickListener {
                unfreezeFrame()
                true
            }

            Toast.makeText(this, getString(R.string.frame_frozen), Toast.LENGTH_SHORT).show()
        }
    }

    private fun unfreezeFrame() {
        binding.frozenImageView.visibility = View.GONE
        binding.frozenButtonsContainer.visibility = View.GONE
        binding.statusText.visibility = View.GONE

        // 恢复相机预览和手势
        binding.previewView.visibility = View.VISIBLE
        gestureHandler.setEnabled(true) // 启用手势

        isFrameFrozen = false
        updateUI()

        Toast.makeText(this, getString(R.string.frame_unfrozen), Toast.LENGTH_SHORT).show()
    }

    private fun saveImage() {
        val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (bitmap != null) {
            // 检查存储权限（Android 10以下需要）
            if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.Q) {
                if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED) {
                    requestStoragePermission()
                    return
                }
            }

            lifecycleScope.launch {
                try {
                    // 检查存储空间
                    val estimatedSize = imageSaver.estimateImageSize(bitmap)
                    if (!imageSaver.checkStorageSpace(estimatedSize)) {
                        runOnUiThread {
                            Toast.makeText(this@MainActivity, "存储空间不足", Toast.LENGTH_SHORT).show()
                        }
                        return@launch
                    }

                    imageSaver.saveImageToGallery(
                        bitmap = bitmap,
                        listener = object : ImageSaver.SaveListener {
                            override fun onSaveSuccess(uri: android.net.Uri?, filePath: String?) {
                                runOnUiThread {
                                    Toast.makeText(this@MainActivity, getString(R.string.image_saved), Toast.LENGTH_LONG).show()
                                    unfreezeFrame() // 保存成功后解冻
                                }
                            }

                            override fun onSaveError(error: String) {
                                runOnUiThread {
                                    Toast.makeText(this@MainActivity, getString(R.string.image_save_failed), Toast.LENGTH_LONG).show()
                                }
                            }

                            override fun onSaveProgress(progress: Int) {
                                // 可以在这里显示保存进度
                            }
                        }
                    )
                } catch (e: Exception) {
                    runOnUiThread {
                        Toast.makeText(this@MainActivity, "保存失败: ${e.message}", Toast.LENGTH_LONG).show()
                    }
                }
            }
        }
    }

    private fun requestStoragePermission() {
        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.Q) {
            requestPermissionLauncher.launch(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    }

    private fun showEnhanceOptions() {
        val options = arrayOf("锐化", "对比度增强", "亮度调节", "降噪")

        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        builder.setTitle("画面增强")
        builder.setItems(options) { _, which ->
            when (which) {
                0 -> applySharpening()
                1 -> applyContrastEnhancement()
                2 -> applyBrightnessAdjustment()
                3 -> applyNoiseReduction()
            }
        }
        builder.setNegativeButton("取消", null)
        builder.show()
    }

    private fun applySharpening() {
        val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (bitmap != null) {
            val enhancedBitmap = imageFilterProcessor.applySharpen(bitmap)
            binding.frozenImageView.setImageBitmap(enhancedBitmap)
            Toast.makeText(this, "已应用锐化效果", Toast.LENGTH_SHORT).show()
        }
    }

    private fun applyContrastEnhancement() {
        val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (bitmap != null) {
            val enhancedBitmap = imageFilterProcessor.applyContrast(bitmap, 1.3f)
            binding.frozenImageView.setImageBitmap(enhancedBitmap)
            Toast.makeText(this, "已增强对比度", Toast.LENGTH_SHORT).show()
        }
    }

    private fun applyBrightnessAdjustment() {
        val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (bitmap != null) {
            val enhancedBitmap = imageFilterProcessor.applyBrightness(bitmap, 1.2f)
            binding.frozenImageView.setImageBitmap(enhancedBitmap)
            Toast.makeText(this, "已调节亮度", Toast.LENGTH_SHORT).show()
        }
    }

    private fun applyNoiseReduction() {
        val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (bitmap != null) {
            val enhancedBitmap = imageFilterProcessor.applyBlur(bitmap, 0.5f)
            binding.frozenImageView.setImageBitmap(enhancedBitmap)
            Toast.makeText(this, "已应用降噪", Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupLongPressZoom() {
        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        var zoomInRunnable: Runnable? = null
        var zoomOutRunnable: Runnable? = null

        // 放大按钮长按
        binding.zoomInButton.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    zoomInRunnable = object : Runnable {
                        override fun run() {
                            zoomIn()
                            handler.postDelayed(this, 100) // 每100ms放大一次
                        }
                    }
                    handler.postDelayed(zoomInRunnable!!, 500) // 500ms后开始连续放大
                    false
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    zoomInRunnable?.let { handler.removeCallbacks(it) }
                    false
                }
                else -> false
            }
        }

        // 缩小按钮长按
        binding.zoomOutButton.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    zoomOutRunnable = object : Runnable {
                        override fun run() {
                            zoomOut()
                            handler.postDelayed(this, 100) // 每100ms缩小一次
                        }
                    }
                    handler.postDelayed(zoomOutRunnable!!, 500) // 500ms后开始连续缩小
                    false
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    zoomOutRunnable?.let { handler.removeCallbacks(it) }
                    false
                }
                else -> false
            }
        }
    }

    private fun toggleFlashlight() {
        camera?.let { cam ->
            isFlashlightOn = !isFlashlightOn
            cam.cameraControl.enableTorch(isFlashlightOn)

            val message = if (isFlashlightOn) {
                getString(R.string.flashlight_on)
            } else {
                getString(R.string.flashlight_off)
            }
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
            updateUI()
        }
    }

    private fun switchMode() {
        modeManager.switchToNextMode()
        updateUI()
    }

    private fun applyModeConfiguration(config: ModeManager.ModeConfiguration) {
        // 应用滤镜
        imageFilterProcessor.applyFilterToPreview(binding.previewView, config.filterType)

        // 设置防抖
        isStabilizationEnabled = config.stabilizationEnabled
        stabilizationController.setEnabled(config.stabilizationEnabled)

        // 设置闪光灯
        if (config.flashlightEnabled && !isFlashlightOn) {
            toggleFlashlight()
        } else if (!config.flashlightEnabled && isFlashlightOn) {
            toggleFlashlight()
        }

        // 设置亮度
        brightnessController.setBrightness(config.brightnessLevel)
    }

    private fun updateUI() {
        updateZoomDisplay()
        updateModeDisplay()
        updateControlsVisibility()
    }

    private fun updateZoomDisplay() {
        binding.zoomLevelText.text = getString(R.string.zoom_level, currentZoomRatio.toInt())

        // 更新防抖控制器的缩放级别
        if (::stabilizationController.isInitialized) {
            stabilizationController.setZoomLevel(currentZoomRatio)
        }
    }

    private fun updateModeDisplay() {
        val currentMode = modeManager.getCurrentMode()
        val modeText = getString(currentMode.displayNameRes)

        // 可以在这里更新模式指示器
        // 例如：binding.modeIndicator.text = modeText
    }

    private fun updateControlsVisibility() {
        // 根据当前状态更新控件可见性
        binding.bottomControlLayout.visibility = if (isFrameFrozen) View.GONE else View.VISIBLE
    }

    // 传感器监听现在由StabilizationController处理
    private fun enableSensorListening() {
        if (isStabilizationEnabled && ::stabilizationController.isInitialized) {
            stabilizationController.startStabilization()
        }
    }

    private fun disableSensorListening() {
        if (::stabilizationController.isInitialized) {
            stabilizationController.stopStabilization()
        }
    }

    // 传感器事件现在由StabilizationController处理
    override fun onSensorChanged(event: SensorEvent?) {
        // 这个方法现在不需要了，因为StabilizationController处理传感器事件
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // 传感器精度变化处理
    }

    override fun onResume() {
        super.onResume()
        if (isStabilizationEnabled) {
            enableSensorListening()
        }
    }

    override fun onPause() {
        super.onPause()
        disableSensorListening()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::cameraExecutor.isInitialized) {
            cameraExecutor.shutdown()
        }
        disableSensorListening()
        if (::zoomController.isInitialized) {
            zoomController.cleanup()
        }
        if (::gestureHandler.isInitialized) {
            gestureHandler.cleanup()
        }
        if (::brightnessController.isInitialized) {
            brightnessController.cleanup()
        }
        if (::stabilizationController.isInitialized) {
            stabilizationController.cleanup()
        }
        if (::performanceMonitor.isInitialized) {
            performanceMonitor.stopMonitoring()
        }
    }
}
