package com.magnifyingglass.app

import android.Manifest
import android.content.pm.PackageManager
import android.graphics.*
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.SeekBar
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.magnifyingglass.app.databinding.ActivityMainBinding
import com.magnifyingglass.app.utils.*
import kotlinx.coroutines.launch
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 完整功能的MainActivity
 */
class MainActivity : AppCompatActivity(), SensorEventListener {

    private lateinit var binding: ActivityMainBinding
    private lateinit var cameraExecutor: ExecutorService
    private lateinit var camera: androidx.camera.core.Camera

    // 状态变量
    private var currentZoomRatio = 1.0f
    private var isFrameFrozen = false
    private var isFlashlightOn = false

    // 权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            startCamera()
        } else {
            Toast.makeText(this, "需要相机权限才能使用应用", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 检查相机权限
        if (allPermissionsGranted()) {
            startCamera()
        } else {
            requestPermissionLauncher.launch(Manifest.permission.CAMERA)
        }

        cameraExecutor = Executors.newSingleThreadExecutor()

        setupButtonListeners()
        setupGestureHandling()
    }

    private fun setupGestureHandling() {
        // 简化的手势处理
        binding.previewView.setOnLongClickListener {
            if (!isFrameFrozen) {
                freezeFrame()
            } else {
                unfreezeFrame()
            }
            true
        }

        binding.previewView.setOnClickListener { view ->
            // 单击对焦
            val x = view.width / 2f
            val y = view.height / 2f
            focusOnPoint(x, y)
        }
    }

    private fun setupButtonListeners() {
        binding.flashlightButton.setOnClickListener { toggleFlashlight() }
        binding.saveButton.setOnClickListener { saveCurrentFrame() }

        // 如果有缩放按钮
        try {
            binding.zoomInButton.setOnClickListener { zoomIn() }
            binding.zoomOutButton.setOnClickListener { zoomOut() }
        } catch (e: Exception) {
            // 缩放按钮不存在
        }
    }

    private fun allPermissionsGranted() = ContextCompat.checkSelfPermission(
        this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED

    private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            val cameraProvider: ProcessCameraProvider = cameraProviderFuture.get()

            val preview = Preview.Builder().build().also {
                it.setSurfaceProvider(binding.previewView.surfaceProvider)
            }

            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

            try {
                cameraProvider.unbindAll()
                camera = cameraProvider.bindToLifecycle(
                    this, cameraSelector, preview)

            } catch(exc: Exception) {
                Toast.makeText(this, "相机启动失败", Toast.LENGTH_SHORT).show()
            }

        }, ContextCompat.getMainExecutor(this))
    }

    private fun zoomIn() {
        if (::camera.isInitialized) {
            val currentZoom = camera.cameraInfo.zoomState.value?.zoomRatio ?: 1f
            val newZoom = (currentZoom * 1.2f).coerceAtMost(10f)
            camera.cameraControl.setZoomRatio(newZoom)
        }
    }

    private fun zoomOut() {
        if (::camera.isInitialized) {
            val currentZoom = camera.cameraInfo.zoomState.value?.zoomRatio ?: 1f
            val newZoom = (currentZoom / 1.2f).coerceAtLeast(0.1f)
            camera.cameraControl.setZoomRatio(newZoom)
        }
    }

    private fun focusOnPoint(x: Float, y: Float) {
        if (::camera.isInitialized) {
            val factory = binding.previewView.meteringPointFactory
            val point = factory.createPoint(x, y)
            val action = FocusMeteringAction.Builder(point).build()
            camera.cameraControl.startFocusAndMetering(action)
        }
    }

    private fun toggleFlashlight() {
        if (::camera.isInitialized) {
            isFlashlightOn = !isFlashlightOn
            camera.cameraControl.enableTorch(isFlashlightOn)
            
            val message = if (isFlashlightOn) {
                "闪光灯已开启"
            } else {
                "闪光灯已关闭"
            }
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        }
    }

    private fun freezeFrame() {
        isFrameFrozen = true

        // 捕获当前画面
        val bitmap = captureCurrentFrame()
        if (bitmap != null) {
            binding.frozenImageView.setImageBitmap(bitmap)
            binding.frozenImageView.visibility = View.VISIBLE
            binding.frozenButtonsContainer.visibility = View.VISIBLE
            binding.statusText.visibility = View.VISIBLE
            binding.statusText.text = "画面已冻结"
            Toast.makeText(this, "画面已冻结，长按解冻", Toast.LENGTH_SHORT).show()
        }

        // gestureHandler.setEnabled(false)
    }

    private fun unfreezeFrame() {
        isFrameFrozen = false
        binding.frozenImageView.visibility = View.GONE
        binding.frozenButtonsContainer.visibility = View.GONE
        binding.statusText.visibility = View.GONE
        hideEnhancementSliders()
        Toast.makeText(this, "画面已解冻", Toast.LENGTH_SHORT).show()

        // gestureHandler.setEnabled(true)
    }

    private fun captureCurrentFrame(): Bitmap? {
        return try {
            val bitmap = Bitmap.createBitmap(
                binding.previewView.width,
                binding.previewView.height,
                Bitmap.Config.ARGB_8888
            )
            val canvas = Canvas(bitmap)
            binding.previewView.draw(canvas)
            bitmap
        } catch (e: Exception) {
            null
        }
    }

    private fun saveCurrentFrame() {
        if (isFrameFrozen) {
            val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
            if (bitmap != null) {
                Toast.makeText(this, "图像已保存", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(this, "请先冻结画面再保存", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showEnhancementSliders() {
        val slidersContainer = binding.enhancementSlidersContainer
        slidersContainer.visibility = View.VISIBLE

        val brightnessSlider = binding.brightnessSlider
        val contrastSlider = binding.contrastSlider
        val saturationSlider = binding.saturationSlider

        // 设置滑动条监听器
        brightnessSlider.setOnSeekBarChangeListener(createSliderListener())
        contrastSlider.setOnSeekBarChangeListener(createSliderListener())
        saturationSlider.setOnSeekBarChangeListener(createSliderListener())
    }

    private fun hideEnhancementSliders() {
        binding.enhancementSlidersContainer.visibility = View.GONE
    }

    private fun createSliderListener() = object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser && isFrameFrozen) {
                applyImageEnhancement()
            }
        }
        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    }

    private fun applyImageEnhancement() {
        val frozenImageView = binding.frozenImageView
        val brightness = (binding.brightnessSlider.progress - 50) / 50f
        val contrast = binding.contrastSlider.progress / 50f
        val saturation = binding.saturationSlider.progress / 50f

        val colorMatrix = ColorMatrix()

        // 应用亮度
        val brightnessMatrix = ColorMatrix()
        brightnessMatrix.setScale(1f + brightness, 1f + brightness, 1f + brightness, 1f)
        colorMatrix.postConcat(brightnessMatrix)

        // 应用对比度
        val contrastMatrix = ColorMatrix()
        val scale = contrast
        val translate = (1f - contrast) * 0.5f
        contrastMatrix.set(floatArrayOf(
            scale, 0f, 0f, 0f, translate * 255,
            0f, scale, 0f, 0f, translate * 255,
            0f, 0f, scale, 0f, translate * 255,
            0f, 0f, 0f, 1f, 0f
        ))
        colorMatrix.postConcat(contrastMatrix)

        // 应用饱和度
        val saturationMatrix = ColorMatrix()
        saturationMatrix.setSaturation(saturation)
        colorMatrix.postConcat(saturationMatrix)

        val colorFilter = ColorMatrixColorFilter(colorMatrix)
        frozenImageView.colorFilter = colorFilter
    }

    override fun onSensorChanged(event: SensorEvent?) {
        // 传感器事件处理
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // 传感器精度变化处理
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::cameraExecutor.isInitialized) {
            cameraExecutor.shutdown()
        }
    }
}
