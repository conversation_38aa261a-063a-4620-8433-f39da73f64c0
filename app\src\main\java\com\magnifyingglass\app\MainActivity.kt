package com.magnifyingglass.app

import android.Manifest
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.Bundle
import android.provider.MediaStore
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.SeekBar
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import com.magnifyingglass.app.databinding.ActivityMainBinding
import com.magnifyingglass.app.utils.CameraController
import com.magnifyingglass.app.utils.ImageFilterProcessor
import com.magnifyingglass.app.utils.PermissionHelper
import com.magnifyingglass.app.utils.RealESRGANProcessor
import com.magnifyingglass.app.utils.TrueRealESRGANProcessor
import com.magnifyingglass.app.utils.ZoomController
import com.magnifyingglass.app.utils.AutoTestManager
import com.magnifyingglass.app.utils.GestureHandler
import com.magnifyingglass.app.utils.BrightnessController
import com.magnifyingglass.app.utils.StabilizationController
import com.magnifyingglass.app.utils.ImageSaver
import com.magnifyingglass.app.utils.ModeManager
import com.magnifyingglass.app.utils.PermissionDialogManager
import com.magnifyingglass.app.utils.PerformanceMonitor
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

class MainActivity : AppCompatActivity(), SensorEventListener {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var cameraController: CameraController
    private lateinit var imageFilterProcessor: ImageFilterProcessor
    private lateinit var permissionHelper: PermissionHelper
    private lateinit var zoomController: ZoomController
    private lateinit var gestureHandler: GestureHandler
    private lateinit var brightnessController: BrightnessController
    private lateinit var stabilizationController: StabilizationController
    private lateinit var imageSaver: ImageSaver
    private lateinit var modeManager: ModeManager
    private lateinit var permissionDialogManager: PermissionDialogManager
    private lateinit var performanceMonitor: PerformanceMonitor
    private lateinit var realESRGANProcessor: RealESRGANProcessor
    private lateinit var trueRealESRGANProcessor: TrueRealESRGANProcessor
    private lateinit var autoTestManager: AutoTestManager
    
    // 相机相关
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var preview: Preview? = null
    private var imageCapture: ImageCapture? = null
    private lateinit var cameraExecutor: ExecutorService
    
    // 手势检测 (现在由GestureHandler处理)
    
    // 传感器相关
    private lateinit var sensorManager: SensorManager
    private var gyroscope: Sensor? = null
    private var accelerometer: Sensor? = null
    
    // 状态变量
    private var currentZoomRatio = 1.0f
    private var previousZoomRatio = 1.0f
    private var currentBrightness = 100
    private var isFlashlightOn = false
    private var isFrameFrozen = false
    private var isStabilizationEnabled = true
    
    // 常量
    companion object {
        private const val MIN_ZOOM_RATIO = 1.0f
        private const val MAX_ZOOM_RATIO = 16.0f
        private const val ZOOM_STEP = 0.5f
        private const val BRIGHTNESS_STEP = 10
        private const val EDGE_THRESHOLD = 100 // 边缘滑动检测阈值
        private const val LONG_PRESS_TIMEOUT = 500L
    }
    
    // CameraMode现在由ModeManager管理
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 检查是否需要显示引导页面
        if (shouldShowOnboarding()) {
            startActivity(Intent(this, OnboardingActivity::class.java))
            finish()
            return
        }
        
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        initializeComponents()
        setupUI()

        // 开始性能监控
        performanceMonitor.startMonitoring()

        checkPermissionsAndStartCamera()
    }
    
    private fun shouldShowOnboarding(): Boolean {
        val prefs = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        return !prefs.getBoolean("onboarding_completed", false)
    }
    
    private fun initializeComponents() {
        cameraExecutor = Executors.newSingleThreadExecutor()
        cameraController = CameraController(this)
        imageFilterProcessor = ImageFilterProcessor()
        permissionHelper = PermissionHelper(this)
        imageSaver = ImageSaver(this)
        permissionDialogManager = PermissionDialogManager(this)
        realESRGANProcessor = RealESRGANProcessor(this)
        trueRealESRGANProcessor = TrueRealESRGANProcessor(this)
        autoTestManager = AutoTestManager(this)

        // 初始化性能监控
        performanceMonitor = PerformanceMonitor(this)
        performanceMonitor.setPerformanceListener(object : PerformanceMonitor.PerformanceListener {
            override fun onMemoryWarning(usedMemoryMB: Long, totalMemoryMB: Long) {
                // 内存警告，可以清理一些缓存
                performanceMonitor.forceGarbageCollection()
            }

            override fun onLowMemory() {
                // 系统低内存，释放非必要资源
                if (::imageFilterProcessor.isInitialized) {
                    // 可以清理滤镜缓存等
                }
            }

            override fun onPerformanceReport(report: PerformanceMonitor.PerformanceReport) {
                // 性能报告，可以用于调试
                android.util.Log.d("Performance", "APK Size: ${report.apkSizeMB}MB")
            }
        })

        // 初始化模式管理器
        modeManager = ModeManager(this)
        modeManager.setModeChangeListener(object : ModeManager.ModeChangeListener {
            override fun onModeChanged(newMode: ModeManager.CameraMode, oldMode: ModeManager.CameraMode) {
                updateModeDisplay()
            }

            override fun onModeConfigurationChanged(mode: ModeManager.CameraMode, config: ModeManager.ModeConfiguration) {
                applyModeConfiguration(config)
            }
        })

        // 初始化亮度控制器
        brightnessController = BrightnessController(this, binding.previewView)
        brightnessController.setBrightnessListener(object : BrightnessController.BrightnessListener {
            override fun onBrightnessChanged(brightness: Int) {
                currentBrightness = brightness
                binding.brightnessText.text = getString(R.string.brightness_level, brightness)
            }

            override fun onLowLightDetected() {
                // 显示低光环境提示
                showLowLightDialog()
            }

            override fun onBrightnessIndicatorVisibilityChanged(visible: Boolean) {
                binding.brightnessText.visibility = if (visible) View.VISIBLE else View.GONE
            }
        })

        // 初始化手势处理器
        gestureHandler = GestureHandler(this, object : GestureHandler.GestureListener {
            override fun onZoomGesture(scaleFactor: Float) {
                if (::zoomController.isInitialized) {
                    zoomController.handlePinchZoom(scaleFactor)
                }
            }

            override fun onDoubleTap() {
                if (::zoomController.isInitialized) {
                    zoomController.toggleZoom()
                }
            }

            override fun onLongPress() {
                if (!isFrameFrozen) {
                    freezeFrame()
                    showEnhancementSliders()
                }
            }

            override fun onSingleTap(x: Float, y: Float) {
                focusOnPoint(x, y)
            }

            override fun onBrightnessAdjust(delta: Int) {
                brightnessController.adjustBrightness(delta)
            }

            override fun onEdgeSwipeStart() {
                // 边缘滑动开始，可以显示亮度指示器
            }

            override fun onEdgeSwipeEnd() {
                // 边缘滑动结束
            }
        })

        // 初始化防抖控制器
        stabilizationController = StabilizationController(this)
        stabilizationController.setStabilizationListener(object : StabilizationController.StabilizationListener {
            override fun onStabilizationStateChanged(isStable: Boolean) {
                // 可以在这里更新UI显示稳定性状态
                runOnUiThread {
                    // 例如：显示稳定性指示器
                }
            }

            override fun onMovementDetected(intensity: Float) {
                // 可以根据运动强度调整对焦频率等
            }
        })

        // 初始化传感器
        sensorManager = getSystemService(Context.SENSOR_SERVICE) as SensorManager
        gyroscope = sensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE)
        accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
    }
    
    private fun setupUI() {
        // 设置触摸监听器
        binding.previewView.setOnTouchListener { view, event ->
            gestureHandler.onTouchEvent(event, view)
        }
        
        // 设置按钮点击监听器
        binding.zoomInButton.setOnClickListener { zoomIn() }
        binding.zoomOutButton.setOnClickListener { zoomOut() }

        // 设置长按监听器实现连续放大缩小
        setupLongPressZoom()
        binding.freezeButton.setOnClickListener { toggleFreeze() }
        binding.saveButton.setOnClickListener { saveImage() }
        binding.backButton.setOnClickListener { unfreezeFrame() }
        binding.enhanceButton.setOnClickListener { showEnhanceOptions() }
        binding.realESRGANButton.setOnClickListener { applyRealESRGANEnhancement() }
        binding.flashlightButton.setOnClickListener { toggleFlashlight() }
        binding.modeButton.setOnClickListener { switchMode() }

        // 设置自动化测试（长按冻结按钮启动）
        binding.freezeButton.setOnLongClickListener {
            showAutoTestDialog()
            true
        }

        updateUI()
    }
    
    private fun checkPermissionsAndStartCamera() {
        if (permissionHelper.hasCameraPermission()) {
            startCamera()
        } else {
            if (permissionHelper.shouldShowCameraPermissionRationale(this)) {
                // 显示权限说明对话框
                permissionDialogManager.showPermissionRationaleDialog("camera",
                    object : PermissionDialogManager.PermissionDialogListener {
                        override fun onPermissionGranted() {
                            requestCameraPermission()
                        }

                        override fun onPermissionDenied() {
                            showPermissionDeniedDialog()
                        }

                        override fun onSettingsRequested() {
                            // 用户前往设置页面
                        }
                    })
            } else {
                requestCameraPermission()
            }
        }
    }
    
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            startCamera()
        } else {
            showPermissionDeniedDialog()
        }
    }
    
    private fun requestCameraPermission() {
        requestPermissionLauncher.launch(android.Manifest.permission.CAMERA)
    }
    
    private fun showPermissionDeniedDialog() {
        permissionDialogManager.showFeatureLimitationDialog("camera")
        finish()
    }

    private fun showLowLightDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(getString(R.string.low_light_detected))
            .setMessage(getString(R.string.low_light_detected))
            .setPositiveButton(getString(R.string.enable_max_brightness)) { _, _ ->
                brightnessController.setMaxBrightness()
                if (!isFlashlightOn) {
                    toggleFlashlight()
                }
            }
            .setNegativeButton(getString(R.string.dismiss), null)
            .show()
    }
    
    private fun startCamera() {
        performanceMonitor.recordCameraInitStart()

        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                bindCameraUseCases()

                performanceMonitor.recordCameraInitComplete()
                
                // 启用传感器监听
                if (isStabilizationEnabled) {
                    enableSensorListening()
                }
                
            } catch (exc: Exception) {
                Toast.makeText(this, getString(R.string.camera_error), Toast.LENGTH_SHORT).show()
            }
        }, ContextCompat.getMainExecutor(this))
    }
    
    private fun bindCameraUseCases() {
        val cameraProvider = cameraProvider ?: return

        // 预览用例 - 优化配置
        preview = Preview.Builder()
            .setTargetAspectRatio(androidx.camera.core.AspectRatio.RATIO_4_3)
            .build().also {
                it.setSurfaceProvider(binding.previewView.surfaceProvider)
            }

        // 图像捕获用例 - 优化配置
        imageCapture = ImageCapture.Builder()
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY) // 改为最大质量
            .setTargetAspectRatio(androidx.camera.core.AspectRatio.RATIO_4_3)
            .setJpegQuality(95) // 设置高质量JPEG
            .build()

        // 选择后置摄像头
        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

        try {
            // 解绑所有用例
            cameraProvider.unbindAll()

            // 绑定用例到生命周期
            camera = cameraProvider.bindToLifecycle(
                this, cameraSelector, preview, imageCapture
            )

            // 初始化变焦控制器
            camera?.let { cam ->
                zoomController = ZoomController(cam, this)
                zoomController.setZoomListener(object : ZoomController.ZoomListener {
                    override fun onZoomChanged(zoomRatio: Float) {
                        currentZoomRatio = zoomRatio
                        updateZoomDisplay()
                    }

                    override fun onZoomLimitsChanged(minZoom: Float, maxZoom: Float) {
                        // 更新UI中的变焦限制显示
                    }
                })

                // 设置相机到亮度控制器
                brightnessController.setCamera(cam)

                // 设置相机到防抖控制器
                stabilizationController.setCamera(cam)

                // 启用连续自动对焦
                enableContinuousAutoFocus()
            }
            
        } catch (exc: Exception) {
            Toast.makeText(this, getString(R.string.camera_not_available), Toast.LENGTH_SHORT).show()
        }
    }
    
    // 手势处理现在由GestureHandler类处理
    
    // 亮度调节现在由BrightnessController处理

    private fun zoomIn() {
        if (::zoomController.isInitialized) {
            zoomController.zoomIn()
        }
    }

    private fun zoomOut() {
        if (::zoomController.isInitialized) {
            zoomController.zoomOut()
        }
    }

    private fun focusOnPoint(x: Float, y: Float) {
        val factory = binding.previewView.meteringPointFactory
        val point = factory.createPoint(x, y)

        // 创建对焦和测光动作，包括自动对焦、自动曝光和自动白平衡
        val action = FocusMeteringAction.Builder(point)
            .addPoint(point, FocusMeteringAction.FLAG_AF) // 自动对焦
            .addPoint(point, FocusMeteringAction.FLAG_AE) // 自动曝光
            .addPoint(point, FocusMeteringAction.FLAG_AWB) // 自动白平衡
            .setAutoCancelDuration(5, java.util.concurrent.TimeUnit.SECONDS) // 5秒后自动取消
            .build()

        camera?.cameraControl?.startFocusAndMetering(action)?.addListener({
            // 对焦完成，显示对焦成功指示
            showFocusIndicator(x, y)
        }, ContextCompat.getMainExecutor(this))
    }

    /**
     * 显示对焦指示器
     */
    private fun showFocusIndicator(x: Float, y: Float) {
        // 创建一个简单的对焦指示器动画
        val focusIndicator = View(this)
        focusIndicator.setBackgroundResource(R.drawable.focus_indicator)
        focusIndicator.alpha = 0.7f

        // 设置指示器位置和大小
        val size = resources.getDimensionPixelSize(R.dimen.focus_indicator_size)
        val params = android.widget.FrameLayout.LayoutParams(size, size)

        // 获取PreviewView在屏幕中的位置
        val location = IntArray(2)
        binding.previewView.getLocationOnScreen(location)
        val previewX = location[0]
        val previewY = location[1]

        // 计算相对于整个屏幕的位置
        params.leftMargin = (previewX + x).toInt() - size / 2
        params.topMargin = (previewY + y).toInt() - size / 2

        // 添加到根布局
        val rootView = findViewById<android.widget.FrameLayout>(android.R.id.content)
        rootView.addView(focusIndicator, params)

        // 创建动画
        focusIndicator.animate()
            .scaleX(1.2f).scaleY(1.2f)
            .setDuration(300)
            .withEndAction {
                focusIndicator.animate()
                    .alpha(0f).scaleX(0.8f).scaleY(0.8f)
                    .setDuration(300)
                    .withEndAction {
                        rootView.removeView(focusIndicator)
                    }
                    .start()
            }
            .start()
    }

    /**
     * 启用连续自动对焦
     */
    private fun enableContinuousAutoFocus() {
        camera?.let { cam ->
            try {
                // 创建屏幕中心点
                val factory = SurfaceOrientedMeteringPointFactory(1f, 1f)
                val centerPoint = factory.createPoint(0.5f, 0.5f)

                // 创建连续自动对焦动作
                val action = FocusMeteringAction.Builder(centerPoint)
                    .disableAutoCancel() // 不自动取消
                    .build()

                cam.cameraControl.startFocusAndMetering(action)

                android.util.Log.d("MainActivity", "Continuous auto focus enabled")
            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "Failed to enable continuous auto focus", e)
            }
        }
    }

    private fun toggleFreeze() {
        if (isFrameFrozen) {
            unfreezeFrame()
        } else {
            freezeFrame()
        }
    }

    private fun freezeFrame() {
        // 捕获当前预览帧
        val bitmap = binding.previewView.getBitmap()
        if (bitmap != null) {
            // 应用当前滤镜到捕获的图像
            val currentConfig = modeManager.getCurrentModeConfiguration()
            val filteredBitmap = if (currentConfig.filterType != ImageFilterProcessor.FilterType.NONE) {
                imageFilterProcessor.applyFilterToBitmap(bitmap, currentConfig.filterType)
            } else {
                bitmap
            }

            binding.frozenImageView.setImageBitmap(filteredBitmap)
            binding.frozenImageView.visibility = View.VISIBLE
            binding.frozenButtonsContainer.visibility = View.VISIBLE
            binding.statusText.visibility = View.VISIBLE
            binding.statusText.text = getString(R.string.frame_frozen)

            isFrameFrozen = true
            updateUI()

            // 冻结时禁用相机预览但保留手势用于解冻
            binding.previewView.visibility = View.INVISIBLE
            // 不完全禁用手势，保留长按解冻功能

            // 为冻结图像添加长按解冻功能
            binding.frozenImageView.setOnLongClickListener {
                unfreezeFrame()
                true
            }

            Toast.makeText(this, getString(R.string.frame_frozen), Toast.LENGTH_SHORT).show()
        }
    }

    private fun unfreezeFrame() {
        binding.frozenImageView.visibility = View.GONE
        binding.frozenButtonsContainer.visibility = View.GONE
        binding.statusText.visibility = View.GONE
        hideEnhancementSliders() // 隐藏滑动条

        // 恢复相机预览和手势
        binding.previewView.visibility = View.VISIBLE
        gestureHandler.setEnabled(true) // 启用手势

        isFrameFrozen = false
        updateUI()

        Toast.makeText(this, getString(R.string.frame_unfrozen), Toast.LENGTH_SHORT).show()
    }

    private fun saveImage() {
        val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (bitmap != null) {
            // 检查存储权限（Android 10以下需要）
            if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.Q) {
                if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED) {
                    requestStoragePermission()
                    return
                }
            }

            lifecycleScope.launch {
                try {
                    // 检查存储空间
                    val estimatedSize = imageSaver.estimateImageSize(bitmap)
                    if (!imageSaver.checkStorageSpace(estimatedSize)) {
                        runOnUiThread {
                            Toast.makeText(this@MainActivity, "存储空间不足", Toast.LENGTH_SHORT).show()
                        }
                        return@launch
                    }

                    imageSaver.saveImageToGallery(
                        bitmap = bitmap,
                        listener = object : ImageSaver.SaveListener {
                            override fun onSaveSuccess(uri: android.net.Uri?, filePath: String?) {
                                runOnUiThread {
                                    Toast.makeText(this@MainActivity, getString(R.string.image_saved), Toast.LENGTH_LONG).show()
                                    unfreezeFrame() // 保存成功后解冻
                                }
                            }

                            override fun onSaveError(error: String) {
                                runOnUiThread {
                                    Toast.makeText(this@MainActivity, getString(R.string.image_save_failed), Toast.LENGTH_LONG).show()
                                }
                            }

                            override fun onSaveProgress(progress: Int) {
                                // 可以在这里显示保存进度
                            }
                        }
                    )
                } catch (e: Exception) {
                    runOnUiThread {
                        Toast.makeText(this@MainActivity, "保存失败: ${e.message}", Toast.LENGTH_LONG).show()
                    }
                }
            }
        }
    }

    private fun requestStoragePermission() {
        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.Q) {
            requestPermissionLauncher.launch(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    }

    private fun showEnhanceOptions() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_enhance, null)
        val dialog = androidx.appcompat.app.AlertDialog.Builder(this)
            .setView(dialogView)
            .create()

        // 获取原始图像
        val originalBitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (originalBitmap == null) {
            Toast.makeText(this, "无法获取图像", Toast.LENGTH_SHORT).show()
            return
        }

        var currentBitmap = originalBitmap.copy(Bitmap.Config.ARGB_8888, true)

        // 获取控件
        val sharpenSeekBar = dialogView.findViewById<android.widget.SeekBar>(R.id.sharpenSeekBar)
        val contrastSeekBar = dialogView.findViewById<android.widget.SeekBar>(R.id.contrastSeekBar)
        val brightnessSeekBar = dialogView.findViewById<android.widget.SeekBar>(R.id.brightnessSeekBar)
        val noiseReductionSeekBar = dialogView.findViewById<android.widget.SeekBar>(R.id.noiseReductionSeekBar)

        val sharpenValue = dialogView.findViewById<android.widget.TextView>(R.id.sharpenValue)
        val contrastValue = dialogView.findViewById<android.widget.TextView>(R.id.contrastValue)
        val brightnessValue = dialogView.findViewById<android.widget.TextView>(R.id.brightnessValue)
        val noiseReductionValue = dialogView.findViewById<android.widget.TextView>(R.id.noiseReductionValue)

        val resetButton = dialogView.findViewById<android.widget.Button>(R.id.resetButton)
        val applyButton = dialogView.findViewById<android.widget.Button>(R.id.applyButton)
        val cancelButton = dialogView.findViewById<android.widget.Button>(R.id.cancelButton)

        // 实时预览功能
        val updatePreview = {
            val sharpen = sharpenSeekBar.progress / 100f
            val contrast = 0.5f + (contrastSeekBar.progress / 100f)
            val brightness = 0.5f + (brightnessSeekBar.progress / 100f)
            val noise = noiseReductionSeekBar.progress / 100f

            currentBitmap = applyEnhancements(originalBitmap, sharpen, contrast, brightness, noise)
            binding.frozenImageView.setImageBitmap(currentBitmap)

            sharpenValue.text = sharpenSeekBar.progress.toString()
            contrastValue.text = contrastSeekBar.progress.toString()
            brightnessValue.text = brightnessSeekBar.progress.toString()
            noiseReductionValue.text = noiseReductionSeekBar.progress.toString()
        }

        // 设置监听器
        sharpenSeekBar.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) updatePreview()
            }
            override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {}
        })

        contrastSeekBar.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) updatePreview()
            }
            override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {}
        })

        brightnessSeekBar.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) updatePreview()
            }
            override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {}
        })

        noiseReductionSeekBar.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) updatePreview()
            }
            override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {}
        })

        // 按钮事件
        resetButton.setOnClickListener {
            sharpenSeekBar.progress = 0
            contrastSeekBar.progress = 50
            brightnessSeekBar.progress = 50
            noiseReductionSeekBar.progress = 0
            updatePreview()
        }

        applyButton.setOnClickListener {
            Toast.makeText(this, "增强效果已应用", Toast.LENGTH_SHORT).show()
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            binding.frozenImageView.setImageBitmap(originalBitmap)
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun applySharpening() {
        val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (bitmap != null) {
            val enhancedBitmap = imageFilterProcessor.applySharpen(bitmap)
            binding.frozenImageView.setImageBitmap(enhancedBitmap)
            Toast.makeText(this, "已应用锐化效果", Toast.LENGTH_SHORT).show()
        }
    }

    private fun applyContrastEnhancement() {
        val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (bitmap != null) {
            val enhancedBitmap = imageFilterProcessor.applyContrast(bitmap, 1.3f)
            binding.frozenImageView.setImageBitmap(enhancedBitmap)
            Toast.makeText(this, "已增强对比度", Toast.LENGTH_SHORT).show()
        }
    }

    private fun applyBrightnessAdjustment() {
        val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (bitmap != null) {
            val enhancedBitmap = imageFilterProcessor.applyBrightness(bitmap, 1.2f)
            binding.frozenImageView.setImageBitmap(enhancedBitmap)
            Toast.makeText(this, "已调节亮度", Toast.LENGTH_SHORT).show()
        }
    }

    private fun applyNoiseReduction() {
        val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
        if (bitmap != null) {
            val enhancedBitmap = imageFilterProcessor.applyBlur(bitmap, 0.5f)
            binding.frozenImageView.setImageBitmap(enhancedBitmap)
            Toast.makeText(this, "已应用降噪", Toast.LENGTH_SHORT).show()
        }
    }

    private fun applyEnhancements(bitmap: Bitmap, sharpen: Float, contrast: Float, brightness: Float, noise: Float): Bitmap {
        var result = bitmap.copy(Bitmap.Config.ARGB_8888, true)

        // 应用锐化
        if (sharpen > 0) {
            result = imageFilterProcessor.applySharpen(result, sharpen)
        }

        // 应用对比度
        if (contrast != 1.0f) {
            result = imageFilterProcessor.applyContrast(result, contrast)
        }

        // 应用亮度
        if (brightness != 1.0f) {
            result = imageFilterProcessor.applyBrightness(result, brightness)
        }

        // 应用降噪
        if (noise > 0) {
            result = imageFilterProcessor.applyBlur(result, noise)
        }

        return result
    }

    private fun setupLongPressZoom() {
        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        var zoomInRunnable: Runnable? = null
        var zoomOutRunnable: Runnable? = null

        // 放大按钮长按
        binding.zoomInButton.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    zoomInRunnable = object : Runnable {
                        override fun run() {
                            zoomIn()
                            handler.postDelayed(this, 100) // 每100ms放大一次
                        }
                    }
                    handler.postDelayed(zoomInRunnable!!, 500) // 500ms后开始连续放大
                    false
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    zoomInRunnable?.let { handler.removeCallbacks(it) }
                    false
                }
                else -> false
            }
        }

        // 缩小按钮长按
        binding.zoomOutButton.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    zoomOutRunnable = object : Runnable {
                        override fun run() {
                            zoomOut()
                            handler.postDelayed(this, 100) // 每100ms缩小一次
                        }
                    }
                    handler.postDelayed(zoomOutRunnable!!, 500) // 500ms后开始连续缩小
                    false
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    zoomOutRunnable?.let { handler.removeCallbacks(it) }
                    false
                }
                else -> false
            }
        }
    }

    private fun toggleFlashlight() {
        camera?.let { cam ->
            isFlashlightOn = !isFlashlightOn
            cam.cameraControl.enableTorch(isFlashlightOn)

            val message = if (isFlashlightOn) {
                getString(R.string.flashlight_on)
            } else {
                getString(R.string.flashlight_off)
            }
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
            updateUI()
        }
    }

    private fun switchMode() {
        modeManager.switchToNextMode()
        updateUI()
    }

    private fun applyModeConfiguration(config: ModeManager.ModeConfiguration) {
        // 应用滤镜
        imageFilterProcessor.applyFilterToPreview(binding.previewView, config.filterType)

        // 设置防抖
        isStabilizationEnabled = config.stabilizationEnabled
        stabilizationController.setEnabled(config.stabilizationEnabled)

        // 设置闪光灯
        if (config.flashlightEnabled && !isFlashlightOn) {
            toggleFlashlight()
        } else if (!config.flashlightEnabled && isFlashlightOn) {
            toggleFlashlight()
        }

        // 设置亮度
        brightnessController.setBrightness(config.brightnessLevel)
    }

    private fun updateUI() {
        updateZoomDisplay()
        updateModeDisplay()
        updateControlsVisibility()
    }

    private fun updateZoomDisplay() {
        binding.zoomLevelText.text = getString(R.string.zoom_level, currentZoomRatio.toInt())

        // 更新防抖控制器的缩放级别
        if (::stabilizationController.isInitialized) {
            stabilizationController.setZoomLevel(currentZoomRatio)
        }
    }

    private fun updateModeDisplay() {
        val currentMode = modeManager.getCurrentMode()
        val modeText = getString(currentMode.displayNameRes)

        // 可以在这里更新模式指示器
        // 例如：binding.modeIndicator.text = modeText
    }

    private fun updateControlsVisibility() {
        // 根据当前状态更新控件可见性
        binding.bottomControlLayout.visibility = if (isFrameFrozen) View.GONE else View.VISIBLE
    }

    // 传感器监听现在由StabilizationController处理
    private fun enableSensorListening() {
        if (isStabilizationEnabled && ::stabilizationController.isInitialized) {
            stabilizationController.startStabilization()
        }
    }

    private fun disableSensorListening() {
        if (::stabilizationController.isInitialized) {
            stabilizationController.stopStabilization()
        }
    }

    private fun showEnhancementSliders() {
        val slidersContainer = findViewById<LinearLayout>(R.id.enhancementSlidersContainer)
        slidersContainer.visibility = View.VISIBLE

        val brightnessSlider = findViewById<SeekBar>(R.id.brightnessSlider)
        val contrastSlider = findViewById<SeekBar>(R.id.contrastSlider)
        val saturationSlider = findViewById<SeekBar>(R.id.saturationSlider)

        // 设置滑动条监听器，实时调整图像
        brightnessSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser && isFrameFrozen) {
                    val brightness = (progress - 50) / 50f // -1.0 to 1.0
                    applyImageEnhancement(brightness = brightness)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        contrastSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser && isFrameFrozen) {
                    val contrast = progress / 50f // 0.0 to 2.0
                    applyImageEnhancement(contrast = contrast)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        saturationSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser && isFrameFrozen) {
                    val saturation = progress / 50f // 0.0 to 2.0
                    applyImageEnhancement(saturation = saturation)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }

    private fun hideEnhancementSliders() {
        val slidersContainer = findViewById<LinearLayout>(R.id.enhancementSlidersContainer)
        slidersContainer.visibility = View.GONE
    }

    private fun applyImageEnhancement(brightness: Float = 0f, contrast: Float = 1f, saturation: Float = 1f) {
        val frozenImageView = findViewById<ImageView>(R.id.frozenImageView)
        val drawable = frozenImageView.drawable

        if (drawable != null) {
            val colorMatrix = ColorMatrix()

            // 应用亮度
            val brightnessMatrix = ColorMatrix()
            brightnessMatrix.setScale(1f + brightness, 1f + brightness, 1f + brightness, 1f)
            colorMatrix.postConcat(brightnessMatrix)

            // 应用对比度
            val contrastMatrix = ColorMatrix()
            val scale = contrast
            val translate = (1f - contrast) * 0.5f
            contrastMatrix.set(floatArrayOf(
                scale, 0f, 0f, 0f, translate * 255,
                0f, scale, 0f, 0f, translate * 255,
                0f, 0f, scale, 0f, translate * 255,
                0f, 0f, 0f, 1f, 0f
            ))
            colorMatrix.postConcat(contrastMatrix)

            // 应用饱和度
            val saturationMatrix = ColorMatrix()
            saturationMatrix.setSaturation(saturation)
            colorMatrix.postConcat(saturationMatrix)

            val colorFilter = ColorMatrixColorFilter(colorMatrix)
            frozenImageView.colorFilter = colorFilter
        }
    }

    private fun applyRealESRGANEnhancement() {
        if (!isFrameFrozen) {
            Toast.makeText(this, "请先冻结画面再进行超分辨率增强", Toast.LENGTH_SHORT).show()
            return
        }

        // 直接使用Real-ESRGAN增强
        performTrueRealESRGANEnhancement()
    }

    private fun performTrueRealESRGANEnhancement() {
        val frozenImageView = findViewById<ImageView>(R.id.frozenImageView)
        val drawable = frozenImageView.drawable

        if (drawable is android.graphics.drawable.BitmapDrawable) {
            val originalBitmap = drawable.bitmap

            // 高级算法总是可用
            val modelType = TrueRealESRGANProcessor.ModelType.GENERAL

            // 显示进度对话框
            val progressDialog = android.app.ProgressDialog(this).apply {
                setTitle("Real-ESRGAN 超分辨率")
                setMessage("正在使用Real-ESRGAN轻量版模型进行AI增强...")
                setProgressStyle(android.app.ProgressDialog.STYLE_HORIZONTAL)
                max = 100
                setCancelable(false)
                show()
            }

            // 在后台线程进行处理
            lifecycleScope.launch {
                try {
                    // 初始化算法
                    val initialized = trueRealESRGANProcessor.initialize(modelType)
                    if (!initialized) {
                        runOnUiThread {
                            progressDialog.dismiss()
                            Toast.makeText(this@MainActivity, "算法初始化失败", Toast.LENGTH_LONG).show()
                        }
                        return@launch
                    }

                    // 处理图像
                    val enhancedBitmap = trueRealESRGANProcessor.enhanceImage(originalBitmap, 4) { progress ->
                        runOnUiThread {
                            progressDialog.progress = (progress * 100).toInt()
                        }
                    }

                    runOnUiThread {
                        progressDialog.dismiss()
                        if (enhancedBitmap != null) {
                            frozenImageView.setImageBitmap(enhancedBitmap)
                            Toast.makeText(this@MainActivity, "🚀 Real-ESRGAN增强完成！", Toast.LENGTH_SHORT).show()
                        } else {
                            Toast.makeText(this@MainActivity, "Real-ESRGAN增强失败，请重试", Toast.LENGTH_LONG).show()
                        }
                    }

                } catch (e: Exception) {
                    runOnUiThread {
                        progressDialog.dismiss()
                        Toast.makeText(this@MainActivity, "AI增强失败: ${e.message}", Toast.LENGTH_LONG).show()
                    }
                }
            }
        } else {
            Toast.makeText(this, "无法获取图像数据", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showModelDownloadInfo(modelType: TrueRealESRGANProcessor.ModelType) {
        val (title, description) = trueRealESRGANProcessor.getModelDownloadInfo(modelType)

        android.app.AlertDialog.Builder(this)
            .setTitle("模型下载说明")
            .setMessage("$title\n\n$description\n\n由于模型文件较大，建议在WiFi环境下下载。\n\n当前版本需要手动下载模型文件并放置到指定目录。")
            .setPositiveButton("了解更多") { _, _ ->
                // 可以打开帮助页面或GitHub链接
                Toast.makeText(this, "请参考GitHub: xinntao/Real-ESRGAN", Toast.LENGTH_LONG).show()
            }
            .setNegativeButton("关闭", null)
            .show()
    }

    /**
     * 显示自动化测试对话框
     */
    private fun showAutoTestDialog() {
        val functions = autoTestManager.getFunctionList()
        val functionNames = functions.map { "${it.name} - ${it.description}" }.toTypedArray()

        android.app.AlertDialog.Builder(this)
            .setTitle("🤖 自动化测试")
            .setMessage("检测到的可点击功能：\n\n${functions.size}个功能\n\n是否开始全自动测试？")
            .setPositiveButton("开始自动测试") { _, _ ->
                autoTestManager.startAutoTest()
            }
            .setNeutralButton("查看功能列表") { _, _ ->
                showFunctionListDialog(functions)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示功能列表对话框
     */
    private fun showFunctionListDialog(functions: List<AutoTestManager.TestFunction>) {
        val functionList = StringBuilder()
        functionList.append("📱 所有可点击功能清单：\n\n")

        functions.forEachIndexed { index, function ->
            functionList.append("${index + 1}. ${function.name}\n")
            functionList.append("   ${function.description}\n")
            functionList.append("   状态: ${function.result}\n\n")
        }

        android.app.AlertDialog.Builder(this)
            .setTitle("📋 功能清单")
            .setMessage(functionList.toString())
            .setPositiveButton("开始测试") { _, _ ->
                autoTestManager.startAutoTest()
            }
            .setNegativeButton("关闭", null)
            .show()
    }



    // 传感器事件现在由StabilizationController处理
    override fun onSensorChanged(event: SensorEvent?) {
        // 这个方法现在不需要了，因为StabilizationController处理传感器事件
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // 传感器精度变化处理
    }

    override fun onResume() {
        super.onResume()
        if (isStabilizationEnabled) {
            enableSensorListening()
        }
    }

    override fun onPause() {
        super.onPause()
        disableSensorListening()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::cameraExecutor.isInitialized) {
            cameraExecutor.shutdown()
        }
        disableSensorListening()
        if (::zoomController.isInitialized) {
            zoomController.cleanup()
        }
        if (::gestureHandler.isInitialized) {
            gestureHandler.cleanup()
        }
        if (::brightnessController.isInitialized) {
            brightnessController.cleanup()
        }
        if (::stabilizationController.isInitialized) {
            stabilizationController.cleanup()
        }
        if (::performanceMonitor.isInitialized) {
            performanceMonitor.stopMonitoring()
        }
    }
}
