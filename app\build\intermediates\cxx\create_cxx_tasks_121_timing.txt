# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 15ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 10ms
    [gap of 21ms]
  create-initial-cxx-model completed in 111ms
create_cxx_tasks completed in 116ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 59ms]
    create-ARM64_V8A-model 10ms
    [gap of 16ms]
    create-X86_64-model 24ms
  create-initial-cxx-model completed in 111ms
create_cxx_tasks completed in 115ms

