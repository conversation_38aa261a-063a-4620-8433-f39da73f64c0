$com/magnifyingglass/app/MainActivity;com/magnifyingglass/app/MainActivity$initializeComponents$1;com/magnifyingglass/app/MainActivity$initializeComponents$2;com/magnifyingglass/app/MainActivity$initializeComponents$3;com/magnifyingglass/app/MainActivity$initializeComponents$4;com/magnifyingglass/app/MainActivity$initializeComponents$5Ecom/magnifyingglass/app/MainActivity$checkPermissionsAndStartCamera$1;com/magnifyingglass/app/MainActivity$bindCameraUseCases$2$10com/magnifyingglass/app/MainActivity$saveImage$12com/magnifyingglass/app/MainActivity$saveImage$1$1.com/magnifyingglass/app/MainActivity$Companion*com/magnifyingglass/app/OnboardingActivity;com/magnifyingglass/app/OnboardingActivity$setupViewPager$19com/magnifyingglass/app/OnboardingActivity$OnboardingPage<com/magnifyingglass/app/OnboardingActivity$OnboardingAdapterQcom/magnifyingglass/app/OnboardingActivity$OnboardingAdapter$OnboardingViewHolder2com/magnifyingglass/app/utils/BrightnessController<com/magnifyingglass/app/utils/BrightnessController$CompanionEcom/magnifyingglass/app/utils/BrightnessController$BrightnessListener.com/magnifyingglass/app/utils/CameraController8com/magnifyingglass/app/utils/CameraController$Companion9com/magnifyingglass/app/utils/CameraController$DeviceInfo=com/magnifyingglass/app/utils/CameraController$CameraSettings,com/magnifyingglass/app/utils/GestureHandler6com/magnifyingglass/app/utils/GestureHandler$Companion<com/magnifyingglass/app/utils/GestureHandler$GestureListener:com/magnifyingglass/app/utils/GestureHandler$ScaleListenerBcom/magnifyingglass/app/utils/GestureHandler$SimpleGestureListener2com/magnifyingglass/app/utils/ImageFilterProcessor<com/magnifyingglass/app/utils/ImageFilterProcessor$Companion=com/magnifyingglass/app/utils/ImageFilterProcessor$FilterType?com/magnifyingglass/app/utils/ImageFilterProcessor$WhenMappings(com/magnifyingglass/app/utils/ImageSaver=com/magnifyingglass/app/utils/ImageSaver$saveImageToGallery$2=com/magnifyingglass/app/utils/ImageSaver$saveImageToGallery$1;com/magnifyingglass/app/utils/ImageSaver$saveToMediaStore$2;com/magnifyingglass/app/utils/ImageSaver$saveToMediaStore$1@com/magnifyingglass/app/utils/ImageSaver$saveToExternalStorage$2@com/magnifyingglass/app/utils/ImageSaver$saveToExternalStorage$1?com/magnifyingglass/app/utils/ImageSaver$saveToPrivateStorage$2?com/magnifyingglass/app/utils/ImageSaver$saveToPrivateStorage$12com/magnifyingglass/app/utils/ImageSaver$Companion5com/magnifyingglass/app/utils/ImageSaver$SaveListener7com/magnifyingglass/app/utils/ImageSaver$SaveStatistics)com/magnifyingglass/app/utils/ModeManager3com/magnifyingglass/app/utils/ModeManager$Companion4com/magnifyingglass/app/utils/ModeManager$CameraMode<com/magnifyingglass/app/utils/ModeManager$ModeChangeListener;com/magnifyingglass/app/utils/ModeManager$ModeConfiguration7com/magnifyingglass/app/utils/ModeManager$AutoFocusMode6com/magnifyingglass/app/utils/ModeManager$WhenMappings0com/magnifyingglass/app/utils/OnboardingAnimator:com/magnifyingglass/app/utils/OnboardingAnimator$CompanionDcom/magnifyingglass/app/utils/OnboardingAnimator$TransitionDirection<com/magnifyingglass/app/utils/OnboardingAnimator$GestureType=com/magnifyingglass/app/utils/OnboardingAnimator$WhenMappings0com/magnifyingglass/app/utils/PerformanceMonitorHcom/magnifyingglass/app/utils/PerformanceMonitor$startMemoryMonitoring$1:com/magnifyingglass/app/utils/PerformanceMonitor$CompanionDcom/magnifyingglass/app/utils/PerformanceMonitor$PerformanceListenerBcom/magnifyingglass/app/utils/PerformanceMonitor$PerformanceReport5com/magnifyingglass/app/utils/PermissionDialogManagerNcom/magnifyingglass/app/utils/PermissionDialogManager$PermissionDialogListener.com/magnifyingglass/app/utils/PermissionHelper8com/magnifyingglass/app/utils/PermissionHelper$Companion5com/magnifyingglass/app/utils/StabilizationController?com/magnifyingglass/app/utils/StabilizationController$CompanionKcom/magnifyingglass/app/utils/StabilizationController$StabilizationListenerCcom/magnifyingglass/app/utils/StabilizationController$LowPassFilter,com/magnifyingglass/app/utils/ZoomController>com/magnifyingglass/app/utils/ZoomController$animateZoomTo$1$26com/magnifyingglass/app/utils/ZoomController$Companion9com/magnifyingglass/app/utils/ZoomController$ZoomListener                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   