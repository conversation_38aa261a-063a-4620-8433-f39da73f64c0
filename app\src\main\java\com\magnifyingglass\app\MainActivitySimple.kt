package com.magnifyingglass.app

import android.Manifest
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.magnifyingglass.app.databinding.ActivityMainBinding
import com.magnifyingglass.app.utils.*
import kotlinx.coroutines.launch
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 简化版MainActivity - 只保留核心功能
 * 删除了所有增强功能，专注于放大镜基本功能
 */
class MainActivitySimple : AppCompatActivity(), SensorEventListener {

    private lateinit var binding: ActivityMainBinding
    private lateinit var cameraExecutor: ExecutorService
    private lateinit var camera: Camera
    
    // 核心控制器
    private lateinit var zoomController: ZoomController
    private lateinit var gestureHandler: GestureHandler
    private lateinit var brightnessController: BrightnessController
    
    // 状态变量
    private var currentZoomRatio = 1.0f
    private var isFrameFrozen = false
    private var isFlashlightOn = false
    
    // 权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            startCamera()
        } else {
            Toast.makeText(this, "需要相机权限才能使用应用", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 检查相机权限
        if (allPermissionsGranted()) {
            startCamera()
        } else {
            requestPermissionLauncher.launch(Manifest.permission.CAMERA)
        }

        cameraExecutor = Executors.newSingleThreadExecutor()
        
        initializeControllers()
        setupButtonListeners()
    }

    private fun initializeControllers() {
        // 初始化缩放控制器
        zoomController = ZoomController()
        zoomController.setZoomListener(object : ZoomController.ZoomListener {
            override fun onZoomChanged(zoomRatio: Float) {
                currentZoomRatio = zoomRatio
                updateZoomDisplay()
            }
        })

        // 初始化亮度控制器
        brightnessController = BrightnessController(this, binding.previewView)
        brightnessController.setBrightnessListener(object : BrightnessController.BrightnessListener {
            override fun onBrightnessChanged(brightness: Int) {
                binding.brightnessText.text = getString(R.string.brightness_level, brightness)
            }
            override fun onLowLightDetected() {
                // 低光环境检测
            }
            override fun onBrightnessIndicatorVisibilityChanged(visible: Boolean) {
                binding.brightnessText.visibility = if (visible) View.VISIBLE else View.GONE
            }
        })

        // 初始化手势处理器
        gestureHandler = GestureHandler(this, object : GestureHandler.GestureListener {
            override fun onSwipeUp() {
                brightnessController.adjustBrightness(10)
            }
            override fun onSwipeDown() {
                brightnessController.adjustBrightness(-10)
            }
            override fun onLongPress() {
                if (!isFrameFrozen) {
                    freezeFrame()
                    showEnhancementSliders()
                }
            }
            override fun onSingleTap(x: Float, y: Float) {
                focusOnPoint(x, y)
            }
            override fun onBrightnessAdjust(delta: Int) {
                brightnessController.adjustBrightness(delta)
            }
            override fun onEdgeSwipeStart() {
                // 边缘滑动开始
            }
        })

        binding.previewView.setOnTouchListener { _, event ->
            gestureHandler.onTouchEvent(event)
            true
        }
    }

    private fun setupButtonListeners() {
        binding.zoomInButton.setOnClickListener { zoomIn() }
        binding.zoomOutButton.setOnClickListener { zoomOut() }
        binding.flashlightButton.setOnClickListener { toggleFlashlight() }
        binding.saveButton.setOnClickListener { saveCurrentFrame() }
        binding.unfreezeButton.setOnClickListener { unfreezeFrame() }
    }

    private fun allPermissionsGranted() = ContextCompat.checkSelfPermission(
        this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED

    private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            val cameraProvider: ProcessCameraProvider = cameraProviderFuture.get()

            val preview = Preview.Builder().build().also {
                it.setSurfaceProvider(binding.previewView.surfaceProvider)
            }

            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

            try {
                cameraProvider.unbindAll()
                camera = cameraProvider.bindToLifecycle(
                    this, cameraSelector, preview)

                // 设置相机到控制器
                zoomController.setCamera(camera)
                brightnessController.setCamera(camera)

            } catch(exc: Exception) {
                Toast.makeText(this, "相机启动失败", Toast.LENGTH_SHORT).show()
            }

        }, ContextCompat.getMainExecutor(this))
    }

    private fun zoomIn() {
        if (::zoomController.isInitialized) {
            zoomController.zoomIn()
        }
    }

    private fun zoomOut() {
        if (::zoomController.isInitialized) {
            zoomController.zoomOut()
        }
    }

    private fun toggleFlashlight() {
        camera.let { cam ->
            isFlashlightOn = !isFlashlightOn
            cam.cameraControl.enableTorch(isFlashlightOn)
            
            val message = if (isFlashlightOn) {
                getString(R.string.flashlight_on)
            } else {
                getString(R.string.flashlight_off)
            }
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        }
    }

    private fun focusOnPoint(x: Float, y: Float) {
        val factory = binding.previewView.meteringPointFactory
        val point = factory.createPoint(x, y)
        val action = FocusMeteringAction.Builder(point).build()
        camera.cameraControl.startFocusAndMetering(action)
    }

    private fun freezeFrame() {
        isFrameFrozen = true
        
        // 捕获当前画面
        val bitmap = captureCurrentFrame()
        if (bitmap != null) {
            binding.frozenImageView.setImageBitmap(bitmap)
            binding.frozenImageView.visibility = View.VISIBLE
            binding.frozenButtonsContainer.visibility = View.VISIBLE
            binding.statusText.visibility = View.VISIBLE
            binding.statusText.text = "画面已冻结"
        }
        
        gestureHandler.setEnabled(false)
    }

    private fun unfreezeFrame() {
        isFrameFrozen = false
        binding.frozenImageView.visibility = View.GONE
        binding.frozenButtonsContainer.visibility = View.GONE
        binding.statusText.visibility = View.GONE
        hideEnhancementSliders()
        
        gestureHandler.setEnabled(true)
    }

    private fun captureCurrentFrame(): Bitmap? {
        return try {
            val bitmap = Bitmap.createBitmap(
                binding.previewView.width,
                binding.previewView.height,
                Bitmap.Config.ARGB_8888
            )
            val canvas = Canvas(bitmap)
            binding.previewView.draw(canvas)
            bitmap
        } catch (e: Exception) {
            null
        }
    }

    private fun saveCurrentFrame() {
        if (isFrameFrozen) {
            val bitmap = (binding.frozenImageView.drawable as? android.graphics.drawable.BitmapDrawable)?.bitmap
            if (bitmap != null) {
                // 这里可以添加保存逻辑
                Toast.makeText(this, "图像已保存", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun showEnhancementSliders() {
        val slidersContainer = binding.enhancementSlidersContainer
        slidersContainer.visibility = View.VISIBLE

        val brightnessSlider = binding.brightnessSlider
        val contrastSlider = binding.contrastSlider
        val saturationSlider = binding.saturationSlider

        // 设置滑动条监听器
        brightnessSlider.setOnSeekBarChangeListener(createSliderListener())
        contrastSlider.setOnSeekBarChangeListener(createSliderListener())
        saturationSlider.setOnSeekBarChangeListener(createSliderListener())
    }

    private fun hideEnhancementSliders() {
        binding.enhancementSlidersContainer.visibility = View.GONE
    }

    private fun createSliderListener() = object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser && isFrameFrozen) {
                // 简单的颜色调整
                applySimpleColorAdjustment()
            }
        }
        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    }

    private fun applySimpleColorAdjustment() {
        // 简单的颜色矩阵调整
        val brightness = (binding.brightnessSlider.progress - 50) / 50f
        val contrast = binding.contrastSlider.progress / 50f
        val saturation = binding.saturationSlider.progress / 50f
        
        // 应用颜色调整到冻结的图像
        // 这里可以添加简单的颜色矩阵变换
    }

    private fun updateZoomDisplay() {
        binding.zoomLevelText.text = getString(R.string.zoom_level, currentZoomRatio.toInt())
    }

    override fun onSensorChanged(event: SensorEvent?) {
        // 传感器事件处理
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // 传感器精度变化处理
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraExecutor.shutdown()
        if (::zoomController.isInitialized) {
            zoomController.cleanup()
        }
        if (::gestureHandler.isInitialized) {
            gestureHandler.cleanup()
        }
        if (::brightnessController.isInitialized) {
            brightnessController.cleanup()
        }
    }
}
