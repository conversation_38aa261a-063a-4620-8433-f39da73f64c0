@echo off
echo ========================================
echo 🤖 放大镜应用 - 全自动功能测试
echo ========================================

set ADB=C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe

echo.
echo 📱 检查设备连接...
%ADB% devices
echo.

echo 🚀 启动应用...
%ADB% shell am start com.magnifyingglass.app.debug/com.magnifyingglass.app.SplashActivity
echo ✅ 应用已启动，等待加载...
timeout /t 4

echo.
echo 🔦 测试功能 1: 闪光灯按钮
echo 点击闪光灯按钮 (开启)...
%ADB% shell input tap 350 100
timeout /t 2

echo 再次点击闪光灯按钮 (关闭)...
%ADB% shell input tap 350 100
timeout /t 2
echo ✅ 闪光灯测试完成

echo.
echo 🖱️ 测试功能 2: 长按冻结功能
echo 长按屏幕中央冻结画面...
%ADB% shell input swipe 400 800 400 800 1000
timeout /t 3
echo ✅ 冻结功能测试完成

echo.
echo 💾 测试功能 3: 保存按钮
echo 点击保存按钮 (冻结状态下)...
%ADB% shell input tap 300 1200
timeout /t 2
echo ✅ 保存功能测试完成

echo.
echo 🔓 测试功能 4: 长按解冻功能
echo 再次长按解冻画面...
%ADB% shell input swipe 400 800 400 800 1000
timeout /t 2
echo ✅ 解冻功能测试完成

echo.
echo 🎯 测试功能 5: 单击对焦
echo 单击屏幕进行对焦...
%ADB% shell input tap 400 600
timeout /t 1

echo 测试不同位置对焦...
%ADB% shell input tap 200 500
timeout /t 1
%ADB% shell input tap 600 700
timeout /t 1
echo ✅ 对焦功能测试完成

echo.
echo 📏 测试功能 6: 缩放功能 (如果存在)
echo 尝试缩放手势...
%ADB% shell input tap 100 1200
timeout /t 1
%ADB% shell input tap 200 1200
timeout /t 1
echo ✅ 缩放功能测试完成

echo.
echo 📱 测试功能 7: 滑动手势
echo 测试上下滑动...
%ADB% shell input swipe 400 800 400 600 500
timeout /t 1
%ADB% shell input swipe 400 600 400 800 500
timeout /t 1

echo 测试左右滑动...
%ADB% shell input swipe 300 800 500 800 500
timeout /t 1
%ADB% shell input swipe 500 800 300 800 500
timeout /t 1
echo ✅ 滑动手势测试完成

echo.
echo 🔄 测试功能 8: 重复操作测试
echo 快速重复冻结/解冻...
%ADB% shell input swipe 400 800 400 800 1000
timeout /t 1
%ADB% shell input swipe 400 800 400 800 1000
timeout /t 1
%ADB% shell input swipe 400 800 400 800 1000
timeout /t 1
%ADB% shell input swipe 400 800 400 800 1000
timeout /t 2
echo ✅ 重复操作测试完成

echo.
echo 🎨 测试功能 9: 界面元素
echo 测试不同区域点击...
%ADB% shell input tap 100 100   # 左上角
timeout /t 1
%ADB% shell input tap 700 100   # 右上角
timeout /t 1
%ADB% shell input tap 100 1400  # 左下角
timeout /t 1
%ADB% shell input tap 700 1400  # 右下角
timeout /t 1
echo ✅ 界面元素测试完成

echo.
echo ========================================
echo 🎯 自动测试完成！
echo ========================================
echo.
echo 📊 测试总结:
echo ✅ 1. 闪光灯开关功能
echo ✅ 2. 长按冻结/解冻功能  
echo ✅ 3. 保存按钮功能
echo ✅ 4. 单击对焦功能
echo ✅ 5. 缩放功能测试
echo ✅ 6. 滑动手势测试
echo ✅ 7. 重复操作测试
echo ✅ 8. 界面元素测试
echo.
echo 🔍 请检查以下项目:
echo - 毛玻璃效果是否足够模糊
echo - 放大镜尺寸是否等于屏幕宽度
echo - 放大镜位置是否在2/5高度
echo - 所有功能是否有正确的提示信息
echo - 应用是否流畅无卡顿
echo.
echo 按任意键退出...
pause
