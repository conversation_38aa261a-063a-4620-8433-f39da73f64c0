plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.magnifyingglass.app'
    compileSdk 34

    // signingConfigs {
    //     debug {
    //         storeFile file('debug.keystore')
    //         storePassword 'android'
    //         keyAlias 'androiddebugkey'
    //         keyPassword 'android'
    //     }
    //     release {
    //         // 发布签名配置（需要创建release.keystore）
    //         storeFile file('release.keystore')
    //         storePassword System.getenv("KEYSTORE_PASSWORD") ?: '404406'
    //         keyAlias System.getenv("KEY_ALIAS") ?: 'your_key_alias'
    //         keyPassword System.getenv("KEY_PASSWORD") ?: '404406'
    //     }
    // }

    defaultConfig {
        applicationId "com.magnifyingglass.app"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }

        // externalNativeBuild {
        //     cmake {
        //         cppFlags "-std=c++14"
        //         abiFilters 'arm64-v8a', 'armeabi-v7a'
        //     }
        // }
    }

    buildTypes {
        release {
            // signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // 优化配置
            debuggable false
            jniDebuggable false
            zipAlignEnabled true
        }
        debug {
            // signingConfig signingConfigs.debug
            minifyEnabled false
            debuggable true
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    kotlinOptions {
        jvmTarget = '1.8'
    }
    
    buildFeatures {
        viewBinding true
    }

    // externalNativeBuild {
    //     cmake {
    //         path "src/main/cpp/CMakeLists.txt"
    //         version "3.18.1"
    //     }
    // }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    
    // CameraX dependencies
    implementation 'androidx.camera:camera-core:1.3.0'
    implementation 'androidx.camera:camera-camera2:1.3.0'
    implementation 'androidx.camera:camera-lifecycle:1.3.0'
    implementation 'androidx.camera:camera-view:1.3.0'
    implementation 'androidx.camera:camera-extensions:1.3.0'
    
    // Lifecycle
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    
    // Permissions
    implementation 'androidx.activity:activity-ktx:1.8.2'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'

    // NCNN for Real-ESRGAN (轻量版AI超分辨率)
    // implementation 'com.tencent.ncnn:ncnn:20230816'

    // RealSR-NCNN-Android - 现成的Real-ESRGAN Android库
    // implementation 'com.github.tumuyan:RealSR-NCNN-Android:1.11.3'

    // OpenCV for advanced image stabilization
    // implementation 'org.opencv:opencv-android:4.8.0'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
