<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.magnifyingglass.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="282" endOffset="51"/></Target><Target id="@+id/previewView" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="10" startOffset="4" endLine="17" endOffset="51"/></Target><Target id="@+id/magnifierOverlay" view="com.magnifyingglass.app.views.MagnifierOverlayView"><Expressions/><location startLine="20" startOffset="4" endLine="27" endOffset="51"/></Target><Target id="@+id/frozenImageView" view="ImageView"><Expressions/><location startLine="30" startOffset="4" endLine="39" endOffset="51"/></Target><Target id="@+id/topInfoLayout" view="LinearLayout"><Expressions/><location startLine="42" startOffset="4" endLine="78" endOffset="18"/></Target><Target id="@+id/zoomLevelText" view="TextView"><Expressions/><location startLine="53" startOffset="8" endLine="59" endOffset="38"/></Target><Target id="@+id/brightnessText" view="TextView"><Expressions/><location startLine="67" startOffset="8" endLine="74" endOffset="39"/></Target><Target id="@+id/bottomControlLayout" view="LinearLayout"><Expressions/><location startLine="81" startOffset="4" endLine="127" endOffset="18"/></Target><Target id="@+id/zoomOutButton" view="ImageButton"><Expressions/><location startLine="93" startOffset="8" endLine="97" endOffset="49"/></Target><Target id="@+id/flashlightButton" view="ImageButton"><Expressions/><location startLine="100" startOffset="8" endLine="104" endOffset="51"/></Target><Target id="@+id/freezeButton" view="ImageButton"><Expressions/><location startLine="107" startOffset="8" endLine="111" endOffset="47"/></Target><Target id="@+id/modeButton" view="ImageButton"><Expressions/><location startLine="114" startOffset="8" endLine="118" endOffset="45"/></Target><Target id="@+id/zoomInButton" view="ImageButton"><Expressions/><location startLine="121" startOffset="8" endLine="125" endOffset="48"/></Target><Target id="@+id/enhancementSlidersContainer" view="LinearLayout"><Expressions/><location startLine="130" startOffset="4" endLine="223" endOffset="18"/></Target><Target id="@+id/brightnessSlider" view="SeekBar"><Expressions/><location startLine="158" startOffset="12" endLine="166" endOffset="45"/></Target><Target id="@+id/contrastSlider" view="SeekBar"><Expressions/><location startLine="185" startOffset="12" endLine="193" endOffset="45"/></Target><Target id="@+id/saturationSlider" view="SeekBar"><Expressions/><location startLine="211" startOffset="12" endLine="219" endOffset="45"/></Target><Target id="@+id/frozenButtonsContainer" view="LinearLayout"><Expressions/><location startLine="226" startOffset="4" endLine="267" endOffset="18"/></Target><Target id="@+id/backButton" view="ImageButton"><Expressions/><location startLine="238" startOffset="8" endLine="243" endOffset="45"/></Target><Target id="@+id/saveButton" view="ImageButton"><Expressions/><location startLine="246" startOffset="8" endLine="250" endOffset="45"/></Target><Target id="@+id/enhanceButton" view="ImageButton"><Expressions/><location startLine="253" startOffset="8" endLine="258" endOffset="48"/></Target><Target id="@+id/realESRGANButton" view="ImageButton"><Expressions/><location startLine="261" startOffset="8" endLine="266" endOffset="57"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="270" startOffset="4" endLine="280" endOffset="55"/></Target></Targets></Layout>