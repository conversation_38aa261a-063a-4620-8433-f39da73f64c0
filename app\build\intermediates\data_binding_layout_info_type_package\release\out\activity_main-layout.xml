<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.magnifyingglass.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="153" endOffset="51"/></Target><Target id="@+id/previewView" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="10" startOffset="4" endLine="17" endOffset="51"/></Target><Target id="@+id/frozenImageView" view="ImageView"><Expressions/><location startLine="20" startOffset="4" endLine="29" endOffset="51"/></Target><Target id="@+id/topInfoLayout" view="LinearLayout"><Expressions/><location startLine="32" startOffset="4" endLine="77" endOffset="18"/></Target><Target id="@+id/zoomLevelText" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="49" endOffset="38"/></Target><Target id="@+id/brightnessText" view="TextView"><Expressions/><location startLine="57" startOffset="8" endLine="64" endOffset="39"/></Target><Target id="@+id/scenarioHintText" view="TextView"><Expressions/><location startLine="67" startOffset="8" endLine="75" endOffset="37"/></Target><Target id="@+id/bottomControlLayout" view="LinearLayout"><Expressions/><location startLine="80" startOffset="4" endLine="126" endOffset="18"/></Target><Target id="@+id/zoomOutButton" view="ImageButton"><Expressions/><location startLine="92" startOffset="8" endLine="96" endOffset="49"/></Target><Target id="@+id/flashlightButton" view="ImageButton"><Expressions/><location startLine="99" startOffset="8" endLine="103" endOffset="51"/></Target><Target id="@+id/freezeButton" view="ImageButton"><Expressions/><location startLine="106" startOffset="8" endLine="110" endOffset="47"/></Target><Target id="@+id/modeButton" view="ImageButton"><Expressions/><location startLine="113" startOffset="8" endLine="117" endOffset="45"/></Target><Target id="@+id/zoomInButton" view="ImageButton"><Expressions/><location startLine="120" startOffset="8" endLine="124" endOffset="48"/></Target><Target id="@+id/saveButton" view="ImageButton"><Expressions/><location startLine="129" startOffset="4" endLine="138" endOffset="55"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="141" startOffset="4" endLine="151" endOffset="55"/></Target></Targets></Layout>