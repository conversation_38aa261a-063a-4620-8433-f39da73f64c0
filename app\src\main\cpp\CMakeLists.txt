cmake_minimum_required(VERSION 3.18.1)

project("realesrgan")

# 设置C++标准
set(CMAKE_CXX_STANDARD 14)

# 查找必要的库
find_library(log-lib log)
find_library(android-lib android)
find_library(jnigraphics-lib jnigraphics)

# 添加NCNN库
find_package(ncnn REQUIRED)

# 包含头文件目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# 添加源文件
add_library(
    realesrgan
    SHARED
    realesrgan_jni.cpp
    realesrgan_wrapper.cpp
)

# 链接库
target_link_libraries(
    realesrgan
    ${log-lib}
    ${android-lib}
    ${jnigraphics-lib}
    ncnn
)
