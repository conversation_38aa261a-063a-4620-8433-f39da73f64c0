http://schemas.android.com/apk/res-auto;;${\:app*release*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_flashlight.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_mode.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_camera.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_freeze.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_save.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/control_button_background.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/splash_background.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_app_icon.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_settings.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_zoom_out.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/focus_indicator.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_zoom_in.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/brightness_indicator_background.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/dialog_permission.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/onboarding_page.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/activity_onboarding.xml,${\:app*release*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*release*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*release*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*sourceProvider*0*resDir*0}/values/themes_splash.xml,${\:app*release*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:overlay_light,0,V"#80FFFFFF";control_icon,0,V"#FFFFFFFF";overlay_dark,0,V"#80000000";black,0,V"#FF000000";active,0,V"#FF4CAF50";control_background_pressed,0,V"#B0000000";control_text,0,V"#FFFFFFFF";error,0,V"#FFF44336";accent,0,V"#FFFF5722";transparent,0,V"#00000000";high_contrast_bg,0,V"#FF000000";inactive,0,V"#FF9E9E9E";white,0,V"#FFFFFFFF";high_contrast_fg,0,V"#FFFFFFFF";control_background,0,V"#80000000";warning,0,V"#FFFF9800";primary_dark,0,V"#FF1976D2";primary,0,V"#FF2196F3";+dimen:control_button_size,1,V"64dp";small_control_button_size,1,V"48dp";focus_indicator_size,1,V"60dp";+drawable:ic_flashlight,2,F;ic_mode,3,F;ic_camera,4,F;ic_freeze,5,F;ic_save,6,F;control_button_background,7,F;splash_background,8,F;ic_app_icon,9,F;ic_launcher_foreground,10,F;ic_settings,11,F;ic_zoom_out,12,F;ic_launcher_background,13,F;focus_indicator,14,F;ic_zoom_in,15,F;brightness_indicator_background,16,F;+id:permissionMessage,17,F;zoomOutButton,18,F;previewView,18,F;bottomControlLayout,18,F;titleTextView,19,F;flashlightButton,18,F;cancelButton,17,F;brightnessText,18,F;saveButton,18,F;buttonLayout,20,F;zoomInButton,18,F;zoomLevelText,18,F;indicatorLayout,20,F;iconImageView,19,F;skipButton,20,F;descriptionTextView,19,F;scenarioHintText,18,F;permissionIcon,17,F;frozenImageView,18,F;freezeButton,18,F;grantButton,17,F;nextButton,20,F;permissionTitle,17,F;topInfoLayout,18,F;modeButton,18,F;statusText,18,F;viewPager,20,F;+layout:onboarding_page,19,F;activity_main,18,F;dialog_permission,17,F;activity_onboarding,20,F;+mipmap:ic_launcher_round,21,F;ic_launcher,22,F;+string:scenario_package,23,V"📦 看清快递单上的小字";permission_storage_title,23,V"需要存储权限";high_contrast_mode,23,V"高对比度";permission_camera_title,23,V"需要相机权限";focus_failed,23,V"对焦失败";permission_grant,23,V"授予权限";zoom_level,23,V"%dx";onboarding_start,23,V"开始使用";enable_max_brightness,23,V"开启";onboarding_skip,23,V"跳过";low_light_detected,23,V"检测到弱光环境，是否开启最大亮度？";frame_frozen,23,V"画面已冻结";low_light_mode,23,V"低光模式";normal_mode,23,V"普通模式";scenario_inspection,23,V"🕵️‍♂️ 检查商品细节瑕疵";zoom_out,23,V"缩小";flashlight,23,V"手电筒";zoom_in,23,V"放大";onboarding_title_3,23,V"调节亮度";camera_not_available,23,V"相机不可用";settings,23,V"设置";flashlight_on,23,V"手电筒已开启";mode_switch,23,V"模式";permission_camera_message,23,V"放大镜需要使用相机来显示放大画面，请授予相机权限。";permission_denied,23,V"权限被拒绝";onboarding_desc_2,23,V"长按屏幕可以冻结当前画面，方便仔细观看";image_save_failed,23,V"保存失败";onboarding_desc_3,23,V"在屏幕边缘上下滑动可以调节画面亮度";onboarding_next,23,V"下一步";freeze_frame,23,V"冻结";reading_mode,23,V"阅读模式";onboarding_desc_1,23,V"双指捏合或双击屏幕可以调节放大倍数";dismiss,23,V"忽略";permission_cancel,23,V"取消";camera_error,23,V"相机初始化失败";onboarding_title_1,23,V"欢迎使用放大镜";image_saved,23,V"图片已保存到相册";frame_unfrozen,23,V"恢复实时画面";onboarding_title_2,23,V"冻结画面";app_name,23,V"放大镜";brightness_level,23,V"亮度\: %d%%";permission_storage_message,23,V"保存截图需要存储权限，请授予存储权限。";scenario_medicine,23,V"💊 阅读药品说明书";save_image,23,V"保存";flashlight_off,23,V"手电筒已关闭";+style:SmallControlButton,24,VDControlButton,android\:layout_width:48dp,android\:layout_height:48dp,android\:padding:10dp,android\:layout_margin:6dp,;ControlButton,24,VNandroid\:layout_width:56dp,android\:layout_height:56dp,android\:background:@drawable/control_button_background,android\:scaleType:centerInside,android\:padding:12dp,android\:layout_margin:8dp,android\:tint:@color/control_icon,;InfoText,24,VNandroid\:textColor:@color/control_text,android\:textSize:16sp,android\:background:@color/control_background,android\:padding:8dp,android\:layout_margin:4dp,;SplashTheme,25,VDTheme.Material3.DayNight.NoActionBar,android\:windowBackground:@drawable/splash_background,android\:windowNoTitle:true,android\:windowFullscreen:true,android\:windowContentOverlay:@null,android\:windowIsTranslucent:false,;Theme.MagnifyingGlass,24,VDTheme.Material3.DayNight,colorPrimary:@color/primary,colorPrimaryVariant:@color/primary_dark,colorOnPrimary:@color/white,colorSecondary:@color/accent,colorOnSecondary:@color/white,android\:statusBarColor:@color/black,android\:navigationBarColor:@color/black,;OnboardingTitle,24,VNandroid\:textSize:24sp,android\:textStyle:bold,android\:textColor:@color/primary,android\:gravity:center,android\:layout_marginBottom:16dp,;OnboardingDescription,24,VNandroid\:textSize:16sp,android\:textColor:@color/black,android\:gravity:center,android\:lineSpacingExtra:4dp,;Theme.MagnifyingGlass.NoActionBar,24,VNwindowActionBar:false,windowNoTitle:true,android\:windowFullscreen:true,android\:windowContentOverlay:@null,;+xml:data_extraction_rules,26,F;backup_rules,27,F;