<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".MainActivity">

    <!-- 相机预览 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/previewView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 放大镜遮罩层 -->
    <com.magnifyingglass.app.views.MagnifierOverlayView
        android:id="@+id/magnifierOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 冻结画面显示 -->
    <ImageView
        android:id="@+id/frozenImageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 顶部信息栏 -->
    <LinearLayout
        android:id="@+id/topInfoLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 放大倍数显示 -->
        <TextView
            android:id="@+id/zoomLevelText"
            style="@style/InfoText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1x"
            android:textStyle="bold" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <!-- 亮度指示 -->
        <TextView
            android:id="@+id/brightnessText"
            style="@style/InfoText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/brightness_indicator_background"
            android:text="100%"
            android:visibility="gone" />



    </LinearLayout>

    <!-- 底部控制栏 -->
    <LinearLayout
        android:id="@+id/bottomControlLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 缩小按钮 -->
        <ImageButton
            android:id="@+id/zoomOutButton"
            style="@style/SmallControlButton"
            android:contentDescription="@string/zoom_out"
            android:src="@drawable/ic_zoom_out" />

        <!-- 手电筒按钮 -->
        <ImageButton
            android:id="@+id/flashlightButton"
            style="@style/SmallControlButton"
            android:contentDescription="@string/flashlight"
            android:src="@drawable/ic_flashlight" />

        <!-- 冻结/拍照按钮 -->
        <ImageButton
            android:id="@+id/freezeButton"
            style="@style/ControlButton"
            android:contentDescription="@string/freeze_frame"
            android:src="@drawable/ic_freeze" />

        <!-- 模式切换按钮 -->
        <ImageButton
            android:id="@+id/modeButton"
            style="@style/SmallControlButton"
            android:contentDescription="@string/mode_switch"
            android:src="@drawable/ic_mode" />

        <!-- 放大按钮 -->
        <ImageButton
            android:id="@+id/zoomInButton"
            style="@style/SmallControlButton"
            android:contentDescription="@string/zoom_in"
            android:src="@drawable/ic_zoom_in" />

    </LinearLayout>

    <!-- 实时滑动条容器 -->
    <LinearLayout
        android:id="@+id/enhancementSlidersContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@drawable/sliders_background"
        android:visibility="gone"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@id/frozenButtonsContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 亮度滑动条 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_brightness"
                android:layout_marginEnd="12dp"
                android:alpha="0.8" />

            <SeekBar
                android:id="@+id/brightnessSlider"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="100"
                android:progress="50"
                android:progressTint="#FFFFFF"
                android:thumbTint="#FFFFFF" />

        </LinearLayout>

        <!-- 对比度滑动条 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_contrast"
                android:layout_marginEnd="12dp"
                android:alpha="0.8" />

            <SeekBar
                android:id="@+id/contrastSlider"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="100"
                android:progress="50"
                android:progressTint="#FFFFFF"
                android:thumbTint="#FFFFFF" />

        </LinearLayout>

        <!-- 饱和度滑动条 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_saturation"
                android:layout_marginEnd="12dp"
                android:alpha="0.8" />

            <SeekBar
                android:id="@+id/saturationSlider"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="100"
                android:progress="50"
                android:progressTint="#FFFFFF"
                android:thumbTint="#FFFFFF" />

        </LinearLayout>

    </LinearLayout>

    <!-- 冻结状态下的按钮容器 -->
    <LinearLayout
        android:id="@+id/frozenButtonsContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/bottomControlLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 返回按钮 -->
        <ImageButton
            android:id="@+id/backButton"
            style="@style/ControlButton"
            android:layout_marginEnd="16dp"
            android:contentDescription="@string/back"
            android:src="@drawable/ic_back" />

        <!-- 保存按钮 -->
        <ImageButton
            android:id="@+id/saveButton"
            style="@style/ControlButton"
            android:contentDescription="@string/save_image"
            android:src="@drawable/ic_save" />

        <!-- 增强按钮 -->
        <ImageButton
            android:id="@+id/enhanceButton"
            style="@style/ControlButton"
            android:layout_marginStart="16dp"
            android:contentDescription="@string/enhance_image"
            android:src="@drawable/ic_enhance" />

        <!-- Real-ESRGAN超分辨率按钮 -->
        <ImageButton
            android:id="@+id/realESRGANButton"
            style="@style/ControlButton"
            android:layout_marginStart="16dp"
            android:contentDescription="Real-ESRGAN超分辨率增强"
            android:src="@drawable/ic_magnifying_glass" />
    </LinearLayout>

    <!-- 状态提示文本 -->
    <TextView
        android:id="@+id/statusText"
        style="@style/InfoText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/frame_frozen"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/saveButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
